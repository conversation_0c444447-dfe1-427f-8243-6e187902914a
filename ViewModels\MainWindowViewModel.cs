using AccountingSystem11.Services;
using System.Windows.Input;
using AccountingSystem11.Models;
using System.Threading.Tasks;

namespace AccountingSystem11.ViewModels
{
    /// <summary>
    /// Main window view model
    /// </summary>
    public class MainWindowViewModel : BaseViewModel
    {
        private readonly IUserService _userService;
        private readonly IReportService _reportService;
        private readonly AccessDataService _accessDataService;

        public MainWindowViewModel(IUserService userService, IReportService reportService, AccessDataService accessDataService)
        {
            _userService = userService;
            _reportService = reportService;
            _accessDataService = accessDataService;

            // Initialize commands
            NavigateToDashboardCommand = new RelayCommand(NavigateToDashboard);
            NavigateToInvoicesCommand = new RelayCommand(NavigateToInvoices);
            NavigateToCustomersCommand = new RelayCommand(NavigateToCustomers);
            NavigateToProductsCommand = new RelayCommand(NavigateToProducts);
            NavigateToInventoryCommand = new RelayCommand(NavigateToInventory);
            NavigateToReportsCommand = new RelayCommand(NavigateToReports);
            NavigateToSettingsCommand = new RelayCommand(NavigateToSettings);
            LogoutCommand = new RelayCommand(Logout);

            // Set default view - استخدام AccessDashboardViewModel
            CurrentViewModel = new AccessDashboardViewModel(_accessDataService);
            SelectedMenuItem = "Dashboard";
        }

        #region Properties

        private BaseViewModel _currentViewModel;
        public BaseViewModel CurrentViewModel
        {
            get => _currentViewModel;
            set => SetProperty(ref _currentViewModel, value);
        }

        private string _selectedMenuItem;
        public string SelectedMenuItem
        {
            get => _selectedMenuItem;
            set => SetProperty(ref _selectedMenuItem, value);
        }

        private User _currentUser;
        public User CurrentUser
        {
            get => _currentUser;
            set => SetProperty(ref _currentUser, value);
        }

        private string _windowTitle = "نظام المحاسبة 11";
        public string WindowTitle
        {
            get => _windowTitle;
            set => SetProperty(ref _windowTitle, value);
        }

        private bool _isMenuExpanded = true;
        public bool IsMenuExpanded
        {
            get => _isMenuExpanded;
            set => SetProperty(ref _isMenuExpanded, value);
        }

        #endregion

        #region Commands

        public ICommand NavigateToDashboardCommand { get; }
        public ICommand NavigateToInvoicesCommand { get; }
        public ICommand NavigateToCustomersCommand { get; }
        public ICommand NavigateToProductsCommand { get; }
        public ICommand NavigateToInventoryCommand { get; }
        public ICommand NavigateToReportsCommand { get; }
        public ICommand NavigateToSettingsCommand { get; }
        public ICommand LogoutCommand { get; }

        #endregion

        #region Navigation Methods

        private void NavigateToDashboard()
        {
            try
            {
                CurrentViewModel = new AccessDashboardViewModel(_accessDataService);
                SelectedMenuItem = "Dashboard";
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في فتح لوحة التحكم: {ex.Message}");
            }
        }

        private void NavigateToInvoices()
        {
            // تبسيط مؤقت - إزالة فحص الصلاحيات
            try
            {
                CurrentViewModel = new InvoiceViewModel(App.GetService<IInvoiceService>(),
                    App.GetService<ICustomerService>(), App.GetService<IProductService>());
                SelectedMenuItem = "Invoices";
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في فتح الفواتير: {ex.Message}");
            }
        }

        private void NavigateToCustomers()
        {
            try
            {
                CurrentViewModel = new CustomerViewModel(App.GetService<ICustomerService>());
                SelectedMenuItem = "Customers";
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في فتح العملاء: {ex.Message}");
            }
        }

        private void NavigateToProducts()
        {
            try
            {
                CurrentViewModel = new ProductViewModel(App.GetService<IProductService>());
                SelectedMenuItem = "Products";
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في فتح المنتجات: {ex.Message}");
            }
        }

        private void NavigateToInventory()
        {
            try
            {
                CurrentViewModel = new InventoryViewModel(App.GetService<IInventoryService>());
                SelectedMenuItem = "Inventory";
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في فتح المخزون: {ex.Message}");
            }
        }

        private void NavigateToReports()
        {
            try
            {
                CurrentViewModel = new ReportViewModel(_reportService);
                SelectedMenuItem = "Reports";
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في فتح التقارير: {ex.Message}");
            }
        }

        private void NavigateToSettings()
        {
            try
            {
                CurrentViewModel = new SettingsViewModel(App.GetService<IUserService>(),
                    App.GetService<ITaxService>());
                SelectedMenuItem = "Settings";
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في فتح الإعدادات: {ex.Message}");
            }
        }

        private void Logout()
        {
            // Implement logout logic
            CurrentUser = null;
            // Navigate to login window
        }

        #endregion

        #region Public Methods

        public async Task InitializeAsync(User user)
        {
            CurrentUser = user;
            WindowTitle = $"نظام المحاسبة 11 - {user.FullName}";

            // Load dashboard data
            await LoadDashboardAsync();
        }

        private async Task LoadDashboardAsync()
        {
            if (CurrentViewModel is DashboardViewModel dashboardViewModel)
            {
                await dashboardViewModel.LoadDataAsync();
            }
        }

        #endregion
    }
}
