<Window x:Class="AccountingSystem11.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:viewModels="clr-namespace:AccountingSystem11.ViewModels"
        xmlns:local="clr-namespace:AccountingSystem11.Views"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        Title="{Binding WindowTitle}" 
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        Background="{StaticResource BackgroundBrush}">

    <Window.DataContext>
        <viewModels:MainWindowViewModel/>
    </Window.DataContext>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Navigation Menu -->
        <Border Grid.Column="0" 
                Background="{StaticResource PrimaryBrush}"
                Width="{Binding IsMenuExpanded, Converter={StaticResource BooleanToWidthConverter}, ConverterParameter='250,60'}"
                MinWidth="60">
            
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Header -->
                <StackPanel Grid.Row="0" Margin="16,20">
                    <TextBlock Text="نظام المحاسبة" 
                               FontSize="18" 
                               FontWeight="Bold" 
                               Foreground="White"
                               HorizontalAlignment="Center"
                               Visibility="{Binding IsMenuExpanded, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                    <TextBlock Text="11" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               Foreground="{StaticResource AccentBrush}"
                               HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Menu Items -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="8,16">
                        
                        <!-- Dashboard -->
                        <Button Style="{StaticResource MenuButtonStyle}"
                                Command="{Binding NavigateToDashboardCommand}"
                                IsEnabled="{Binding CurrentUser.HasPermission, ConverterParameter='ViewDashboard'}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ViewDashboard" Width="24" Height="24" Margin="0,0,12,0"/>
                                <TextBlock Text="لوحة التحكم" 
                                           Visibility="{Binding IsMenuExpanded, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                            </StackPanel>
                        </Button>

                        <!-- Invoices -->
                        <Button Style="{StaticResource MenuButtonStyle}"
                                Command="{Binding NavigateToInvoicesCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Receipt" Width="24" Height="24" Margin="0,0,12,0"/>
                                <TextBlock Text="الفواتير" 
                                           Visibility="{Binding IsMenuExpanded, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                            </StackPanel>
                        </Button>

                        <!-- Customers -->
                        <Button Style="{StaticResource MenuButtonStyle}"
                                Command="{Binding NavigateToCustomersCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AccountGroup" Width="24" Height="24" Margin="0,0,12,0"/>
                                <TextBlock Text="العملاء" 
                                           Visibility="{Binding IsMenuExpanded, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                            </StackPanel>
                        </Button>

                        <!-- Products -->
                        <Button Style="{StaticResource MenuButtonStyle}"
                                Command="{Binding NavigateToProductsCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Package" Width="24" Height="24" Margin="0,0,12,0"/>
                                <TextBlock Text="المنتجات" 
                                           Visibility="{Binding IsMenuExpanded, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                            </StackPanel>
                        </Button>

                        <!-- Inventory -->
                        <Button Style="{StaticResource MenuButtonStyle}"
                                Command="{Binding NavigateToInventoryCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Warehouse" Width="24" Height="24" Margin="0,0,12,0"/>
                                <TextBlock Text="المخزون" 
                                           Visibility="{Binding IsMenuExpanded, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                            </StackPanel>
                        </Button>

                        <!-- Reports -->
                        <Button Style="{StaticResource MenuButtonStyle}"
                                Command="{Binding NavigateToReportsCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ChartLine" Width="24" Height="24" Margin="0,0,12,0"/>
                                <TextBlock Text="التقارير" 
                                           Visibility="{Binding IsMenuExpanded, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                            </StackPanel>
                        </Button>

                        <!-- Settings -->
                        <Button Style="{StaticResource MenuButtonStyle}"
                                Command="{Binding NavigateToSettingsCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Settings" Width="24" Height="24" Margin="0,0,12,0"/>
                                <TextBlock Text="الإعدادات" 
                                           Visibility="{Binding IsMenuExpanded, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                            </StackPanel>
                        </Button>

                    </StackPanel>
                </ScrollViewer>

                <!-- Footer -->
                <StackPanel Grid.Row="2" Margin="8,16">
                    
                    <!-- User Info -->
                    <Border Background="White" 
                            CornerRadius="4" 
                            Padding="8"
                            Margin="0,0,0,8"
                            Visibility="{Binding IsMenuExpanded, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel>
                            <TextBlock Text="{Binding CurrentUser.FullName}" 
                                       FontWeight="SemiBold"
                                       Foreground="{StaticResource TextPrimaryBrush}"/>
                            <TextBlock Text="{Binding CurrentUser.Role}" 
                                       FontSize="12"
                                       Foreground="{StaticResource TextSecondaryBrush}"/>
                        </StackPanel>
                    </Border>

                    <!-- Menu Toggle -->
                    <Button Style="{StaticResource MenuButtonStyle}"
                            Command="{Binding ToggleMenuCommand}">
                        <materialDesign:PackIcon Kind="Menu" Width="24" Height="24"/>
                    </Button>

                    <!-- Logout -->
                    <Button Style="{StaticResource MenuButtonStyle}"
                            Command="{Binding LogoutCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Logout" Width="24" Height="24" Margin="0,0,12,0"/>
                            <TextBlock Text="تسجيل الخروج" 
                                       Visibility="{Binding IsMenuExpanded, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                        </StackPanel>
                    </Button>

                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content Area -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Top Bar -->
            <Border Grid.Row="0" 
                    Background="{StaticResource SurfaceBrush}"
                    BorderBrush="{StaticResource PrimaryBrush}"
                    BorderThickness="0,0,0,1"
                    Padding="16,12">
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Page Title -->
                    <TextBlock Grid.Column="0"
                               Text="{Binding SelectedMenuItem}"
                               Style="{StaticResource PageTitleStyle}"/>

                    <!-- Quick Actions -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        
                        <!-- Notifications -->
                        <Button Style="{StaticResource SecondaryButtonStyle}"
                                Margin="4,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Bell" Width="16" Height="16" Margin="0,0,4,0"/>
                                <TextBlock Text="الإشعارات"/>
                            </StackPanel>
                        </Button>

                        <!-- Quick Invoice -->
                        <Button Style="{StaticResource PrimaryButtonStyle}"
                                Command="{Binding QuickInvoiceCommand}"
                                Margin="4,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,4,0"/>
                                <TextBlock Text="فاتورة جديدة"/>
                            </StackPanel>
                        </Button>

                    </StackPanel>
                </Grid>
            </Border>

            <!-- Content Area -->
            <ContentPresenter Grid.Row="1"
                              Content="{Binding CurrentViewModel}"
                              Margin="16">
                <ContentPresenter.Resources>
                    <!-- DataTemplates for ViewModels -->
                    <DataTemplate DataType="{x:Type viewModels:DashboardViewModel}">
                        <local:DashboardView/>
                    </DataTemplate>
                </ContentPresenter.Resources>
            </ContentPresenter>

            <!-- Status Bar -->
            <Border Grid.Row="2" 
                    Background="{StaticResource SurfaceBrush}"
                    BorderBrush="{StaticResource PrimaryBrush}"
                    BorderThickness="0,1,0,0"
                    Padding="16,8">
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Status Messages -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <TextBlock Text="{Binding StatusMessage}" 
                                   Foreground="{StaticResource TextSecondaryBrush}"/>
                    </StackPanel>

                    <!-- System Info -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <TextBlock Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat='yyyy/MM/dd HH:mm'}" 
                                   Foreground="{StaticResource TextSecondaryBrush}"
                                   Margin="0,0,16,0"/>
                        <TextBlock Text="نظام المحاسبة 11 - الإصدار 1.0" 
                                   Foreground="{StaticResource TextSecondaryBrush}"/>
                    </StackPanel>
                </Grid>
            </Border>

        </Grid>

        <!-- Loading Overlay -->
        <Grid Grid.ColumnSpan="2" 
              Background="#80000000"
              Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}">
            
            <Border Background="{StaticResource SurfaceBrush}"
                    CornerRadius="8"
                    Padding="32"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center">
                
                <StackPanel>
                    <ProgressBar IsIndeterminate="True" 
                                 Width="200" 
                                 Height="4"
                                 Margin="0,0,0,16"/>
                    <TextBlock Text="{Binding BusyMessage}" 
                               HorizontalAlignment="Center"
                               FontSize="14"/>
                </StackPanel>
            </Border>
        </Grid>

    </Grid>
</Window>
