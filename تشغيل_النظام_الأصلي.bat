@echo off
title نظام المحاسبة 11 - النظام الأصلي
color 0A
echo.
echo ========================================================
echo        نظام المحاسبة 11 - النظام الأصلي
echo ========================================================
echo.

echo 🏢 النظام الأصلي - كما كان في البداية!
echo ✅ قاعدة بيانات Microsoft Access
echo 🗃️ جميع المميزات الأصلية
echo 🚀 واجهة WPF كاملة
echo.

echo [1] التحقق من .NET...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET غير مثبت
    echo 📥 جاري فتح رابط التحميل...
    start https://dotnet.microsoft.com/download/dotnet/6.0
    echo.
    echo بعد تثبيت .NET، أعد تشغيل هذا الملف
    pause
    exit /b 1
)
echo ✅ .NET مثبت - الإصدار:
dotnet --version

echo.
echo [2] التحقق من Access Database Engine...
reg query "HKLM\SOFTWARE\Microsoft\Office\ClickToRun\REGISTRY\MACHINE\Software\Classes\CLSID\{3BE786A4-0091-4C40-90E1-9A0CC37EADB4}" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Access Database Engine مثبت (Office 365)
) else (
    reg query "HKLM\SOFTWARE\Classes\CLSID\{3BE786A4-0091-4C40-90E1-9A0CC37EADB4}" >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ Access Database Engine مثبت
    ) else (
        echo ❌ Access Database Engine غير مثبت
        echo 📥 جاري فتح رابط التحميل...
        start https://www.microsoft.com/en-us/download/details.aspx?id=54920
        echo.
        echo 📋 تعليمات التثبيت:
        echo    1. حمل "AccessDatabaseEngine_X64.exe"
        echo    2. ثبته
        echo    3. أعد تشغيل الجهاز
        echo    4. أعد تشغيل هذا الملف
        echo.
        pause
        exit /b 1
    )
)

echo.
echo [3] تنظيف المشروع...
dotnet clean AccountingSystem11.csproj --verbosity minimal >nul 2>&1
echo ✅ تم تنظيف المشروع

echo.
echo [4] استعادة الحزم...
dotnet restore AccountingSystem11.csproj --verbosity minimal >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ فشل في استعادة الحزم
    echo 🔧 جرب تشغيل الملف كـ Administrator
    pause
    exit /b 1
)
echo ✅ تم استعادة الحزم

echo.
echo [5] بناء المشروع...
dotnet build AccountingSystem11.csproj --configuration Release --verbosity minimal >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ فشل البناء
    echo.
    echo 🔧 جرب الحلول التالية:
    echo    1. شغل الملف كـ Administrator
    echo    2. أغلق مضاد الفيروسات مؤقتاً
    echo    3. تأكد من وجود مساحة كافية
    echo.
    echo 📋 تفاصيل أكثر:
    dotnet build AccountingSystem11.csproj --verbosity normal
    pause
    exit /b 1
)
echo ✅ تم بناء المشروع بنجاح

echo.
echo [6] إعداد قاعدة البيانات...
echo 🗃️ جاري إنشاء قاعدة بيانات Access...

REM إنشاء مجلد Data إذا لم يكن موجود
if not exist "Data" mkdir "Data"

echo ✅ تم إعداد مجلد قاعدة البيانات

echo.
echo ========================================================
echo 🎉 جاري تشغيل نظام المحاسبة 11 الأصلي...
echo ========================================================
echo.
echo 🗃️ قاعدة البيانات: Microsoft Access
echo 📁 مكان البيانات: Data\AccountingSystem.accdb
echo.
echo 🔐 بيانات تسجيل الدخول:
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo.
echo 🎯 المميزات الأصلية:
echo    ✅ واجهة WPF كاملة
echo    ✅ إدارة العملاء والموردين
echo    ✅ كتالوج المنتجات والخدمات
echo    ✅ إصدار الفواتير
echo    ✅ تقارير مالية شاملة
echo    ✅ دعم الضرائب المصرية
echo    ✅ الفاتورة الإلكترونية
echo    ✅ إدارة المخزون
echo    ✅ نظام المستخدمين
echo.
echo ⚠️ ملاحظات:
echo    • قاعدة البيانات ستُنشأ تلقائياً عند أول تشغيل
echo    • البيانات محفوظة في ملف Access محلي
echo    • لا تحتاج اتصال إنترنت للعمل
echo    • يمكن عمل نسخة احتياطية بنسخ ملف .accdb
echo.
echo ========================================================

timeout /t 3 /nobreak >nul

echo 🚀 بدء التشغيل...
dotnet run --project AccountingSystem11.csproj --configuration Release

echo.
echo ✅ تم إغلاق البرنامج
echo 💾 البيانات محفوظة في: Data\AccountingSystem.accdb
echo.

REM عرض معلومات قاعدة البيانات
if exist "Data\AccountingSystem.accdb" (
    echo 📊 معلومات قاعدة البيانات:
    for %%A in ("Data\AccountingSystem.accdb") do (
        echo ✅ حجم قاعدة البيانات: %%~zA بايت
        echo ✅ آخر تعديل: %%~tA
    )
) else (
    echo ⚠️  لم يتم إنشاء قاعدة البيانات
    echo تحقق من تثبيت Access Database Engine
)

echo.
pause
