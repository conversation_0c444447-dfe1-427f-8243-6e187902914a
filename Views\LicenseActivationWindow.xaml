<Window x:Class="AccountingSystem11.Views.LicenseActivationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تفعيل الترخيص - نظام المحاسبة 11" 
        Height="700" Width="500"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{materialDesign:MaterialDesignFont}">

    <Grid>
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#FF1976D2" Offset="0"/>
                <GradientStop Color="#FF0D47A1" Offset="1"/>
            </LinearGradientBrush>
        </Grid.Background>

        <!-- بطاقة التفعيل -->
        <materialDesign:Card Margin="30" Padding="32" 
                           VerticalAlignment="Center"
                           materialDesign:ShadowAssist.ShadowDepth="Depth4">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- شعار النظام -->
                    <materialDesign:PackIcon Kind="Certificate" 
                                           Width="80" Height="80"
                                           HorizontalAlignment="Center"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           Margin="0,0,0,16"/>

                    <!-- عنوان النظام -->
                    <TextBlock Text="نظام المحاسبة 11" 
                             Style="{DynamicResource MaterialDesignHeadline4TextBlock}"
                             HorizontalAlignment="Center"
                             Margin="0,0,0,8"/>

                    <TextBlock Text="تفعيل الترخيص التجاري" 
                             Style="{DynamicResource MaterialDesignBody1TextBlock}"
                             HorizontalAlignment="Center"
                             Opacity="0.7"
                             Margin="0,0,0,24"/>

                    <!-- معلومات الترخيص الحالي -->
                    <Border Background="{DynamicResource MaterialDesignSelection}"
                          CornerRadius="8"
                          Padding="16"
                          Margin="0,0,0,24">
                        <StackPanel>
                            <TextBlock x:Name="CurrentLicenseTitle"
                                     Text="🔓 الترخيص الحالي:"
                                     Style="{DynamicResource MaterialDesignSubtitle2TextBlock}"
                                     FontWeight="Bold"
                                     Margin="0,0,0,8"/>
                            
                            <TextBlock x:Name="LicenseTypeText"
                                     Text="نسخة تجريبية"
                                     Style="{DynamicResource MaterialDesignBody1TextBlock}"
                                     Margin="0,0,0,4"/>
                            
                            <TextBlock x:Name="ExpiryDateText"
                                     Text="تنتهي في: 30 يوم"
                                     Style="{DynamicResource MaterialDesignBody2TextBlock}"
                                     Foreground="Orange"
                                     Margin="0,0,0,4"/>
                            
                            <TextBlock x:Name="FeaturesText"
                                     Text="المميزات: أساسية"
                                     Style="{DynamicResource MaterialDesignBody2TextBlock}"
                                     Margin="0,0,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- حقل اسم العميل -->
                    <TextBox x:Name="CustomerNameTextBox"
                            materialDesign:HintAssist.Hint="اسم العميل أو الشركة"
                            materialDesign:HintAssist.IsFloating="True"
                            materialDesign:TextFieldAssist.HasClearButton="True"
                            materialDesign:TextFieldAssist.PrefixText="👤"
                            Style="{DynamicResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,16"
                            FontSize="14"
                            FlowDirection="RightToLeft"/>

                    <!-- حقل البريد الإلكتروني -->
                    <TextBox x:Name="CustomerEmailTextBox"
                            materialDesign:HintAssist.Hint="البريد الإلكتروني"
                            materialDesign:HintAssist.IsFloating="True"
                            materialDesign:TextFieldAssist.HasClearButton="True"
                            materialDesign:TextFieldAssist.PrefixText="📧"
                            Style="{DynamicResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,16"
                            FontSize="14"
                            FlowDirection="RightToLeft"/>

                    <!-- حقل مفتاح الترخيص -->
                    <TextBox x:Name="LicenseKeyTextBox"
                            materialDesign:HintAssist.Hint="مفتاح الترخيص (XXXXX-XXXXX-XXXXX-XXXXX-XXXXX)"
                            materialDesign:HintAssist.IsFloating="True"
                            materialDesign:TextFieldAssist.HasClearButton="True"
                            materialDesign:TextFieldAssist.PrefixText="🔑"
                            Style="{DynamicResource MaterialDesignOutlinedTextBox}"
                            Margin="0,0,0,16"
                            FontSize="14"
                            CharacterCasing="Upper"
                            MaxLength="29"
                            TextChanged="LicenseKeyTextBox_TextChanged"/>

                    <!-- زر التفعيل -->
                    <Button x:Name="ActivateButton"
                          Content="تفعيل الترخيص"
                          Style="{DynamicResource MaterialDesignRaisedButton}"
                          materialDesign:ButtonAssist.CornerRadius="20"
                          Height="45"
                          FontSize="16"
                          FontWeight="Bold"
                          Click="ActivateButton_Click"
                          Margin="0,0,0,16"/>

                    <!-- أزرار إضافية -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,16">
                        <Button x:Name="BuyLicenseButton"
                              Content="شراء ترخيص"
                              Style="{DynamicResource MaterialDesignOutlinedButton}"
                              materialDesign:ButtonAssist.CornerRadius="15"
                              Margin="0,0,8,0"
                              Click="BuyLicenseButton_Click"/>
                        
                        <Button x:Name="ContinueTrialButton"
                              Content="متابعة التجربة"
                              Style="{DynamicResource MaterialDesignFlatButton}"
                              Margin="8,0,0,0"
                              Click="ContinueTrialButton_Click"/>
                    </StackPanel>

                    <!-- معلومات الأسعار -->
                    <Expander Header="📋 خطط الأسعار والمميزات" 
                            Margin="0,0,0,16"
                            materialDesign:ExpanderAssist.HorizontalHeaderPadding="0">
                        <StackPanel Margin="0,8,0,0">
                            <!-- الخطة الأساسية -->
                            <Border Background="#E3F2FD" CornerRadius="8" Padding="12" Margin="0,0,0,8">
                                <StackPanel>
                                    <TextBlock Text="💼 الخطة الأساسية - 1,500 جنيه/سنة"
                                             FontWeight="Bold" Margin="0,0,0,4"/>
                                    <TextBlock Text="• مستخدم واحد فقط" Margin="0,0,0,2"/>
                                    <TextBlock Text="• إدارة العملاء والمنتجات" Margin="0,0,0,2"/>
                                    <TextBlock Text="• الفواتير الأساسية" Margin="0,0,0,2"/>
                                    <TextBlock Text="• تقارير بسيطة" Margin="0,0,0,2"/>
                                </StackPanel>
                            </Border>

                            <!-- الخطة المتقدمة -->
                            <Border Background="#E8F5E8" CornerRadius="8" Padding="12" Margin="0,0,0,8">
                                <StackPanel>
                                    <TextBlock Text="🚀 الخطة المتقدمة - 3,500 جنيه/سنة"
                                             FontWeight="Bold" Margin="0,0,0,4"/>
                                    <TextBlock Text="• حتى 5 مستخدمين" Margin="0,0,0,2"/>
                                    <TextBlock Text="• جميع مميزات الخطة الأساسية" Margin="0,0,0,2"/>
                                    <TextBlock Text="• الفاتورة الإلكترونية" Margin="0,0,0,2"/>
                                    <TextBlock Text="• تقارير متقدمة ورسوم بيانية" Margin="0,0,0,2"/>
                                    <TextBlock Text="• تصدير Excel متقدم" Margin="0,0,0,2"/>
                                </StackPanel>
                            </Border>

                            <!-- خطة المؤسسات -->
                            <Border Background="#FFF3E0" CornerRadius="8" Padding="12" Margin="0,0,0,8">
                                <StackPanel>
                                    <TextBlock Text="🏢 خطة المؤسسات - 7,500 جنيه/سنة"
                                             FontWeight="Bold" Margin="0,0,0,4"/>
                                    <TextBlock Text="• حتى 50 مستخدم" Margin="0,0,0,2"/>
                                    <TextBlock Text="• جميع مميزات الخطة المتقدمة" Margin="0,0,0,2"/>
                                    <TextBlock Text="• مزامنة سحابية" Margin="0,0,0,2"/>
                                    <TextBlock Text="• API للتكامل" Margin="0,0,0,2"/>
                                    <TextBlock Text="• دعم فني مخصص" Margin="0,0,0,2"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Expander>

                    <!-- رسالة الحالة -->
                    <TextBlock x:Name="StatusMessageTextBlock"
                             Style="{DynamicResource MaterialDesignBody2TextBlock}"
                             HorizontalAlignment="Center"
                             Margin="0,16,0,0"
                             Visibility="Collapsed"/>

                    <!-- شريط التحميل -->
                    <ProgressBar x:Name="LoadingProgressBar"
                               Style="{DynamicResource MaterialDesignLinearProgressBar}"
                               IsIndeterminate="True"
                               Margin="0,16,0,0"
                               Visibility="Collapsed"/>

                    <!-- معلومات الاتصال -->
                    <Border Background="{DynamicResource MaterialDesignDivider}"
                          CornerRadius="8"
                          Padding="12"
                          Margin="0,16,0,0">
                        <StackPanel>
                            <TextBlock Text="📞 للمساعدة في الشراء والتفعيل:"
                                     Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                     FontWeight="Bold"
                                     Margin="0,0,0,4"/>
                            <TextBlock Text="📧 البريد: <EMAIL>"
                                     Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                     Margin="0,0,0,2"/>
                            <TextBlock Text="📱 الهاتف: +20 100 123 4567"
                                     Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                     Margin="0,0,0,2"/>
                            <TextBlock Text="🌐 الموقع: www.system11.com"
                                     Style="{DynamicResource MaterialDesignCaptionTextBlock}"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </ScrollViewer>
        </materialDesign:Card>
    </Grid>
</Window>
