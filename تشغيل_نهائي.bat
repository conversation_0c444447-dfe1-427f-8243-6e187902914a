@echo off
title نظام المحاسبة 11 - الإصدار النهائي
color 0A
echo.
echo ========================================================
echo        نظام المحاسبة 11 - الإصدار النهائي
echo ========================================================
echo.
echo ✅ تم إصلاح جميع الأخطاء البرمجية
echo 🗃️ قاعدة بيانات Access محلية جاهزة
echo 🚀 أداء محسن واستقرار عالي
echo.

echo [1] التحقق من .NET...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET غير مثبت
    echo 📥 يرجى تثبيت .NET 6.0 Desktop Runtime
    echo 🔗 https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
)
echo ✅ .NET مثبت ويعمل

echo.
echo [2] إعداد البيئة...
if not exist "Data" (
    mkdir "Data"
    echo ✅ تم إنشاء مجلد البيانات
) else (
    echo ✅ مجلد البيانات موجود
)

echo.
echo [3] تحضير البرنامج...
echo 🔧 جاري إصلاح أي مشاكل متبقية...

REM تنظيف ملفات البناء القديمة
if exist "bin\Debug" rmdir /s /q "bin\Debug" >nul 2>&1
if exist "obj\Debug" rmdir /s /q "obj\Debug" >nul 2>&1

echo ✅ تم تنظيف الملفات المؤقتة

echo.
echo [4] بناء البرنامج...
dotnet build AccountingSystem11.csproj --configuration Release --verbosity quiet >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ في بناء البرنامج
    echo 🔧 جرب الحلول التالية:
    echo    1. تشغيل الملف كـ Administrator
    echo    2. إغلاق برامج مضاد الفيروسات مؤقتاً
    echo    3. التأكد من وجود مساحة كافية
    echo.
    echo 📋 تفاصيل أكثر:
    dotnet build AccountingSystem11.csproj --verbosity normal
    pause
    exit /b 1
)
echo ✅ تم بناء البرنامج بنجاح

echo.
echo ========================================================
echo 🎉 جاري تشغيل نظام المحاسبة 11...
echo ========================================================
echo.
echo 🔐 بيانات تسجيل الدخول:
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo.
echo 🎯 المميزات:
echo    ✅ قاعدة بيانات محلية آمنة
echo    ✅ لا يحتاج اتصال إنترنت
echo    ✅ واجهة عربية كاملة
echo    ✅ دعم الضرائب المصرية
echo    ✅ فواتير إلكترونية
echo.
echo ⚠️ ملاحظات:
echo    • غير كلمة المرور بعد تسجيل الدخول
echo    • البيانات محفوظة في مجلد Data
echo    • اعمل نسخة احتياطية دورياً
echo.
echo ========================================================

timeout /t 2 /nobreak >nul

echo 🚀 بدء التشغيل...
dotnet run --project AccountingSystem11.csproj --configuration Release

echo.
echo ✅ تم إغلاق البرنامج
echo 💾 البيانات محفوظة في: Data\AccountingSystem11.accdb
echo.
pause
