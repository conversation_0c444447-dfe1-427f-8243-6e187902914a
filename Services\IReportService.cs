using AccountingSystem11.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AccountingSystem11.Services
{
    /// <summary>
    /// Interface for reporting operations
    /// </summary>
    public interface IReportService
    {
        // Sales Reports
        Task<SalesReportData> GenerateSalesReportAsync(DateTime startDate, DateTime endDate, int? customerId = null);
        Task<List<TopSellingProduct>> GetTopSellingProductsAsync(DateTime startDate, DateTime endDate, int count = 10);
        Task<List<CustomerSalesReport>> GetCustomerSalesReportAsync(DateTime startDate, DateTime endDate);
        Task<List<DailySalesReport>> GetDailySalesReportAsync(DateTime startDate, DateTime endDate);
        Task<List<MonthlySalesReport>> GetMonthlySalesReportAsync(int year);

        // Financial Reports
        Task<ProfitLossReport> GenerateProfitLossReportAsync(DateTime startDate, DateTime endDate);
        Task<CashFlowReport> GenerateCashFlowReportAsync(DateTime startDate, DateTime endDate);
        Task<BalanceSheetReport> GenerateBalanceSheetReportAsync(DateTime asOfDate);
        Task<AccountsReceivableReport> GenerateAccountsReceivableReportAsync(DateTime? asOfDate = null);
        Task<AccountsPayableReport> GenerateAccountsPayableReportAsync(DateTime? asOfDate = null);

        // Inventory Reports
        Task<InventoryReportData> GenerateInventoryReportAsync(DateTime? asOfDate = null);
        Task<InventoryValuationReport> GenerateInventoryValuationReportAsync(DateTime? asOfDate = null);
        Task<StockMovementReport> GenerateStockMovementReportAsync(DateTime startDate, DateTime endDate, int? productId = null);
        Task<List<LowStockAlert>> GetLowStockAlertsAsync();

        // Tax Reports
        Task<TaxReport> GenerateTaxReportAsync(DateTime startDate, DateTime endDate);
        Task<VatReturnData> GenerateVatReturnAsync(DateTime startDate, DateTime endDate);

        // Dashboard Reports
        Task<DashboardData> GetDashboardDataAsync(DateTime? startDate = null, DateTime? endDate = null);
        Task<List<RecentActivity>> GetRecentActivitiesAsync(int count = 10);
        Task<List<Alert>> GetSystemAlertsAsync();

        // Export Functions
        Task<bool> ExportReportToPdfAsync<T>(T reportData, string templateName, string filePath);
        Task<bool> ExportReportToExcelAsync<T>(T reportData, string filePath);
        Task<bool> EmailReportAsync<T>(T reportData, string templateName, string toEmail, string subject);
    }

    // Report Data Models
    public class TopSellingProduct
    {
        public int ProductId { get; set; }
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public decimal QuantitySold { get; set; }
        public decimal Revenue { get; set; }
        public int OrderCount { get; set; }
    }

    public class CustomerSalesReport
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalTax { get; set; }
        public int InvoiceCount { get; set; }
        public decimal AverageOrderValue { get; set; }
        public DateTime LastOrderDate { get; set; }
    }

    public class DailySalesReport
    {
        public DateTime Date { get; set; }
        public decimal Sales { get; set; }
        public decimal Tax { get; set; }
        public int InvoiceCount { get; set; }
        public int CustomerCount { get; set; }
    }

    public class MonthlySalesReport
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string MonthName { get; set; }
        public decimal Sales { get; set; }
        public decimal Tax { get; set; }
        public int InvoiceCount { get; set; }
        public decimal GrowthPercentage { get; set; }
    }

    public class ProfitLossReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal Revenue { get; set; }
        public decimal CostOfGoodsSold { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal OperatingExpenses { get; set; }
        public decimal NetProfit { get; set; }
        public decimal GrossProfitMargin { get; set; }
        public decimal NetProfitMargin { get; set; }
    }

    public class CashFlowReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal OpeningBalance { get; set; }
        public decimal CashInflows { get; set; }
        public decimal CashOutflows { get; set; }
        public decimal NetCashFlow { get; set; }
        public decimal ClosingBalance { get; set; }
        public List<CashFlowItem> Items { get; set; } = new List<CashFlowItem>();
    }

    public class CashFlowItem
    {
        public DateTime Date { get; set; }
        public string Description { get; set; }
        public decimal Inflow { get; set; }
        public decimal Outflow { get; set; }
        public decimal Balance { get; set; }
    }

    public class BalanceSheetReport
    {
        public DateTime AsOfDate { get; set; }
        public decimal TotalAssets { get; set; }
        public decimal TotalLiabilities { get; set; }
        public decimal TotalEquity { get; set; }
        public List<BalanceSheetItem> Assets { get; set; } = new List<BalanceSheetItem>();
        public List<BalanceSheetItem> Liabilities { get; set; } = new List<BalanceSheetItem>();
        public List<BalanceSheetItem> Equity { get; set; } = new List<BalanceSheetItem>();
    }

    public class BalanceSheetItem
    {
        public string AccountName { get; set; }
        public decimal Amount { get; set; }
        public string Category { get; set; }
    }

    public class AccountsReceivableReport
    {
        public DateTime AsOfDate { get; set; }
        public decimal TotalReceivable { get; set; }
        public decimal Current { get; set; }
        public decimal Days30 { get; set; }
        public decimal Days60 { get; set; }
        public decimal Days90 { get; set; }
        public decimal Over90Days { get; set; }
        public List<AccountsReceivableItem> Items { get; set; } = new List<AccountsReceivableItem>();
    }

    public class AccountsReceivableItem
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal Current { get; set; }
        public decimal Days30 { get; set; }
        public decimal Days60 { get; set; }
        public decimal Days90 { get; set; }
        public decimal Over90Days { get; set; }
        public DateTime OldestInvoiceDate { get; set; }
    }

    public class AccountsPayableReport
    {
        public DateTime AsOfDate { get; set; }
        public decimal TotalPayable { get; set; }
        public List<AccountsPayableItem> Items { get; set; } = new List<AccountsPayableItem>();
    }

    public class AccountsPayableItem
    {
        public int SupplierId { get; set; }
        public string SupplierName { get; set; }
        public decimal Amount { get; set; }
        public DateTime DueDate { get; set; }
        public int DaysOverdue { get; set; }
    }

    public class LowStockAlert
    {
        public int ProductId { get; set; }
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public decimal CurrentStock { get; set; }
        public decimal MinStockLevel { get; set; }
        public decimal ReorderPoint { get; set; }
        public string Status { get; set; }
    }

    public class DashboardData
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalProfit { get; set; }
        public int TotalInvoices { get; set; }
        public int TotalCustomers { get; set; }
        public decimal SalesGrowth { get; set; }
        public decimal ProfitGrowth { get; set; }
        public List<DailySalesReport> SalesChart { get; set; } = new List<DailySalesReport>();
        public List<TopSellingProduct> TopProducts { get; set; } = new List<TopSellingProduct>();
        public List<LowStockAlert> LowStockAlerts { get; set; } = new List<LowStockAlert>();
        public List<RecentActivity> RecentActivities { get; set; } = new List<RecentActivity>();
    }

    public class RecentActivity
    {
        public DateTime Date { get; set; }
        public string Type { get; set; }
        public string Description { get; set; }
        public string User { get; set; }
        public string Details { get; set; }
    }

    public class Alert
    {
        public string Type { get; set; }
        public string Title { get; set; }
        public string Message { get; set; }
        public DateTime Date { get; set; }
        public string Severity { get; set; }
        public bool IsRead { get; set; }
    }
}
