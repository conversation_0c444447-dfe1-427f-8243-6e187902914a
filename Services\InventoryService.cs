using AccountingSystem11.Data;
using AccountingSystem11.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AccountingSystem11.Services
{
    /// <summary>
    /// Inventory service implementation
    /// </summary>
    public class InventoryService : IInventoryService
    {
        private readonly AccountingDbContext _context;

        public InventoryService(AccountingDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<InventoryTransaction>> GetAllTransactionsAsync()
        {
            return await _context.InventoryTransactions
                .Include(t => t.Product)
                .Include(t => t.InvoiceItem)
                .OrderByDescending(t => t.TransactionDate)
                .ToListAsync();
        }

        public async Task<InventoryTransaction> GetTransactionByIdAsync(int id)
        {
            return await _context.InventoryTransactions
                .Include(t => t.Product)
                .Include(t => t.InvoiceItem)
                .FirstOrDefaultAsync(t => t.Id == id);
        }

        public async Task<InventoryTransaction> CreateTransactionAsync(InventoryTransaction transaction)
        {
            if (transaction == null)
                throw new ArgumentNullException(nameof(transaction));

            // Update product stock
            var product = await _context.Products.FindAsync(transaction.ProductId);
            if (product != null)
            {
                transaction.StockBefore = product.StockQuantity;
                product.StockQuantity += transaction.Quantity;
                transaction.StockAfter = product.StockQuantity;
                product.Update();
            }

            transaction.CreatedAt = DateTime.Now;
            _context.InventoryTransactions.Add(transaction);
            await _context.SaveChangesAsync();

            return transaction;
        }

        public async Task<InventoryTransaction> UpdateTransactionAsync(InventoryTransaction transaction)
        {
            if (transaction == null)
                throw new ArgumentNullException(nameof(transaction));

            var existingTransaction = await _context.InventoryTransactions.FindAsync(transaction.Id);
            if (existingTransaction == null)
                throw new InvalidOperationException("المعاملة غير موجودة");

            // Reverse the old transaction effect on stock
            var product = await _context.Products.FindAsync(transaction.ProductId);
            if (product != null)
            {
                product.StockQuantity -= existingTransaction.Quantity;
                product.StockQuantity += transaction.Quantity;
                product.Update();
            }

            // Update transaction properties
            existingTransaction.TransactionDate = transaction.TransactionDate;
            existingTransaction.TransactionNumber = transaction.TransactionNumber;
            existingTransaction.TransactionType = transaction.TransactionType;
            existingTransaction.Quantity = transaction.Quantity;
            existingTransaction.UnitCost = transaction.UnitCost;
            existingTransaction.TotalCost = transaction.TotalCost;
            existingTransaction.ReferenceNumber = transaction.ReferenceNumber;
            existingTransaction.Description = transaction.Description;
            existingTransaction.Notes = transaction.Notes;
            existingTransaction.Location = transaction.Location;
            existingTransaction.BatchNumber = transaction.BatchNumber;
            existingTransaction.ExpiryDate = transaction.ExpiryDate;
            existingTransaction.Update();

            await _context.SaveChangesAsync();
            return existingTransaction;
        }

        public async Task<bool> DeleteTransactionAsync(int id)
        {
            var transaction = await _context.InventoryTransactions.FindAsync(id);
            if (transaction == null)
                return false;

            // Reverse the transaction effect on stock
            var product = await _context.Products.FindAsync(transaction.ProductId);
            if (product != null)
            {
                product.StockQuantity -= transaction.Quantity;
                product.Update();
            }

            transaction.Delete();
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<InventoryTransaction>> GetTransactionsByProductAsync(int productId)
        {
            return await _context.InventoryTransactions
                .Include(t => t.Product)
                .Where(t => t.ProductId == productId)
                .OrderByDescending(t => t.TransactionDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryTransaction>> GetTransactionsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.InventoryTransactions
                .Include(t => t.Product)
                .Where(t => t.TransactionDate >= startDate && t.TransactionDate <= endDate)
                .OrderByDescending(t => t.TransactionDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryTransaction>> GetTransactionsByTypeAsync(InventoryTransactionType type)
        {
            return await _context.InventoryTransactions
                .Include(t => t.Product)
                .Where(t => t.TransactionType == type)
                .OrderByDescending(t => t.TransactionDate)
                .ToListAsync();
        }

        public async Task<bool> AdjustStockAsync(int productId, decimal quantity, string reason, string location = null)
        {
            var product = await _context.Products.FindAsync(productId);
            if (product == null)
                return false;

            var transaction = new InventoryTransaction
            {
                ProductId = productId,
                TransactionDate = DateTime.Now,
                TransactionNumber = await GenerateNextTransactionNumberAsync(InventoryTransactionType.Adjustment),
                TransactionType = InventoryTransactionType.Adjustment,
                Quantity = quantity,
                UnitCost = product.PurchasePrice,
                TotalCost = quantity * product.PurchasePrice,
                StockBefore = product.StockQuantity,
                Description = reason,
                Location = location ?? product.Location,
                CreatedAt = DateTime.Now
            };

            product.StockQuantity += quantity;
            transaction.StockAfter = product.StockQuantity;
            product.Update();

            _context.InventoryTransactions.Add(transaction);
            await _context.SaveChangesAsync();

            return true;
        }

        public async Task<bool> TransferStockAsync(int productId, decimal quantity, string fromLocation, string toLocation, string reason)
        {
            var product = await _context.Products.FindAsync(productId);
            if (product == null)
                return false;

            // Create outbound transaction
            var outTransaction = new InventoryTransaction
            {
                ProductId = productId,
                TransactionDate = DateTime.Now,
                TransactionNumber = await GenerateNextTransactionNumberAsync(InventoryTransactionType.Transfer),
                TransactionType = InventoryTransactionType.Transfer,
                Quantity = -quantity,
                UnitCost = product.PurchasePrice,
                TotalCost = -quantity * product.PurchasePrice,
                StockBefore = product.StockQuantity,
                StockAfter = product.StockQuantity - quantity,
                Description = $"نقل من {fromLocation} إلى {toLocation}: {reason}",
                Location = fromLocation,
                CreatedAt = DateTime.Now
            };

            // Create inbound transaction
            var inTransaction = new InventoryTransaction
            {
                ProductId = productId,
                TransactionDate = DateTime.Now,
                TransactionNumber = await GenerateNextTransactionNumberAsync(InventoryTransactionType.Transfer),
                TransactionType = InventoryTransactionType.Transfer,
                Quantity = quantity,
                UnitCost = product.PurchasePrice,
                TotalCost = quantity * product.PurchasePrice,
                StockBefore = product.StockQuantity - quantity,
                StockAfter = product.StockQuantity,
                Description = $"نقل من {fromLocation} إلى {toLocation}: {reason}",
                Location = toLocation,
                CreatedAt = DateTime.Now
            };

            _context.InventoryTransactions.AddRange(outTransaction, inTransaction);
            await _context.SaveChangesAsync();

            return true;
        }

        public async Task<decimal> GetCurrentStockAsync(int productId, string location = null)
        {
            var product = await _context.Products.FindAsync(productId);
            return product?.StockQuantity ?? 0;
        }

        public async Task<decimal> GetStockValueAsync(int? productId = null, string location = null)
        {
            var query = _context.Products.Where(p => p.IsActive);

            if (productId.HasValue)
                query = query.Where(p => p.Id == productId.Value);

            if (!string.IsNullOrWhiteSpace(location))
                query = query.Where(p => p.Location == location);

            return await query.SumAsync(p => p.StockQuantity * p.PurchasePrice);
        }

        public async Task<IEnumerable<Product>> GetLowStockProductsAsync()
        {
            return await _context.Products
                .Where(p => p.IsActive && p.StockQuantity <= p.MinStockLevel)
                .OrderBy(p => p.StockQuantity)
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> GetOutOfStockProductsAsync()
        {
            return await _context.Products
                .Where(p => p.IsActive && p.StockQuantity <= 0)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> GetOverStockProductsAsync()
        {
            return await _context.Products
                .Where(p => p.IsActive && p.StockQuantity > p.MaxStockLevel && p.MaxStockLevel > 0)
                .OrderByDescending(p => p.StockQuantity)
                .ToListAsync();
        }

        public async Task<InventoryValuationReport> GetInventoryValuationAsync(DateTime? asOfDate = null)
        {
            var reportDate = asOfDate ?? DateTime.Today;
            
            var products = await _context.Products
                .Include(p => p.Category)
                .Where(p => p.IsActive)
                .ToListAsync();

            var report = new InventoryValuationReport
            {
                ReportDate = reportDate,
                TotalProducts = products.Count,
                TotalValue = products.Sum(p => p.StockQuantity * p.PurchasePrice)
            };

            foreach (var product in products)
            {
                report.Items.Add(new InventoryValuationItem
                {
                    ProductId = product.Id,
                    ProductCode = product.Code,
                    ProductName = product.Name,
                    Category = product.Category?.Name ?? "غير محدد",
                    Quantity = product.StockQuantity,
                    UnitCost = product.PurchasePrice,
                    TotalValue = product.StockQuantity * product.PurchasePrice,
                    Location = product.Location,
                    LastTransactionDate = product.UpdatedAt ?? product.CreatedAt
                });
            }

            return report;
        }

        public async Task<StockMovementReport> GetStockMovementReportAsync(DateTime startDate, DateTime endDate, int? productId = null)
        {
            var query = _context.InventoryTransactions
                .Include(t => t.Product)
                .Where(t => t.TransactionDate >= startDate && t.TransactionDate <= endDate);

            if (productId.HasValue)
                query = query.Where(t => t.ProductId == productId.Value);

            var transactions = await query
                .OrderBy(t => t.TransactionDate)
                .ToListAsync();

            var report = new StockMovementReport
            {
                StartDate = startDate,
                EndDate = endDate,
                ProductId = productId
            };

            if (productId.HasValue)
            {
                var product = await _context.Products.FindAsync(productId.Value);
                report.ProductName = product?.Name;
                
                // Calculate opening stock
                var openingStock = await _context.InventoryTransactions
                    .Where(t => t.ProductId == productId.Value && t.TransactionDate < startDate)
                    .SumAsync(t => t.Quantity);
                
                report.OpeningStock = openingStock;
                report.TotalIn = transactions.Where(t => t.Quantity > 0).Sum(t => t.Quantity);
                report.TotalOut = Math.Abs(transactions.Where(t => t.Quantity < 0).Sum(t => t.Quantity));
                report.ClosingStock = report.OpeningStock + report.TotalIn - report.TotalOut;
            }

            var runningBalance = report.OpeningStock;
            foreach (var transaction in transactions)
            {
                runningBalance += transaction.Quantity;
                
                report.Movements.Add(new StockMovementItem
                {
                    Date = transaction.TransactionDate,
                    TransactionNumber = transaction.TransactionNumber,
                    Type = transaction.TransactionType,
                    Description = transaction.Description,
                    QuantityIn = transaction.Quantity > 0 ? transaction.Quantity : 0,
                    QuantityOut = transaction.Quantity < 0 ? Math.Abs(transaction.Quantity) : 0,
                    Balance = runningBalance,
                    Reference = transaction.ReferenceNumber
                });
            }

            return report;
        }

        public async Task<string> GenerateNextTransactionNumberAsync(InventoryTransactionType type)
        {
            var prefix = type switch
            {
                InventoryTransactionType.Purchase => "PUR",
                InventoryTransactionType.Sale => "SAL",
                InventoryTransactionType.Return => "RET",
                InventoryTransactionType.Adjustment => "ADJ",
                InventoryTransactionType.Transfer => "TRF",
                InventoryTransactionType.OpeningBalance => "OPN",
                InventoryTransactionType.Damage => "DMG",
                InventoryTransactionType.Loss => "LSS",
                InventoryTransactionType.Production => "PRD",
                InventoryTransactionType.Consumption => "CON",
                _ => "INV"
            };

            var lastTransaction = await _context.InventoryTransactions
                .Where(t => t.TransactionType == type)
                .OrderByDescending(t => t.Id)
                .FirstOrDefaultAsync();

            var nextNumber = (lastTransaction?.Id ?? 0) + 1;
            var year = DateTime.Now.Year;
            
            return $"{prefix}{year}{nextNumber:D6}";
        }

        public async Task<bool> ProcessSaleAsync(int productId, decimal quantity, decimal unitPrice, string invoiceNumber)
        {
            var product = await _context.Products.FindAsync(productId);
            if (product == null)
                return false;

            var transaction = new InventoryTransaction
            {
                ProductId = productId,
                TransactionDate = DateTime.Now,
                TransactionNumber = await GenerateNextTransactionNumberAsync(InventoryTransactionType.Sale),
                TransactionType = InventoryTransactionType.Sale,
                Quantity = -quantity,
                UnitCost = product.PurchasePrice,
                TotalCost = -quantity * product.PurchasePrice,
                StockBefore = product.StockQuantity,
                Description = $"بيع - فاتورة رقم {invoiceNumber}",
                ReferenceNumber = invoiceNumber,
                CreatedAt = DateTime.Now
            };

            product.StockQuantity -= quantity;
            transaction.StockAfter = product.StockQuantity;
            product.Update();

            _context.InventoryTransactions.Add(transaction);
            await _context.SaveChangesAsync();

            return true;
        }

        public async Task<bool> ProcessPurchaseAsync(int productId, decimal quantity, decimal unitCost, string invoiceNumber)
        {
            var product = await _context.Products.FindAsync(productId);
            if (product == null)
                return false;

            var transaction = new InventoryTransaction
            {
                ProductId = productId,
                TransactionDate = DateTime.Now,
                TransactionNumber = await GenerateNextTransactionNumberAsync(InventoryTransactionType.Purchase),
                TransactionType = InventoryTransactionType.Purchase,
                Quantity = quantity,
                UnitCost = unitCost,
                TotalCost = quantity * unitCost,
                StockBefore = product.StockQuantity,
                Description = $"شراء - فاتورة رقم {invoiceNumber}",
                ReferenceNumber = invoiceNumber,
                CreatedAt = DateTime.Now
            };

            product.StockQuantity += quantity;
            transaction.StockAfter = product.StockQuantity;
            product.Update();

            _context.InventoryTransactions.Add(transaction);
            await _context.SaveChangesAsync();

            return true;
        }

        public async Task<bool> ProcessReturnAsync(int productId, decimal quantity, bool isSaleReturn, string invoiceNumber)
        {
            var product = await _context.Products.FindAsync(productId);
            if (product == null)
                return false;

            var adjustmentQuantity = isSaleReturn ? quantity : -quantity;
            var description = isSaleReturn ? "مرتجع مبيعات" : "مرتجع مشتريات";

            var transaction = new InventoryTransaction
            {
                ProductId = productId,
                TransactionDate = DateTime.Now,
                TransactionNumber = await GenerateNextTransactionNumberAsync(InventoryTransactionType.Return),
                TransactionType = InventoryTransactionType.Return,
                Quantity = adjustmentQuantity,
                UnitCost = product.PurchasePrice,
                TotalCost = adjustmentQuantity * product.PurchasePrice,
                StockBefore = product.StockQuantity,
                Description = $"{description} - فاتورة رقم {invoiceNumber}",
                ReferenceNumber = invoiceNumber,
                CreatedAt = DateTime.Now
            };

            product.StockQuantity += adjustmentQuantity;
            transaction.StockAfter = product.StockQuantity;
            product.Update();

            _context.InventoryTransactions.Add(transaction);
            await _context.SaveChangesAsync();

            return true;
        }
    }
}
