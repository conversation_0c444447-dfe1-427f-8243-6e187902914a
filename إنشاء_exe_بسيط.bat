@echo off
title إنشاء EXE للبرنامج البسيط
color 0B
echo.
echo ========================================================
echo        إنشاء EXE للبرنامج البسيط
echo ========================================================
echo.

echo 🔨 جاري إنشاء ملف .exe للبرنامج البسيط...
echo ✅ هذا البرنامج يعمل بدون أخطاء
echo.

REM إنشاء مجلد الإخراج
if not exist "EXE_بسيط" mkdir "EXE_بسيط"

echo [1] تنظيف المشروع...
dotnet clean البرنامج_البسيط.csproj >nul 2>&1
echo ✅ تم التنظيف

echo.
echo [2] بناء المشروع...
dotnet build البرنامج_البسيط.csproj --configuration Release >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ فشل البناء
    echo 🔧 تأكد من تثبيت .NET 6.0 SDK
    pause
    exit /b 1
)
echo ✅ تم البناء بنجاح

echo.
echo [3] إنشاء ملف .exe مستقل...
echo ⏳ قد يستغرق دقائق قليلة...

dotnet publish البرنامج_البسيط.csproj ^
    --configuration Release ^
    --runtime win-x64 ^
    --self-contained true ^
    --output "EXE_بسيط" ^
    --verbosity minimal ^
    -p:PublishSingleFile=true ^
    -p:PublishTrimmed=true

if %errorlevel% equ 0 (
    echo.
    echo ========================================================
    echo 🎉 تم إنشاء ملف .exe بنجاح!
    echo ========================================================
    echo.
    
    REM البحث عن الملف التنفيذي
    if exist "EXE_بسيط\البرنامج_البسيط.exe" (
        set "EXE_NAME=البرنامج_البسيط.exe"
    ) else (
        set "EXE_NAME=*.exe"
    )
    
    echo 📁 مكان الملف: EXE_بسيط\
    echo 📄 اسم الملف: %EXE_NAME%
    echo.
    
    REM حساب حجم الملف
    for %%A in ("EXE_بسيط\*.exe") do (
        echo 💾 حجم الملف: %%~zA بايت
    )
    
    echo.
    echo ✅ المميزات:
    echo    • ملف واحد مستقل (.exe)
    echo    • لا يحتاج تثبيت .NET
    echo    • يعمل على أي جهاز Windows
    echo    • بدون أخطاء برمجية
    echo    • سريع التشغيل
    echo.
    
    REM إنشاء ملف تشغيل
    echo @echo off > "EXE_بسيط\تشغيل.bat"
    echo title نظام المحاسبة 11 - البرنامج البسيط >> "EXE_بسيط\تشغيل.bat"
    echo color 0A >> "EXE_بسيط\تشغيل.bat"
    echo echo ======================================== >> "EXE_بسيط\تشغيل.bat"
    echo echo      نظام المحاسبة 11 - البرنامج البسيط >> "EXE_بسيط\تشغيل.bat"
    echo echo ======================================== >> "EXE_بسيط\تشغيل.bat"
    echo echo. >> "EXE_بسيط\تشغيل.bat"
    echo echo 🚀 جاري التشغيل... >> "EXE_بسيط\تشغيل.bat"
    echo echo ✅ برنامج مبسط بدون أخطاء >> "EXE_بسيط\تشغيل.bat"
    echo echo. >> "EXE_بسيط\تشغيل.bat"
    echo %EXE_NAME% >> "EXE_بسيط\تشغيل.bat"
    echo pause >> "EXE_بسيط\تشغيل.bat"
    
    REM إنشاء ملف README
    echo نظام المحاسبة 11 - البرنامج البسيط > "EXE_بسيط\README.txt"
    echo. >> "EXE_بسيط\README.txt"
    echo هذا برنامج مبسط يعمل بدون أخطاء >> "EXE_بسيط\README.txt"
    echo. >> "EXE_بسيط\README.txt"
    echo طريقة التشغيل: >> "EXE_بسيط\README.txt"
    echo 1. اضغط دبل كليك على تشغيل.bat >> "EXE_بسيط\README.txt"
    echo 2. أو اضغط دبل كليك على %EXE_NAME% >> "EXE_بسيط\README.txt"
    echo. >> "EXE_بسيط\README.txt"
    echo المميزات: >> "EXE_بسيط\README.txt"
    echo - يعمل بدون تثبيت .NET >> "EXE_بسيط\README.txt"
    echo - لا يحتاج قاعدة بيانات معقدة >> "EXE_بسيط\README.txt"
    echo - واجهة عربية جميلة >> "EXE_بسيط\README.txt"
    echo - سريع وخفيف >> "EXE_بسيط\README.txt"
    
    echo ✅ تم إنشاء الملفات المساعدة
    echo.
    
    REM إنشاء ملف مضغوط
    echo 📦 إنشاء ملف مضغوط...
    powershell -Command "Compress-Archive -Path 'EXE_بسيط\*' -DestinationPath 'نظام_المحاسبة_11_بسيط.zip' -Force" >nul 2>&1
    if exist "نظام_المحاسبة_11_بسيط.zip" (
        echo ✅ تم إنشاء ملف مضغوط: نظام_المحاسبة_11_بسيط.zip
    )
    
    echo.
    echo 🎯 الملفات الجاهزة:
    echo 📁 EXE_بسيط\ - المجلد الكامل
    echo 📄 %EXE_NAME% - الملف التنفيذي
    echo 📄 تشغيل.bat - ملف تشغيل سهل
    echo 📄 README.txt - تعليمات الاستخدام
    echo 📦 نظام_المحاسبة_11_بسيط.zip - ملف مضغوط
    echo.
    
    set /p choice="هل تريد فتح مجلد الملفات؟ (y/n): "
    if /i "%choice%"=="y" (
        explorer "EXE_بسيط"
    )
    
    echo.
    set /p choice2="هل تريد اختبار التشغيل؟ (y/n): "
    if /i "%choice2%"=="y" (
        cd "EXE_بسيط"
        echo 🚀 جاري اختبار التشغيل...
        %EXE_NAME%
    )
    
) else (
    echo.
    echo ❌ فشل إنشاء ملف .exe
    echo.
    echo 🔧 الحلول:
    echo 1. تأكد من تثبيت .NET 6.0 SDK
    echo 2. شغل Command Prompt كـ Administrator
    echo 3. تأكد من وجود مساحة كافية (200 MB)
    echo 4. أغلق برامج مضاد الفيروسات مؤقتاً
    echo.
    echo 💡 أو جرب التشغيل المباشر:
    echo dotnet run --project البرنامج_البسيط.csproj
)

echo.
pause
