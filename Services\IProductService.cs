using AccountingSystem11.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AccountingSystem11.Services
{
    /// <summary>
    /// Interface for product service operations
    /// </summary>
    public interface IProductService
    {
        Task<IEnumerable<Product>> GetAllProductsAsync();
        Task<Product> GetProductByIdAsync(int id);
        Task<Product> GetProductByCodeAsync(string code);
        Task<Product> GetProductByBarcodeAsync(string barcode);
        Task<Product> CreateProductAsync(Product product);
        Task<Product> UpdateProductAsync(Product product);
        Task<bool> DeleteProductAsync(int id);
        Task<bool> ProductExistsAsync(int id);
        Task<bool> CodeExistsAsync(string code, int? excludeId = null);
        Task<bool> BarcodeExistsAsync(string barcode, int? excludeId = null);
        Task<IEnumerable<Product>> SearchProductsAsync(string searchTerm);
        Task<IEnumerable<Product>> GetProductsByCategoryAsync(int categoryId);
        Task<IEnumerable<Product>> GetLowStockProductsAsync();
        Task<IEnumerable<Product>> GetOutOfStockProductsAsync();
        Task<bool> UpdateStockQuantityAsync(int productId, decimal quantity);
        Task<bool> AdjustStockAsync(int productId, decimal adjustment, string reason);
        Task<IEnumerable<Product>> GetTopSellingProductsAsync(int count = 10);
        Task<decimal> GetProductStockValueAsync();
        Task<string> GenerateNextProductCodeAsync();
    }
}
