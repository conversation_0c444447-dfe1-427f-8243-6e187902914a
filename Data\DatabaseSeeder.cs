using AccountingSystem11.Models;
using System;
using System.Linq;

namespace AccountingSystem11.Data
{
    /// <summary>
    /// Database seeder for initial data
    /// </summary>
    public static class DatabaseSeeder
    {
        public static void SeedData(AccountingDbContext context)
        {
            try
            {
                // Seed admin user if not exists
                SeedAdminUser(context);

                // Seed default categories if not exists
                SeedProductCategories(context);

                // Seed sample customer if not exists
                SeedSampleCustomer(context);

                // Seed sample products if not exists
                SeedSampleProducts(context);

                context.SaveChanges();
            }
            catch (Exception ex)
            {
                // Log error (implement logging as needed)
                System.Diagnostics.Debug.WriteLine($"Error seeding database: {ex.Message}");
            }
        }

        private static void SeedAdminUser(AccountingDbContext context)
        {
            if (!context.Users.Any(u => u.Username == "admin"))
            {
                var adminUser = new User
                {
                    Username = "admin",
                    FullName = "مدير النظام",
                    Email = "<EMAIL>",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                    Role = UserRole.Admin,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = "System"
                };

                context.Users.Add(adminUser);
            }
        }

        private static void SeedProductCategories(AccountingDbContext context)
        {
            var categories = new[]
            {
                new ProductCategory { Name = "عام", Code = "GEN", Description = "فئة عامة للمنتجات", CreatedAt = DateTime.Now, CreatedBy = "System" },
                new ProductCategory { Name = "ملابس", Code = "CLO", Description = "الملابس والأزياء", CreatedAt = DateTime.Now, CreatedBy = "System" },
                new ProductCategory { Name = "أغذية ومشروبات", Code = "FOO", Description = "المواد الغذائية والمشروبات", CreatedAt = DateTime.Now, CreatedBy = "System" },
                new ProductCategory { Name = "إلكترونيات", Code = "ELE", Description = "الأجهزة الإلكترونية والكهربائية", CreatedAt = DateTime.Now, CreatedBy = "System" },
                new ProductCategory { Name = "أدوية", Code = "MED", Description = "الأدوية والمستحضرات الطبية", CreatedAt = DateTime.Now, CreatedBy = "System" },
                new ProductCategory { Name = "مستحضرات تجميل", Code = "COS", Description = "مستحضرات التجميل والعناية", CreatedAt = DateTime.Now, CreatedBy = "System" },
                new ProductCategory { Name = "أدوات منزلية", Code = "HOM", Description = "الأدوات والأجهزة المنزلية", CreatedAt = DateTime.Now, CreatedBy = "System" },
                new ProductCategory { Name = "كتب وقرطاسية", Code = "BOO", Description = "الكتب والأدوات المكتبية", CreatedAt = DateTime.Now, CreatedBy = "System" },
                new ProductCategory { Name = "ألعاب", Code = "TOY", Description = "ألعاب الأطفال والترفيه", CreatedAt = DateTime.Now, CreatedBy = "System" },
                new ProductCategory { Name = "رياضة", Code = "SPO", Description = "المعدات والأدوات الرياضية", CreatedAt = DateTime.Now, CreatedBy = "System" }
            };

            foreach (var category in categories)
            {
                if (!context.ProductCategories.Any(c => c.Code == category.Code))
                {
                    context.ProductCategories.Add(category);
                }
            }
        }

        private static void SeedSampleCustomer(AccountingDbContext context)
        {
            if (!context.Customers.Any())
            {
                var sampleCustomer = new Customer
                {
                    Name = "عميل تجريبي",
                    CompanyName = "شركة تجريبية",
                    Phone = "02-********",
                    Mobile = "***********",
                    Email = "<EMAIL>",
                    Address = "123 شارع التجربة، المعادي",
                    City = "القاهرة",
                    Governorate = "القاهرة",
                    PostalCode = "11728",
                    CustomerType = CustomerType.Company,
                    CreditLimit = 50000,
                    PaymentTerms = 30,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = "System"
                };

                context.Customers.Add(sampleCustomer);
            }
        }

        private static void SeedSampleProducts(AccountingDbContext context)
        {
            if (!context.Products.Any())
            {
                var generalCategory = context.ProductCategories.FirstOrDefault(c => c.Code == "GEN");
                var clothingCategory = context.ProductCategories.FirstOrDefault(c => c.Code == "CLO");
                var foodCategory = context.ProductCategories.FirstOrDefault(c => c.Code == "FOO");

                var sampleProducts = new[]
                {
                    new Product
                    {
                        Code = "PRD001",
                        Name = "منتج تجريبي 1",
                        Description = "وصف المنتج التجريبي الأول",
                        CategoryId = generalCategory?.Id,
                        Unit = "قطعة",
                        PurchasePrice = 50,
                        SellingPrice = 75,
                        MinSellingPrice = 60,
                        WholesalePrice = 65,
                        StockQuantity = 100,
                        MinStockLevel = 10,
                        MaxStockLevel = 500,
                        ReorderPoint = 20,
                        IsTaxable = true,
                        IsActive = true,
                        CreatedAt = DateTime.Now,
                        CreatedBy = "System"
                    },
                    new Product
                    {
                        Code = "CLO001",
                        Name = "قميص قطني",
                        Description = "قميص قطني عالي الجودة",
                        CategoryId = clothingCategory?.Id,
                        Unit = "قطعة",
                        PurchasePrice = 80,
                        SellingPrice = 120,
                        MinSellingPrice = 100,
                        WholesalePrice = 110,
                        StockQuantity = 50,
                        MinStockLevel = 5,
                        MaxStockLevel = 200,
                        ReorderPoint = 10,
                        IsTaxable = true,
                        IsActive = true,
                        CreatedAt = DateTime.Now,
                        CreatedBy = "System"
                    },
                    new Product
                    {
                        Code = "FOO001",
                        Name = "عصير برتقال",
                        Description = "عصير برتقال طبيعي 1 لتر",
                        CategoryId = foodCategory?.Id,
                        Unit = "زجاجة",
                        PurchasePrice = 15,
                        SellingPrice = 25,
                        MinSellingPrice = 20,
                        WholesalePrice = 22,
                        StockQuantity = 200,
                        MinStockLevel = 20,
                        MaxStockLevel = 1000,
                        ReorderPoint = 50,
                        IsTaxable = true,
                        IsActive = true,
                        CreatedAt = DateTime.Now,
                        CreatedBy = "System"
                    }
                };

                foreach (var product in sampleProducts)
                {
                    context.Products.Add(product);
                }
            }
        }
    }
}
