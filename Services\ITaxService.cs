using AccountingSystem11.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AccountingSystem11.Services
{
    /// <summary>
    /// Interface for Egyptian tax system operations
    /// </summary>
    public interface ITaxService
    {
        /// <summary>
        /// Calculate VAT amount for given amount and rate
        /// </summary>
        decimal CalculateVat(decimal amount, decimal vatRate = 14);

        /// <summary>
        /// Calculate total tax including VAT and additional taxes
        /// </summary>
        decimal CalculateTotalTax(decimal amount, decimal vatRate = 14, decimal additionalTaxRate = 0);

        /// <summary>
        /// Get current VAT rate
        /// </summary>
        decimal GetCurrentVatRate();

        /// <summary>
        /// Get tax settings for specific business type
        /// </summary>
        Task<TaxSettings> GetTaxSettingsAsync(BusinessType businessType);

        /// <summary>
        /// Update tax settings
        /// </summary>
        Task<bool> UpdateTaxSettingsAsync(TaxSettings settings);

        /// <summary>
        /// Generate tax report for date range
        /// </summary>
        Task<TaxReport> GenerateTaxReportAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Get VAT return data for submission
        /// </summary>
        Task<VatReturnData> GetVatReturnDataAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Validate tax number format
        /// </summary>
        bool ValidateTaxNumber(string taxNumber);

        /// <summary>
        /// Format tax number according to Egyptian standards
        /// </summary>
        string FormatTaxNumber(string taxNumber);

        /// <summary>
        /// Check if product/service is tax exempt
        /// </summary>
        bool IsProductTaxExempt(int productId);

        /// <summary>
        /// Get tax rate for specific product
        /// </summary>
        Task<decimal> GetProductTaxRateAsync(int productId);
    }

    /// <summary>
    /// Tax settings for different business types
    /// </summary>
    public class TaxSettings
    {
        public int Id { get; set; }
        public BusinessType BusinessType { get; set; }
        public decimal VatRate { get; set; } = 14;
        public decimal AdditionalTaxRate { get; set; } = 0;
        public bool IsVatRegistered { get; set; } = true;
        public string TaxNumber { get; set; }
        public string TaxOffice { get; set; }
        public DateTime EffectiveDate { get; set; }
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// Tax report data
    /// </summary>
    public class TaxReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TaxableAmount { get; set; }
        public decimal VatAmount { get; set; }
        public decimal AdditionalTaxAmount { get; set; }
        public decimal TotalTaxAmount { get; set; }
        public int InvoiceCount { get; set; }
        public List<TaxReportItem> Items { get; set; } = new List<TaxReportItem>();
    }

    /// <summary>
    /// Tax report item
    /// </summary>
    public class TaxReportItem
    {
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public string CustomerName { get; set; }
        public string CustomerTaxNumber { get; set; }
        public decimal Amount { get; set; }
        public decimal VatAmount { get; set; }
        public decimal TotalAmount { get; set; }
    }

    /// <summary>
    /// VAT return data for submission
    /// </summary>
    public class VatReturnData
    {
        public DateTime PeriodStart { get; set; }
        public DateTime PeriodEnd { get; set; }
        public decimal OutputVat { get; set; }
        public decimal InputVat { get; set; }
        public decimal NetVat { get; set; }
        public decimal TaxableSupplies { get; set; }
        public decimal ExemptSupplies { get; set; }
        public decimal ZeroRatedSupplies { get; set; }
    }

    /// <summary>
    /// Business types for tax purposes
    /// </summary>
    public enum BusinessType
    {
        Retail = 1,         // تجارة تجزئة
        Wholesale = 2,      // تجارة جملة
        Manufacturing = 3,  // تصنيع
        Services = 4,       // خدمات
        Restaurant = 5,     // مطعم
        Pharmacy = 6,       // صيدلية
        Medical = 7,        // طبي
        Education = 8,      // تعليمي
        Transport = 9,      // نقل
        Construction = 10,  // إنشاءات
        Agriculture = 11,   // زراعة
        Import = 12,        // استيراد
        Export = 13,        // تصدير
        Other = 99          // أخرى
    }
}
