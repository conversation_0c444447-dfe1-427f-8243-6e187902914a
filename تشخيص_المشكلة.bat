@echo off
title تشخيص مشاكل نظام المحاسبة 11
color 0C
echo.
echo ========================================================
echo        تشخيص مشاكل نظام المحاسبة 11
echo ========================================================
echo.

echo [1] التحقق من .NET...
dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ .NET مثبت - الإصدار:
    dotnet --version
) else (
    echo ❌ .NET غير مثبت
    echo.
    echo 🔧 الحل:
    echo 1. اذهب إلى: https://dotnet.microsoft.com/download/dotnet/6.0
    echo 2. حمل ".NET Desktop Runtime 6.0.x"
    echo 3. ثبته وأعد تشغيل الجهاز
    echo.
    pause
    exit /b 1
)

echo.
echo [2] التحقق من Access Database Engine...
set "ACCESS_OK=0"

reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Classes\Microsoft.ACE.OLEDB.16.0" >nul 2>&1
if %errorlevel% equ 0 set "ACCESS_OK=1"

reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Classes\Microsoft.ACE.OLEDB.12.0" >nul 2>&1
if %errorlevel% equ 0 set "ACCESS_OK=1"

if "%ACCESS_OK%"=="1" (
    echo ✅ Access Database Engine مثبت
) else (
    echo ❌ Access Database Engine غير مثبت
    echo.
    echo 🔧 الحل:
    echo 1. اذهب إلى: https://www.microsoft.com/en-us/download/details.aspx?id=54920
    echo 2. حمل "AccessDatabaseEngine_X64.exe" للأجهزة الحديثة
    echo 3. ثبته وأعد تشغيل الجهاز
    echo.
    pause
    exit /b 1
)

echo.
echo [3] اختبار بناء البرنامج...
echo 🔧 جاري اختبار البناء...

dotnet build AccountingSystem11.csproj --verbosity quiet >build_output.txt 2>&1
set "BUILD_RESULT=%errorlevel%"

if %BUILD_RESULT% equ 0 (
    echo ✅ البناء نجح!
    del build_output.txt >nul 2>&1
) else (
    echo ❌ فشل البناء
    echo.
    echo 📋 تفاصيل الأخطاء:
    type build_output.txt
    echo.
    echo 🔧 الحلول المقترحة:
    echo 1. شغل الملف كـ Administrator
    echo 2. أغلق برامج مضاد الفيروسات مؤقتاً
    echo 3. تأكد من وجود مساحة كافية (1 GB)
    echo 4. أعد تشغيل الجهاز
    echo.
    del build_output.txt >nul 2>&1
    pause
    exit /b 1
)

echo.
echo [4] اختبار تشغيل بسيط...
echo 🚀 جاري اختبار التشغيل...

timeout /t 2 /nobreak >nul

echo.
echo ========================================================
echo 🎉 التشخيص مكتمل!
echo ========================================================
echo.

if %BUILD_RESULT% equ 0 (
    echo ✅ جميع المتطلبات متوفرة
    echo ✅ البرنامج جاهز للتشغيل
    echo.
    echo 🚀 طرق التشغيل:
    echo.
    echo 1️⃣ الطريقة الأولى (مُوصى بها):
    echo    📁 اضغط دبل كليك على: تشغيل_نهائي.bat
    echo.
    echo 2️⃣ الطريقة الثانية:
    echo    📁 اضغط دبل كليك على: تشغيل_بسيط.bat
    echo.
    echo 3️⃣ الطريقة الثالثة (يدوي):
    echo    💻 افتح Command Prompt هنا واكتب:
    echo    dotnet run --project AccountingSystem11.csproj
    echo.
    echo 🔐 بيانات تسجيل الدخول:
    echo 👤 اسم المستخدم: admin
    echo 🔑 كلمة المرور: admin123
    echo.
    
    set /p choice="هل تريد تشغيل البرنامج الآن؟ (y/n): "
    if /i "%choice%"=="y" (
        echo.
        echo 🚀 جاري التشغيل...
        dotnet run --project AccountingSystem11.csproj
    )
) else (
    echo ⚠️  هناك مشاكل تحتاج حل
    echo راجع الأخطاء أعلاه واتبع الحلول المقترحة
)

echo.
pause
