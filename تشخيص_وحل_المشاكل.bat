@echo off
title تشخيص وحل المشاكل - نظام المحاسبة 11
color 0E
echo.
echo ========================================================
echo      تشخيص وحل المشاكل - نظام المحاسبة 11
echo ========================================================
echo.

echo 🔍 جاري تشخيص المشاكل...
echo.

REM التحقق من وجود الملفات الأساسية
echo [1] التحقق من الملفات الأساسية...
set "MISSING_FILES="

if not exist "AccountingSystem11.csproj" (
    echo ❌ ملف المشروع مفقود: AccountingSystem11.csproj
    set "MISSING_FILES=1"
) else (
    echo ✅ ملف المشروع موجود
)

if not exist "App.xaml" (
    echo ❌ ملف التطبيق مفقود: App.xaml
    set "MISSING_FILES=1"
) else (
    echo ✅ ملف التطبيق موجود
)

if not exist "Views\MainWindow.xaml" (
    echo ❌ النافذة الرئيسية مفقودة: Views\MainWindow.xaml
    set "MISSING_FILES=1"
) else (
    echo ✅ النافذة الرئيسية موجودة
)

echo.

REM التحقق من .NET
echo [2] التحقق من .NET...
dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ .NET متوفر - الإصدار:
    dotnet --version
    set "DOTNET_OK=1"
) else (
    echo ❌ .NET غير متوفر أو غير مثبت بشكل صحيح
    set "DOTNET_OK=0"
)

echo.

REM التحقق من Visual Studio Build Tools
echo [3] التحقق من أدوات البناء...
where msbuild >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ MSBuild متوفر
    set "MSBUILD_OK=1"
) else (
    echo ⚠️  MSBuild غير متوفر (قد يكون مطلوب لـ .NET Framework)
    set "MSBUILD_OK=0"
)

echo.

REM التحقق من SQL Server
echo [4] التحقق من قاعدة البيانات...
sqlcmd -S "(localdb)\MSSQLLocalDB" -Q "SELECT 1" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ LocalDB متوفر
    set "DB_OK=1"
) else (
    sqlcmd -S ".\SQLEXPRESS" -Q "SELECT 1" >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ SQL Server Express متوفر
        set "DB_OK=1"
    ) else (
        echo ⚠️  SQL Server غير متوفر - سيتم استخدام قاعدة بيانات بديلة
        set "DB_OK=0"
    )
)

echo.

REM تشخيص مشاكل المشروع
echo [5] تشخيص مشاكل المشروع...
if exist "obj" (
    echo ⚠️  مجلد obj موجود - قد يحتوي على ملفات قديمة
    set "CLEAN_NEEDED=1"
) else (
    set "CLEAN_NEEDED=0"
)

if exist "bin" (
    echo ⚠️  مجلد bin موجود - قد يحتوي على ملفات قديمة
    set "CLEAN_NEEDED=1"
)

echo.

REM عرض التشخيص
echo ========================================================
echo 📋 نتائج التشخيص:
echo ========================================================

if defined MISSING_FILES (
    echo ❌ مشكلة: ملفات أساسية مفقودة
    echo 🔧 الحل: إعادة إنشاء الملفات المفقودة
    echo.
)

if "%DOTNET_OK%"=="0" (
    echo ❌ مشكلة: .NET غير متوفر
    echo 🔧 الحل: تثبيت .NET 6.0 أو أحدث
    echo 📥 الرابط: https://dotnet.microsoft.com/download
    echo.
)

if "%DB_OK%"=="0" (
    echo ⚠️  تحذير: SQL Server غير متوفر
    echo 🔧 الحل: سيتم استخدام قاعدة بيانات بديلة
    echo.
)

if "%CLEAN_NEEDED%"=="1" (
    echo ⚠️  تحذير: ملفات بناء قديمة موجودة
    echo 🔧 الحل: تنظيف المشروع
    echo.
)

echo ========================================================
echo 🛠️  الحلول المقترحة:
echo ========================================================

REM تطبيق الحلول
set /p fix_choice="هل تريد تطبيق الحلول تلقائياً؟ (y/n): "
if /i "%fix_choice%"=="y" (
    echo.
    echo 🔧 جاري تطبيق الحلول...
    
    REM تنظيف المشروع
    if "%CLEAN_NEEDED%"=="1" (
        echo [الحل 1] تنظيف المشروع...
        if exist "bin" rmdir /s /q "bin" 2>nul
        if exist "obj" rmdir /s /q "obj" 2>nul
        echo ✅ تم تنظيف المشروع
    )
    
    REM إنشاء الملفات المفقودة
    if defined MISSING_FILES (
        echo [الحل 2] إنشاء الملفات المفقودة...
        call :CreateMissingFiles
    )
    
    REM استعادة الحزم
    if "%DOTNET_OK%"=="1" (
        echo [الحل 3] استعادة الحزم...
        dotnet restore AccountingSystem11.csproj
        if %errorlevel% equ 0 (
            echo ✅ تم استعادة الحزم بنجاح
        ) else (
            echo ❌ فشل في استعادة الحزم
        )
    )
    
    REM بناء المشروع
    if "%DOTNET_OK%"=="1" (
        echo [الحل 4] بناء المشروع...
        dotnet build AccountingSystem11.csproj -c Release
        if %errorlevel% equ 0 (
            echo ✅ تم بناء المشروع بنجاح
            set "BUILD_SUCCESS=1"
        ) else (
            echo ❌ فشل في بناء المشروع
            set "BUILD_SUCCESS=0"
        )
    )
    
    echo.
    echo ========================================================
    echo 🎉 انتهاء تطبيق الحلول
    echo ========================================================
    
    if "%BUILD_SUCCESS%"=="1" (
        echo ✅ تم حل جميع المشاكل بنجاح!
        echo 🚀 يمكنك الآن تشغيل البرنامج
        echo.
        set /p run_choice="هل تريد تشغيل البرنامج الآن؟ (y/n): "
        if /i "%run_choice%"=="y" (
            echo 🚀 جاري تشغيل البرنامج...
            dotnet run --project AccountingSystem11.csproj
        )
    ) else (
        echo ❌ لا تزال هناك مشاكل تحتاج حل يدوي
        goto :ManualSolutions
    )
) else (
    goto :ManualSolutions
)

goto :end

:ManualSolutions
echo.
echo ========================================================
echo 🔧 الحلول اليدوية:
echo ========================================================

if "%DOTNET_OK%"=="0" (
    echo 1️⃣ تثبيت .NET:
    echo    📥 اذهب إلى: https://dotnet.microsoft.com/download
    echo    📋 حمل .NET 6.0 Desktop Runtime أو أحدث
    echo    🔧 ثبت البرنامج وأعد تشغيل الكمبيوتر
    echo.
)

if "%DB_OK%"=="0" (
    echo 2️⃣ تثبيت SQL Server:
    echo    📥 اذهب إلى: https://www.microsoft.com/sql-server/sql-server-downloads
    echo    📋 حمل SQL Server Express (مجاني)
    echo    🔧 أو ثبت Visual Studio (يحتوي على LocalDB)
    echo.
)

echo 3️⃣ إعادة تحميل المشروع:
echo    🔄 أغلق Visual Studio
echo    🗑️ احذف مجلدات bin و obj
echo    🔄 أعد فتح Visual Studio
echo    📦 اضغط "Restore NuGet Packages"
echo.

echo 4️⃣ تشغيل من Command Line:
echo    💻 افتح Command Prompt كـ Administrator
echo    📁 انتقل لمجلد المشروع
echo    ⚡ شغل: dotnet restore
echo    🔨 شغل: dotnet build
echo    🚀 شغل: dotnet run
echo.

goto :end

:CreateMissingFiles
REM إنشاء الملفات المفقودة الأساسية
if not exist "Views" mkdir "Views"
if not exist "Models" mkdir "Models"
if not exist "Services" mkdir "Services"
if not exist "Data" mkdir "Data"

REM إنشاء App.xaml إذا كان مفقود
if not exist "App.xaml" (
    echo ^<Application x:Class="AccountingSystem11.App" > App.xaml
    echo              xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" >> App.xaml
    echo              xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"^> >> App.xaml
    echo ^</Application^> >> App.xaml
    echo ✅ تم إنشاء App.xaml
)

echo ✅ تم إنشاء الملفات المفقودة
goto :eof

:end
echo.
echo 📞 للمساعدة الإضافية:
echo    📧 البريد: <EMAIL>
echo    🌐 الموقع: https://system11.com
echo    📱 الهاتف: +20 xxx xxx xxxx
echo.
pause
