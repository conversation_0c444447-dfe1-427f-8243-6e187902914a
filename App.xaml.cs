using System;
using System.Globalization;
using System.Threading;
using System.Windows;
using AccountingSystem11.Data;
using AccountingSystem11.Services;
using AccountingSystem11.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace AccountingSystem11
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private ServiceProvider _serviceProvider;

        protected override void OnStartup(StartupEventArgs e)
        {
            // Set Arabic culture for the application
            var culture = new CultureInfo("ar-EG");
            Thread.CurrentThread.CurrentCulture = culture;
            Thread.CurrentThread.CurrentUICulture = culture;
            CultureInfo.DefaultThreadCurrentCulture = culture;
            CultureInfo.DefaultThreadCurrentUICulture = culture;

            // Configure services
            var services = new ServiceCollection();
            ConfigureServices(services);
            _serviceProvider = services.BuildServiceProvider();

            // Initialize database
            InitializeDatabase();

            base.OnStartup(e);
        }

        private void ConfigureServices(ServiceCollection services)
        {
            // SQL Server Database
            services.AddDbContext<AccountingDbContext>(options =>
                options.UseSqlServer(GetConnectionString()));

            // Services
            services.AddScoped<ICustomerService, CustomerService>();
            services.AddScoped<IProductService, ProductService>();
            services.AddScoped<IInvoiceService, InvoiceService>();
            services.AddScoped<ITaxService, TaxService>();
            services.AddScoped<IReportService, ReportService>();
            services.AddScoped<IExcelService, ExcelService>();
            services.AddScoped<IEInvoiceService, EInvoiceService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IInventoryService, InventoryService>();

            // ViewModels
            services.AddTransient<MainWindowViewModel>();
            services.AddTransient<DashboardViewModel>();
            services.AddTransient<InvoiceViewModel>();
            services.AddTransient<CustomerViewModel>();
            services.AddTransient<ProductViewModel>();
            services.AddTransient<ReportViewModel>();
            services.AddTransient<SettingsViewModel>();
        }

        private string GetConnectionString()
        {
            // SQL Server LocalDB - يعمل بدون تثبيت SQL Server كامل
            return @"Server=(localdb)\MSSQLLocalDB;Database=AccountingSystem11;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true;";
        }

        private async void InitializeDatabase()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<AccountingDbContext>();

                // Ensure database is created
                await context.Database.EnsureCreatedAsync();

                // Seed initial data if needed
                await DatabaseSeeder.SeedDataAsync(context);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public static T GetService<T>()
        {
            return ((App)Current)._serviceProvider.GetService<T>();
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _serviceProvider?.Dispose();
            base.OnExit(e);
        }
    }
}
