using System;
using System.Globalization;
using System.Threading;
using System.Windows;
using AccountingSystem11.Data;
using AccountingSystem11.Services;
using AccountingSystem11.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace AccountingSystem11
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private ServiceProvider _serviceProvider;

        protected override void OnStartup(StartupEventArgs e)
        {
            // Set Arabic culture for the application
            var culture = new CultureInfo("ar-EG");
            Thread.CurrentThread.CurrentCulture = culture;
            Thread.CurrentThread.CurrentUICulture = culture;
            CultureInfo.DefaultThreadCurrentCulture = culture;
            CultureInfo.DefaultThreadCurrentUICulture = culture;

            // Configure services
            var services = new ServiceCollection();
            ConfigureServices(services);
            _serviceProvider = services.BuildServiceProvider();

            // Initialize database
            InitializeDatabase();

            base.OnStartup(e);
        }

        private void ConfigureServices(ServiceCollection services)
        {
            // Access Database Service
            services.AddSingleton<AccessDataService>();

            // Database (keeping original for future use)
            services.AddDbContext<AccountingDbContext>(options =>
                options.UseSqlServer(GetConnectionString()));

            // Services
            services.AddScoped<ICustomerService, CustomerService>();
            services.AddScoped<IProductService, ProductService>();
            services.AddScoped<IInvoiceService, InvoiceService>();
            services.AddScoped<ITaxService, TaxService>();
            services.AddScoped<IReportService, ReportService>();
            services.AddScoped<IExcelService, ExcelService>();
            services.AddScoped<IEInvoiceService, EInvoiceService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IInventoryService, InventoryService>();

            // ViewModels
            services.AddTransient<MainWindowViewModel>();
            services.AddTransient<DashboardViewModel>();
            services.AddTransient<AccessDashboardViewModel>();
            services.AddTransient<InvoiceViewModel>();
            services.AddTransient<CustomerViewModel>();
            services.AddTransient<ProductViewModel>();
            services.AddTransient<ReportViewModel>();
            services.AddTransient<SettingsViewModel>();
        }

        private string GetConnectionString()
        {
            // دعم جميع إصدارات .NET مع خيارات متعددة لقاعدة البيانات

#if NET48
            // .NET Framework 4.8 - استخدام SQL Server Express أو LocalDB
            var connectionStrings = new[]
            {
                @"Server=.\SQLEXPRESS;Database=AccountingSystem11;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=true;",
                @"Server=(localdb)\MSSQLLocalDB;Database=AccountingSystem11;Integrated Security=true;MultipleActiveResultSets=true;TrustServerCertificate=true;",
                @"Data Source=.\SQLEXPRESS;Initial Catalog=AccountingSystem11;Integrated Security=True;MultipleActiveResultSets=True;TrustServerCertificate=True;",
                @"Data Source=(localdb)\MSSQLLocalDB;Initial Catalog=AccountingSystem11;Integrated Security=True;MultipleActiveResultSets=True;TrustServerCertificate=True;"
            };
#else
            // .NET 6+ - استخدام LocalDB أو SQL Server
            var connectionStrings = new[]
            {
                @"Server=(localdb)\MSSQLLocalDB;Database=AccountingSystem11;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true;",
                @"Server=.\SQLEXPRESS;Database=AccountingSystem11;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true;",
                @"Server=localhost;Database=AccountingSystem11;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true;",
                @"Data Source=(localdb)\MSSQLLocalDB;Initial Catalog=AccountingSystem11;Integrated Security=True;MultipleActiveResultSets=True;TrustServerCertificate=True;"
            };
#endif

            // جرب كل connection string حتى تجد واحد يعمل
            foreach (var connectionString in connectionStrings)
            {
                try
                {
                    using var connection = new Microsoft.Data.SqlClient.SqlConnection(connectionString);
                    connection.Open();
                    connection.Close();
                    return connectionString; // إذا نجح الاتصال، استخدم هذا
                }
                catch
                {
                    // جرب التالي
                    continue;
                }
            }

            // إذا فشلت جميع المحاولات، استخدم الافتراضي
            return connectionStrings[0];
        }

        private async void InitializeDatabase()
        {
            try
            {
                // تهيئة قاعدة بيانات Access
                var accessDataService = _serviceProvider.GetRequiredService<AccessDataService>();
                var success = await accessDataService.InitializeAsync();

                if (!success)
                {
                    MessageBox.Show("تعذر تهيئة قاعدة البيانات المحلية", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }

                // الاحتفاظ بالكود الأصلي كخيار احتياطي
                /*
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<AccountingDbContext>();

                // Ensure database is created
                context.Database.EnsureCreated();

                // Seed initial data if needed
                DatabaseSeeder.SeedData(context);
                */
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public static T GetService<T>()
        {
            return ((App)Current)._serviceProvider.GetService<T>();
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _serviceProvider?.Dispose();
            base.OnExit(e);
        }
    }
}
