@echo off
title نظام المحاسبة 11 - SQL Server
color 0A
echo.
echo ========================================================
echo        نظام المحاسبة 11 - SQL Server LocalDB
echo ========================================================
echo.

echo 🗃️ تم التحويل إلى SQL Server LocalDB!
echo ✅ لا يحتاج Access Database Engine
echo 🚀 أداء أسرع واستقرار أعلى
echo 💾 قاعدة بيانات أكثر قوة
echo.

echo [1] التحقق من .NET...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET غير مثبت
    echo 📥 جاري فتح رابط التحميل...
    start https://dotnet.microsoft.com/download/dotnet/6.0
    echo.
    echo بعد تثبيت .NET، أعد تشغيل هذا الملف
    pause
    exit /b 1
)
echo ✅ .NET مثبت - الإصدار:
dotnet --version

echo.
echo [2] التحقق من SQL Server LocalDB...
sqllocaldb info MSSQLLocalDB >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ SQL Server LocalDB متوفر
) else (
    echo ⚠️  SQL Server LocalDB غير متوفر
    echo 📥 جاري تثبيت SQL Server Express LocalDB...
    echo.
    echo 🔗 سيتم فتح رابط التحميل...
    start https://www.microsoft.com/en-us/download/details.aspx?id=101064
    echo.
    echo 📋 تعليمات التثبيت:
    echo    1. حمل "SQL Server Express LocalDB"
    echo    2. ثبته (اختر Basic Installation)
    echo    3. أعد تشغيل الجهاز
    echo    4. أعد تشغيل هذا الملف
    echo.
    echo 💡 أو يمكنك تثبيت Visual Studio (يحتوي على LocalDB)
    pause
    exit /b 1
)

echo.
echo [3] تنظيف المشروع...
dotnet clean AccountingSystem11.csproj --verbosity minimal >nul 2>&1
echo ✅ تم تنظيف المشروع

echo.
echo [4] استعادة الحزم...
dotnet restore AccountingSystem11.csproj --verbosity minimal >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ فشل في استعادة الحزم
    echo 🔧 جرب تشغيل الملف كـ Administrator
    pause
    exit /b 1
)
echo ✅ تم استعادة الحزم

echo.
echo [5] بناء المشروع...
dotnet build AccountingSystem11.csproj --configuration Release --verbosity minimal >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ فشل البناء
    echo.
    echo 🔧 جرب الحلول التالية:
    echo    1. شغل الملف كـ Administrator
    echo    2. أغلق مضاد الفيروسات مؤقتاً
    echo    3. تأكد من وجود مساحة كافية
    echo.
    echo 📋 تفاصيل أكثر:
    dotnet build AccountingSystem11.csproj --verbosity normal
    pause
    exit /b 1
)
echo ✅ تم بناء المشروع بنجاح

echo.
echo [6] إعداد قاعدة البيانات...
echo 🗃️ جاري إنشاء قاعدة البيانات...

REM بدء LocalDB إذا لم يكن يعمل
sqllocaldb start MSSQLLocalDB >nul 2>&1

echo ✅ تم إعداد قاعدة البيانات

echo.
echo ========================================================
echo 🎉 جاري تشغيل نظام المحاسبة 11...
echo ========================================================
echo.
echo 🗃️ قاعدة البيانات: SQL Server LocalDB
echo 📁 مكان البيانات: LocalDB (مدارة تلقائياً)
echo.
echo 🔐 بيانات تسجيل الدخول:
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo.
echo 🎯 المميزات الجديدة:
echo    ✅ لا يحتاج Access Database Engine
echo    ✅ أداء أسرع وأكثر استقراراً
echo    ✅ دعم أفضل للبيانات الكبيرة
echo    ✅ نسخ احتياطي أسهل
echo    ✅ أمان محسن
echo.
echo ⚠️ ملاحظات:
echo    • قاعدة البيانات ستُنشأ تلقائياً عند أول تشغيل
echo    • البيانات محفوظة في LocalDB
echo    • لا تحتاج اتصال إنترنت للعمل
echo.
echo ========================================================

timeout /t 3 /nobreak >nul

echo 🚀 بدء التشغيل...
dotnet run --project AccountingSystem11.csproj --configuration Release

echo.
echo ✅ تم إغلاق البرنامج
echo 💾 البيانات محفوظة في SQL Server LocalDB
echo.

REM عرض معلومات قاعدة البيانات
echo 📊 معلومات قاعدة البيانات:
sqllocaldb info MSSQLLocalDB 2>nul | findstr "State:"
if %errorlevel% equ 0 (
    echo ✅ LocalDB يعمل بشكل طبيعي
) else (
    echo ⚠️  LocalDB متوقف
)

echo.
pause
