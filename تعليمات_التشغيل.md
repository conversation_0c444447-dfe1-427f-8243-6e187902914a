# 🚀 دليل تشغيل نظام المحاسبة 11

## الطريقة الأولى: التشغيل السريع (مُوصى بها)

### 1. تحميل وتثبيت .NET Runtime
```
🌐 اذهب إلى: https://dotnet.microsoft.com/download/dotnet/6.0
📥 حمل: .NET Desktop Runtime 6.0.x (x64)
⚙️ ثبت البرنامج على جهازك
```

### 2. تشغيل البرنامج
```
📁 اذهب لمجلد المشروع
🖱️ اضغط دبل كليك على ملف "run.bat"
⏳ انتظر حتى يكتمل التحميل
🎉 سيفتح البرنامج تلقائياً
```

## الطريقة الثانية: التشغيل اليدوي

### 1. فتح سطر الأوامر
```
⌨️ اضغط Windows + R
💻 اكتب: cmd
📂 انتقل للمجلد: cd "d:\اتن"
```

### 2. تنفيذ الأوامر
```bash
# تحقق من .NET
dotnet --version

# استعادة الحزم
dotnet restore

# بناء المشروع  
dotnet build

# تشغيل البرنامج
dotnet run
```

## 🔐 بيانات تسجيل الدخول

```
👤 اسم المستخدم: admin
🔑 كلمة المرور: admin123
```

⚠️ **مهم**: غير كلمة المرور فور تسجيل الدخول الأول!

## 🖥️ ما ستراه في البرنامج

### الشاشة الرئيسية:
- 📊 لوحة تحكم بالإحصائيات
- 💰 بطاقات المبيعات والأرباح  
- 📈 مخططات بيانية
- 🔔 تنبيهات المخزون

### القوائم الجانبية:
- 🏠 لوحة التحكم
- 🧾 الفواتير
- 👥 العملاء
- 📦 المنتجات
- 🏪 المخزون
- 📋 التقارير
- ⚙️ الإعدادات

## ❌ حل المشاكل الشائعة

### المشكلة: "dotnet is not recognized"
```
✅ الحل: تثبيت .NET 6.0 Desktop Runtime
🔗 الرابط: https://dotnet.microsoft.com/download/dotnet/6.0
```

### المشكلة: خطأ في قاعدة البيانات
```
✅ الحل: البرنامج ينشئ قاعدة البيانات تلقائياً
⏳ انتظر قليلاً في المرة الأولى
```

### المشكلة: البرنامج لا يفتح
```
✅ الحل 1: شغل cmd كـ Administrator
✅ الحل 2: تأكد من تثبيت .NET Runtime
✅ الحل 3: أعد تشغيل الجهاز
```

### المشكلة: رسالة خطأ "Port already in use"
```
✅ الحل: أغلق أي نسخة أخرى من البرنامج
✅ أو أعد تشغيل الجهاز
```

## 📞 الدعم الفني

إذا واجهت أي مشكلة:
1. تأكد من تثبيت .NET 6.0 Desktop Runtime
2. جرب تشغيل cmd كـ Administrator  
3. تأكد من وجود جميع ملفات المشروع
4. أعد تشغيل الجهاز

## 🎯 نصائح مهمة

### للاستخدام الأمثل:
- 💾 احفظ نسخة احتياطية من البيانات دورياً
- 🔒 غير كلمة المرور الافتراضية
- 📱 استخدم دقة شاشة 1920x1080 أو أعلى
- 🖱️ استخدم الماوس للتنقل السريع

### للأداء الأفضل:
- 💻 8 GB RAM أو أكثر
- 💾 SSD للتخزين
- 🌐 اتصال إنترنت للتحديثات

---

**نظام المحاسبة 11** - حلول محاسبية ذكية 🇪🇬
