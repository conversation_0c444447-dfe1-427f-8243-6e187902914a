using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AccountingSystem11.Models
{
    /// <summary>
    /// User entity for system authentication and authorization
    /// </summary>
    public class User : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string Username { get; set; }

        [Required]
        [MaxLength(100)]
        public string FullName { get; set; }

        [Required]
        [MaxLength(100)]
        public string Email { get; set; }

        [Required]
        [MaxLength(255)]
        public string PasswordHash { get; set; }

        [MaxLength(15)]
        public string Phone { get; set; }

        [MaxLength(15)]
        public string Mobile { get; set; }

        /// <summary>
        /// User role
        /// </summary>
        public UserRole Role { get; set; } = UserRole.User;

        /// <summary>
        /// Whether user is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Whether user must change password on next login
        /// </summary>
        public bool MustChangePassword { get; set; } = false;

        /// <summary>
        /// Last login date
        /// </summary>
        public DateTime? LastLoginDate { get; set; }

        /// <summary>
        /// Last login IP address
        /// </summary>
        [MaxLength(45)]
        public string LastLoginIp { get; set; }

        /// <summary>
        /// Failed login attempts
        /// </summary>
        public int FailedLoginAttempts { get; set; } = 0;

        /// <summary>
        /// Account locked until
        /// </summary>
        public DateTime? LockedUntil { get; set; }

        /// <summary>
        /// Password reset token
        /// </summary>
        [MaxLength(100)]
        public string PasswordResetToken { get; set; }

        /// <summary>
        /// Password reset token expiry
        /// </summary>
        public DateTime? PasswordResetTokenExpiry { get; set; }

        /// <summary>
        /// User preferences (JSON)
        /// </summary>
        [MaxLength(1000)]
        public string Preferences { get; set; }

        /// <summary>
        /// User notes
        /// </summary>
        [MaxLength(500)]
        public string Notes { get; set; }

        // Navigation properties
        public virtual ICollection<UserPermission> Permissions { get; set; } = new List<UserPermission>();
        public virtual ICollection<UserSession> Sessions { get; set; } = new List<UserSession>();

        /// <summary>
        /// Check if user has specific permission
        /// </summary>
        /// <param name="permission">Permission to check</param>
        /// <returns>True if user has permission</returns>
        public bool HasPermission(Permission permission)
        {
            // Admin has all permissions
            if (Role == UserRole.Admin)
                return true;

            // Check specific permissions
            foreach (var userPermission in Permissions)
            {
                if (userPermission.Permission == permission && userPermission.IsGranted)
                    return true;
            }

            return false;
        }

        /// <summary>
        /// Check if account is locked
        /// </summary>
        /// <returns>True if account is locked</returns>
        public bool IsLocked()
        {
            return LockedUntil.HasValue && LockedUntil.Value > DateTime.Now;
        }
    }

    public enum UserRole
    {
        Admin = 1,          // مدير النظام
        Manager = 2,        // مدير
        Accountant = 3,     // محاسب
        Cashier = 4,        // كاشير
        User = 5,           // مستخدم عادي
        Viewer = 6          // مشاهد فقط
    }
}
