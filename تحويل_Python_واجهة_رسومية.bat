@echo off
title تحويل Python إلى EXE - واجهة رسومية
color 0C
echo.
echo ========================================================
echo    تحويل Python إلى EXE - واجهة رسومية سهلة
echo ========================================================
echo.

echo 🎨 أداة تحويل بواجهة رسومية سهلة!
echo ✅ لا تحتاج كتابة أوامر
echo 🖱️ كله بالضغط على الأزرار
echo.

echo [1] التحقق من Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت
    echo 📥 يرجى تثبيت Python أولاً من: https://www.python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python مثبت

echo.
echo [2] التحقق من auto-py-to-exe...
python -c "import auto_py_to_exe" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ auto-py-to-exe غير مثبت
    echo 📦 جاري تثبيت auto-py-to-exe...
    pip install auto-py-to-exe
    if %errorlevel% neq 0 (
        echo ❌ فشل التثبيت
        echo 🔧 جرب: pip install --user auto-py-to-exe
        pause
        exit /b 1
    )
)
echo ✅ auto-py-to-exe متوفر

echo.
echo [3] التحقق من ملف البرنامج...
if not exist "accounting_system_python.py" (
    echo ❌ ملف البرنامج غير موجود
    echo يرجى التأكد من وجود ملف accounting_system_python.py
    pause
    exit /b 1
)
echo ✅ ملف البرنامج موجود

echo.
echo ========================================================
echo 🎨 فتح واجهة التحويل الرسومية...
echo ========================================================
echo.

echo 📋 تعليمات الاستخدام:
echo.
echo 1️⃣ في خانة "Script Location":
echo    • اضغط "Browse" واختر ملف: accounting_system_python.py
echo.
echo 2️⃣ في قسم "Onefile":
echo    • اختر "One File" لإنشاء ملف exe واحد
echo.
echo 3️⃣ في قسم "Console Window":
echo    • اختر "Window Based" لإخفاء نافذة الأوامر
echo.
echo 4️⃣ في قسم "Additional Files":
echo    • لا تحتاج إضافة ملفات (اختياري)
echo.
echo 5️⃣ في قسم "Advanced":
echo    • Name: نظام_المحاسبة_11_Python
echo    • Output Directory: اختر مجلد للحفظ
echo.
echo 6️⃣ اضغط "CONVERT .PY TO .EXE" في الأسفل
echo.
echo 7️⃣ انتظر انتهاء التحويل (قد يستغرق دقائق)
echo.

timeout /t 5 /nobreak >nul

echo 🚀 فتح الواجهة الرسومية...
auto-py-to-exe

echo.
echo ✅ تم إغلاق واجهة التحويل
echo.
echo 💡 إذا تم التحويل بنجاح:
echo    • ستجد ملف .exe في المجلد الذي اخترته
echo    • الملف سيعمل بدون تثبيت Python
echo    • يمكنك نسخه لأي جهاز آخر
echo.
pause
