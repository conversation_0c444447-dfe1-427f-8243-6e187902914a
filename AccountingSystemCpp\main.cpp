#include <windows.h>
#include <commctrl.h>
#include <commdlg.h>
#include <shellapi.h>
#include <iostream>
#include <string>
#include <vector>
#include <map>
#include <fstream>
#include <sstream>
#include <memory>
#include "resource.h"
#include "database.h"

#pragma comment(lib, "comctl32.lib")
#pragma comment(lib, "shell32.lib")

// Global variables
HINSTANCE g_hInst;
HWND g_hMainWnd;
std::unique_ptr<Database> g_database;

// Window class names
const wchar_t *MAIN_WINDOW_CLASS = L"AccountingSystem11MainWindow";
const wchar_t *CHILD_WINDOW_CLASS = L"AccountingSystem11ChildWindow";

// Control IDs
#define ID_MENU_CUSTOMERS 1001
#define ID_MENU_PRODUCTS 1002
#define ID_MENU_INVOICES 1003
#define ID_MENU_REPORTS 1004
#define ID_MENU_SETTINGS 1005
#define ID_MENU_EXIT 1006
#define ID_MENU_ABOUT 1007

#define ID_BTN_ADD_CUSTOMER 2001
#define ID_BTN_EDIT_CUSTOMER 2002
#define ID_BTN_DEL_CUSTOMER 2003
#define ID_LIST_CUSTOMERS 2004

#define ID_STATIC_WELCOME 3001
#define ID_STATIC_STATS 3002

// Forward declarations
LRESULT CALLBACK MainWndProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam);
LRESULT CALLBACK ChildWndProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam);
INT_PTR CALLBACK AboutDlgProc(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam);
INT_PTR CALLBACK CustomerDlgProc(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam);

void CreateMainWindow();
void CreateMenuBar(HWND hwnd);
void ShowDashboard(HWND hwnd);
void ShowCustomers(HWND hwnd);
void ShowProducts(HWND hwnd);
void ShowInvoices(HWND hwnd);
void ShowReports(HWND hwnd);
void ShowSettings(HWND hwnd);
void UpdateStatusBar(HWND hwnd, const std::wstring &text);

// Utility functions
std::wstring GetAppPath();
std::wstring LoadStringResource(UINT id);
void ShowErrorMessage(const std::wstring &message);
void ShowInfoMessage(const std::wstring &message);

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    g_hInst = hInstance;

    // Initialize common controls
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_LISTVIEW_CLASSES | ICC_TREEVIEW_CLASSES | ICC_BAR_CLASSES | ICC_TAB_CLASSES;
    InitCommonControlsEx(&icex);

    // Initialize database
    try
    {
        g_database = std::make_unique<Database>();
        if (!g_database->Initialize())
        {
            ShowErrorMessage(L"فشل في تهيئة قاعدة البيانات");
            return -1;
        }
    }
    catch (const std::exception &e)
    {
        std::wstring error = L"خطأ في قاعدة البيانات: ";
        error += std::wstring(e.what(), e.what() + strlen(e.what()));
        ShowErrorMessage(error);
        return -1;
    }

    // Register window classes
    WNDCLASSEX wcex = {};
    wcex.cbSize = sizeof(WNDCLASSEX);
    wcex.style = CS_HREDRAW | CS_VREDRAW;
    wcex.lpfnWndProc = MainWndProc;
    wcex.hInstance = hInstance;
    wcex.hIcon = LoadIcon(hInstance, MAKEINTRESOURCE(IDI_MAIN));
    wcex.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wcex.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wcex.lpszMenuName = MAKEINTRESOURCE(IDR_MAINMENU);
    wcex.lpszClassName = MAIN_WINDOW_CLASS;
    wcex.hIconSm = LoadIcon(hInstance, MAKEINTRESOURCE(IDI_SMALL));

    if (!RegisterClassEx(&wcex))
    {
        ShowErrorMessage(L"فشل في تسجيل فئة النافذة الرئيسية");
        return -1;
    }

    // Create main window
    CreateMainWindow();

    if (!g_hMainWnd)
    {
        ShowErrorMessage(L"فشل في إنشاء النافذة الرئيسية");
        return -1;
    }

    ShowWindow(g_hMainWnd, nCmdShow);
    UpdateWindow(g_hMainWnd);

    // Show welcome message
    ShowInfoMessage(L"مرحباً بك في نظام المحاسبة 11 - إصدار C++\n\nبيانات تسجيل الدخول:\nاسم المستخدم: admin\nكلمة المرور: admin123");

    // Message loop
    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0))
    {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    return (int)msg.wParam;
}

void CreateMainWindow()
{
    g_hMainWnd = CreateWindowEx(
        WS_EX_APPWINDOW,
        MAIN_WINDOW_CLASS,
        L"نظام المحاسبة 11 - إصدار C++",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT,
        1200, 800,
        nullptr, nullptr, g_hInst, nullptr);
}

LRESULT CALLBACK MainWndProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam)
{
    switch (msg)
    {
    case WM_CREATE:
        ShowDashboard(hwnd);
        UpdateStatusBar(hwnd, L"جاهز - نظام المحاسبة 11");
        break;

    case WM_COMMAND:
        switch (LOWORD(wParam))
        {
        case ID_MENU_CUSTOMERS:
            ShowCustomers(hwnd);
            break;
        case ID_MENU_PRODUCTS:
            ShowProducts(hwnd);
            break;
        case ID_MENU_INVOICES:
            ShowInvoices(hwnd);
            break;
        case ID_MENU_REPORTS:
            ShowReports(hwnd);
            break;
        case ID_MENU_SETTINGS:
            ShowSettings(hwnd);
            break;
        case ID_MENU_ABOUT:
            DialogBox(g_hInst, MAKEINTRESOURCE(IDD_ABOUT), hwnd, AboutDlgProc);
            break;
        case ID_MENU_EXIT:
            PostQuitMessage(0);
            break;
        case ID_BTN_ADD_CUSTOMER:
            DialogBox(g_hInst, MAKEINTRESOURCE(IDD_CUSTOMER), hwnd, CustomerDlgProc);
            break;
        }
        break;

    case WM_SIZE:
        // Handle window resizing
        break;

    case WM_DESTROY:
        PostQuitMessage(0);
        break;

    default:
        return DefWindowProc(hwnd, msg, wParam, lParam);
    }
    return 0;
}

void ShowDashboard(HWND hwnd)
{
    // Clear existing controls
    HWND hChild = GetWindow(hwnd, GW_CHILD);
    while (hChild)
    {
        HWND hNext = GetWindow(hChild, GW_HWNDNEXT);
        DestroyWindow(hChild);
        hChild = hNext;
    }

    // Create dashboard controls
    RECT rect;
    GetClientRect(hwnd, &rect);

    // Welcome message
    CreateWindow(L"STATIC", L"🏢 مرحباً بك في نظام المحاسبة 11 - إصدار C++",
                 WS_VISIBLE | WS_CHILD | SS_CENTER,
                 50, 50, rect.right - 100, 40,
                 hwnd, (HMENU)ID_STATIC_WELCOME, g_hInst, nullptr);

    // Statistics
    std::wstring stats = L"📊 إحصائيات النظام:\n";
    stats += L"👥 العملاء: " + std::to_wstring(g_database->GetCustomerCount()) + L"\n";
    stats += L"📦 المنتجات: " + std::to_wstring(g_database->GetProductCount()) + L"\n";
    stats += L"🧾 الفواتير: " + std::to_wstring(g_database->GetInvoiceCount()) + L"\n";
    stats += L"💰 إجمالي المبيعات: " + std::to_wstring(g_database->GetTotalSales()) + L" جنيه";

    CreateWindow(L"STATIC", stats.c_str(),
                 WS_VISIBLE | WS_CHILD | SS_LEFT,
                 50, 120, rect.right - 100, 200,
                 hwnd, (HMENU)ID_STATIC_STATS, g_hInst, nullptr);

    // System info
    std::wstring sysInfo = L"💻 معلومات النظام:\n";
    sysInfo += L"🔧 اللغة: C++ Native\n";
    sysInfo += L"🗃️ قاعدة البيانات: SQLite محلية\n";
    sysInfo += L"⚡ الأداء: سريع جداً\n";
    sysInfo += L"💾 الذاكرة: محسنة\n";
    sysInfo += L"🚀 التوافق: جميع إصدارات Windows";

    CreateWindow(L"STATIC", sysInfo.c_str(),
                 WS_VISIBLE | WS_CHILD | SS_LEFT,
                 50, 350, rect.right - 100, 150,
                 hwnd, nullptr, g_hInst, nullptr);

    UpdateStatusBar(hwnd, L"لوحة التحكم - جاهز");
}

void ShowCustomers(HWND hwnd)
{
    // Clear existing controls
    HWND hChild = GetWindow(hwnd, GW_CHILD);
    while (hChild)
    {
        HWND hNext = GetWindow(hChild, GW_HWNDNEXT);
        DestroyWindow(hChild);
        hChild = hNext;
    }

    RECT rect;
    GetClientRect(hwnd, &rect);

    // Title
    CreateWindow(L"STATIC", L"👥 إدارة العملاء",
                 WS_VISIBLE | WS_CHILD | SS_CENTER,
                 50, 20, rect.right - 100, 30,
                 hwnd, nullptr, g_hInst, nullptr);

    // Buttons
    CreateWindow(L"BUTTON", L"➕ إضافة عميل",
                 WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                 50, 60, 120, 30,
                 hwnd, (HMENU)ID_BTN_ADD_CUSTOMER, g_hInst, nullptr);

    CreateWindow(L"BUTTON", L"✏️ تعديل",
                 WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                 180, 60, 100, 30,
                 hwnd, (HMENU)ID_BTN_EDIT_CUSTOMER, g_hInst, nullptr);

    CreateWindow(L"BUTTON", L"🗑️ حذف",
                 WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                 290, 60, 100, 30,
                 hwnd, (HMENU)ID_BTN_DEL_CUSTOMER, g_hInst, nullptr);

    // Customer list
    HWND hList = CreateWindow(WC_LISTVIEW, L"",
                              WS_VISIBLE | WS_CHILD | LVS_REPORT | LVS_SINGLESEL | WS_BORDER,
                              50, 100, rect.right - 100, rect.bottom - 200,
                              hwnd, (HMENU)ID_LIST_CUSTOMERS, g_hInst, nullptr);

    // Add columns
    LVCOLUMN lvc = {};
    lvc.mask = LVCF_TEXT | LVCF_WIDTH;
    lvc.cx = 100;
    lvc.pszText = (LPWSTR)L"الرقم";
    ListView_InsertColumn(hList, 0, &lvc);

    lvc.pszText = (LPWSTR)L"الاسم";
    lvc.cx = 150;
    ListView_InsertColumn(hList, 1, &lvc);

    lvc.pszText = (LPWSTR)L"الشركة";
    lvc.cx = 150;
    ListView_InsertColumn(hList, 2, &lvc);

    lvc.pszText = (LPWSTR)L"الهاتف";
    lvc.cx = 120;
    ListView_InsertColumn(hList, 3, &lvc);

    lvc.pszText = (LPWSTR)L"البريد الإلكتروني";
    lvc.cx = 200;
    ListView_InsertColumn(hList, 4, &lvc);

    // Load customers
    auto customers = g_database->GetCustomers();
    for (size_t i = 0; i < customers.size(); ++i)
    {
        LVITEM lvi = {};
        lvi.mask = LVIF_TEXT;
        lvi.iItem = (int)i;
        lvi.pszText = (LPWSTR)std::to_wstring(customers[i].id).c_str();
        ListView_InsertItem(hList, &lvi);

        ListView_SetItemText(hList, (int)i, 1, (LPWSTR)customers[i].name.c_str());
        ListView_SetItemText(hList, (int)i, 2, (LPWSTR)customers[i].company.c_str());
        ListView_SetItemText(hList, (int)i, 3, (LPWSTR)customers[i].phone.c_str());
        ListView_SetItemText(hList, (int)i, 4, (LPWSTR)customers[i].email.c_str());
    }

    UpdateStatusBar(hwnd, L"العملاء - " + std::to_wstring(customers.size()) + L" عميل");
}

void ShowProducts(HWND hwnd)
{
    // Clear existing controls
    HWND hChild = GetWindow(hwnd, GW_CHILD);
    while (hChild)
    {
        HWND hNext = GetWindow(hChild, GW_HWNDNEXT);
        DestroyWindow(hChild);
        hChild = hNext;
    }

    CreateWindow(L"STATIC", L"📦 إدارة المنتجات - قريباً",
                 WS_VISIBLE | WS_CHILD | SS_CENTER,
                 50, 200, 400, 30,
                 hwnd, nullptr, g_hInst, nullptr);

    UpdateStatusBar(hwnd, L"المنتجات - قيد التطوير");
}

void ShowInvoices(HWND hwnd)
{
    // Clear existing controls
    HWND hChild = GetWindow(hwnd, GW_CHILD);
    while (hChild)
    {
        HWND hNext = GetWindow(hChild, GW_HWNDNEXT);
        DestroyWindow(hChild);
        hChild = hNext;
    }

    CreateWindow(L"STATIC", L"🧾 إدارة الفواتير - قريباً",
                 WS_VISIBLE | WS_CHILD | SS_CENTER,
                 50, 200, 400, 30,
                 hwnd, nullptr, g_hInst, nullptr);

    UpdateStatusBar(hwnd, L"الفواتير - قيد التطوير");
}

void ShowReports(HWND hwnd)
{
    // Clear existing controls
    HWND hChild = GetWindow(hwnd, GW_CHILD);
    while (hChild)
    {
        HWND hNext = GetWindow(hChild, GW_HWNDNEXT);
        DestroyWindow(hChild);
        hChild = hNext;
    }

    CreateWindow(L"STATIC", L"📊 التقارير - قريباً",
                 WS_VISIBLE | WS_CHILD | SS_CENTER,
                 50, 200, 400, 30,
                 hwnd, nullptr, g_hInst, nullptr);

    UpdateStatusBar(hwnd, L"التقارير - قيد التطوير");
}

void ShowSettings(HWND hwnd)
{
    // Clear existing controls
    HWND hChild = GetWindow(hwnd, GW_CHILD);
    while (hChild)
    {
        HWND hNext = GetWindow(hChild, GW_HWNDNEXT);
        DestroyWindow(hChild);
        hChild = hNext;
    }

    CreateWindow(L"STATIC", L"⚙️ الإعدادات - قريباً",
                 WS_VISIBLE | WS_CHILD | SS_CENTER,
                 50, 200, 400, 30,
                 hwnd, nullptr, g_hInst, nullptr);

    UpdateStatusBar(hwnd, L"الإعدادات - قيد التطوير");
}

void UpdateStatusBar(HWND hwnd, const std::wstring &text)
{
    // This would update a status bar if we had one
    SetWindowText(hwnd, (L"نظام المحاسبة 11 - " + text).c_str());
}

INT_PTR CALLBACK AboutDlgProc(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam)
{
    switch (message)
    {
    case WM_INITDIALOG:
        return TRUE;
    case WM_COMMAND:
        if (LOWORD(wParam) == IDOK || LOWORD(wParam) == IDCANCEL)
        {
            EndDialog(hDlg, LOWORD(wParam));
            return TRUE;
        }
        break;
    }
    return FALSE;
}

INT_PTR CALLBACK CustomerDlgProc(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam)
{
    switch (message)
    {
    case WM_INITDIALOG:
        return TRUE;
    case WM_COMMAND:
        if (LOWORD(wParam) == IDOK)
        {
            // Save customer data
            EndDialog(hDlg, LOWORD(wParam));
            return TRUE;
        }
        else if (LOWORD(wParam) == IDCANCEL)
        {
            EndDialog(hDlg, LOWORD(wParam));
            return TRUE;
        }
        break;
    }
    return FALSE;
}

std::wstring GetAppPath()
{
    wchar_t path[MAX_PATH];
    GetModuleFileName(nullptr, path, MAX_PATH);
    std::wstring fullPath(path);
    return fullPath.substr(0, fullPath.find_last_of(L"\\"));
}

void ShowErrorMessage(const std::wstring &message)
{
    MessageBox(nullptr, message.c_str(), L"خطأ", MB_OK | MB_ICONERROR);
}

void ShowInfoMessage(const std::wstring &message)
{
    MessageBox(nullptr, message.c_str(), L"معلومات", MB_OK | MB_ICONINFORMATION);
}
