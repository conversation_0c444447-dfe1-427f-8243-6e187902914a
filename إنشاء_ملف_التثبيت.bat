@echo off
title إنشاء ملف التثبيت - نظام المحاسبة 11
color 0A
echo.
echo ========================================================
echo       إنشاء ملف التثبيت - نظام المحاسبة 11
echo ========================================================
echo.

echo 📦 جاري إنشاء ملف تثبيت احترافي...
echo.

echo [1] تنظيف وإعداد المجلدات...
if exist "Setup\Output" rmdir /s /q "Setup\Output" 2>nul
if not exist "Setup\Files" mkdir "Setup\Files"
if not exist "Setup\Output" mkdir "Setup\Output"

echo [2] بناء النسخة النهائية...
dotnet publish AccountingSystem11.csproj -c Release -r win-x64 --self-contained false -p:PublishSingleFile=true -o "Setup\Files"

if %errorlevel% neq 0 (
    echo ❌ فشل في بناء المشروع
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح

echo [3] إنشاء ملفات التثبيت...

REM إنشاء ملف الترخيص
(
echo EULA - End User License Agreement
echo نظام المحاسبة 11
echo.
echo © 2024 شركة 11 للبرمجيات. جميع الحقوق محفوظة.
echo.
echo بتثبيت هذا البرنامج، فإنك توافق على الشروط التالية:
echo.
echo 1. هذا البرنامج مرخص للاستخدام وليس للبيع
echo 2. يُمنع نسخ أو توزيع البرنامج بدون إذن
echo 3. الشركة غير مسؤولة عن أي أضرار
echo 4. يحق للشركة تحديث الشروط في أي وقت
echo.
echo للدعم الفني: <EMAIL>
echo الموقع الإلكتروني: www.system11.com
) > "Setup\License.txt"

REM إنشاء ملف README
(
echo نظام المحاسبة 11
echo ================
echo.
echo شكراً لاختيارك نظام المحاسبة 11!
echo.
echo هذا النظام مصمم خصيصاً للشركات المصرية
echo ويدعم جميع متطلبات المحاسبة المصرية.
echo.
echo المتطلبات:
echo - Windows 10 أو أحدث
echo - .NET 6.0 أو أحدث
echo - 4 GB RAM
echo - 500 MB مساحة فارغة
echo.
echo بيانات تسجيل الدخول الافتراضية:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
echo للدعم الفني:
echo البريد: <EMAIL>
echo الهاتف: +20 100 123 4567
echo الموقع: www.system11.com
) > "Setup\ReadMe.txt"

echo [4] إنشاء ملف Inno Setup محسن...

REM إنشاء ملف Inno Setup مبسط
(
echo [Setup]
echo AppName=نظام المحاسبة 11
echo AppVersion=1.0.0
echo AppPublisher=شركة 11 للبرمجيات
echo AppPublisherURL=https://www.system11.com
echo DefaultDirName={autopf}\AccountingSystem11
echo DefaultGroupName=نظام المحاسبة 11
echo OutputDir=Output
echo OutputBaseFilename=AccountingSystem11_Setup
echo Compression=lzma
echo SolidCompression=yes
echo PrivilegesRequired=admin
echo LicenseFile=License.txt
echo InfoBeforeFile=ReadMe.txt
echo.
echo [Languages]
echo Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"
echo.
echo [Tasks]
echo Name: "desktopicon"; Description: "إنشاء أيقونة على سطح المكتب"; GroupDescription: "أيقونات إضافية"
echo.
echo [Files]
echo Source: "Files\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
echo Source: "License.txt"; DestDir: "{app}"; Flags: ignoreversion
echo Source: "ReadMe.txt"; DestDir: "{app}"; Flags: ignoreversion
echo.
echo [Icons]
echo Name: "{group}\نظام المحاسبة 11"; Filename: "{app}\AccountingSystem11.exe"
echo Name: "{group}\إلغاء التثبيت"; Filename: "{uninstallexe}"
echo Name: "{autodesktop}\نظام المحاسبة 11"; Filename: "{app}\AccountingSystem11.exe"; Tasks: desktopicon
echo.
echo [Run]
echo Filename: "{app}\AccountingSystem11.exe"; Description: "تشغيل نظام المحاسبة 11"; Flags: nowait postinstall skipifsilent
) > "Setup\Simple_Setup.iss"

echo [5] البحث عن Inno Setup...

REM البحث عن Inno Setup في المواقع الشائعة
set "INNO_PATH="

if exist "C:\Program Files (x86)\Inno Setup 6\iscc.exe" (
    set "INNO_PATH=C:\Program Files (x86)\Inno Setup 6\iscc.exe"
) else if exist "C:\Program Files\Inno Setup 6\iscc.exe" (
    set "INNO_PATH=C:\Program Files\Inno Setup 6\iscc.exe"
) else if exist "C:\Program Files (x86)\Inno Setup 5\iscc.exe" (
    set "INNO_PATH=C:\Program Files (x86)\Inno Setup 5\iscc.exe"
) else if exist "C:\Program Files\Inno Setup 5\iscc.exe" (
    set "INNO_PATH=C:\Program Files\Inno Setup 5\iscc.exe"
) else (
    where iscc >nul 2>&1
    if %errorlevel% equ 0 (
        set "INNO_PATH=iscc"
    )
)

if defined INNO_PATH (
    echo ✅ تم العثور على Inno Setup: %INNO_PATH%
    echo [6] إنشاء ملف التثبيت...
    
    cd Setup
    "%INNO_PATH%" "Simple_Setup.iss"
    cd ..
    
    if exist "Setup\Output\AccountingSystem11_Setup.exe" (
        echo.
        echo ========================================================
        echo 🎉 تم إنشاء ملف التثبيت بنجاح!
        echo ========================================================
        echo.
        echo 📁 مكان الملف: Setup\Output\AccountingSystem11_Setup.exe
        
        REM نسخ الملف للمجلد الرئيسي
        copy "Setup\Output\AccountingSystem11_Setup.exe" "نظام_المحاسبة_11_Setup.exe" >nul
        
        if exist "نظام_المحاسبة_11_Setup.exe" (
            echo 📁 نسخة في المجلد الرئيسي: نظام_المحاسبة_11_Setup.exe
        )
        
        REM معلومات الملف
        for %%A in ("نظام_المحاسبة_11_Setup.exe") do (
            set "filesize=%%~zA"
            set /a "filesize_mb=%%~zA/1048576"
        )
        
        echo 💾 حجم الملف: %filesize_mb% MB
        echo 🔧 نوع الملف: Windows Installer
        echo ✅ جاهز للتوزيع والتثبيت
        
        echo.
        echo 🎯 ملف التثبيت يحتوي على:
        echo    ✅ البرنامج الكامل
        echo    ✅ جميع المكتبات المطلوبة
        echo    ✅ ملفات الترخيص والمساعدة
        echo    ✅ أيقونات سطح المكتب
        echo    ✅ قائمة ابدأ
        echo    ✅ إلغاء تثبيت نظيف
        
        echo.
        set /p choice="هل تريد اختبار ملف التثبيت؟ (y/n): "
        if /i "%choice%"=="y" (
            echo 🚀 جاري تشغيل ملف التثبيت...
            start "نظام_المحاسبة_11_Setup.exe"
        )
        
    ) else (
        echo ❌ فشل في إنشاء ملف التثبيت
        echo تحقق من رسائل الخطأ أعلاه
    )
    
) else (
    echo ❌ لم يتم العثور على Inno Setup
    echo.
    echo 📥 لإنشاء ملف التثبيت، يرجى تثبيت Inno Setup:
    echo 🔗 الرابط: https://jrsoftware.org/isinfo.php
    echo.
    echo 📦 بدلاً من ذلك، سيتم إنشاء حزمة ZIP:
    
    echo [6] إنشاء حزمة ZIP بديلة...
    
    REM إنشاء مجلد التوزيع
    if not exist "Distribution" mkdir "Distribution"
    
    REM نسخ الملفات
    xcopy "Setup\Files\*" "Distribution\" /E /I /Y >nul
    copy "Setup\License.txt" "Distribution\" >nul
    copy "Setup\ReadMe.txt" "Distribution\" >nul
    
    REM إنشاء ملف تشغيل
    (
    echo @echo off
    echo title نظام المحاسبة 11
    echo echo مرحباً بك في نظام المحاسبة 11
    echo echo.
    echo echo بيانات تسجيل الدخول:
    echo echo اسم المستخدم: admin
    echo echo كلمة المرور: admin123
    echo echo.
    echo AccountingSystem11.exe
    ) > "Distribution\تشغيل.bat"
    
    REM إنشاء ملف ZIP
    powershell -Command "try { Compress-Archive -Path 'Distribution\*' -DestinationPath 'نظام_المحاسبة_11_Portable.zip' -Force; Write-Host 'تم إنشاء حزمة ZIP بنجاح' } catch { Write-Host 'فشل في إنشاء حزمة ZIP' }" 2>nul
    
    if exist "نظام_المحاسبة_11_Portable.zip" (
        echo ✅ تم إنشاء حزمة محمولة: نظام_المحاسبة_11_Portable.zip
        
        for %%A in ("نظام_المحاسبة_11_Portable.zip") do (
            set "filesize=%%~zA"
            set /a "filesize_mb=%%~zA/1048576"
        )
        
        echo 💾 حجم الملف: %filesize_mb% MB
        echo 📦 نوع الملف: ZIP Archive
        echo ✅ جاهز للتوزيع
        
        echo.
        echo 🎯 الحزمة المحمولة تحتوي على:
        echo    ✅ البرنامج الكامل
        echo    ✅ ملف تشغيل سهل
        echo    ✅ ملفات المساعدة
        echo    ✅ لا يحتاج تثبيت
        echo    ✅ يعمل من أي مكان
        
    ) else (
        echo ❌ فشل في إنشاء حزمة ZIP
    )
)

echo.
echo ========================================================
echo 📋 ملخص الملفات المُنشأة:
echo ========================================================

if exist "نظام_المحاسبة_11_Setup.exe" (
    echo ✅ ملف التثبيت: نظام_المحاسبة_11_Setup.exe
)

if exist "نظام_المحاسبة_11_Portable.zip" (
    echo ✅ الحزمة المحمولة: نظام_المحاسبة_11_Portable.zip
)

if exist "Setup\Output\AccountingSystem11_Setup.exe" (
    echo ✅ ملف التثبيت الأصلي: Setup\Output\AccountingSystem11_Setup.exe
)

echo ✅ ملفات المشروع: Setup\Files\
echo ✅ الوثائق: Setup\License.txt, Setup\ReadMe.txt

echo.
echo 🎊 ملف التثبيت جاهز للتوزيع التجاري!
echo.

set /p choice2="هل تريد فتح مجلد الملفات؟ (y/n): "
if /i "%choice2%"=="y" (
    explorer .
)

pause
