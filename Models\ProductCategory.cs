using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AccountingSystem11.Models
{
    /// <summary>
    /// Product category for organizing products
    /// </summary>
    public class ProductCategory : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; }

        [MaxLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// Parent category for hierarchical structure
        /// </summary>
        public int? ParentCategoryId { get; set; }
        public virtual ProductCategory ParentCategory { get; set; }

        /// <summary>
        /// Category code for reference
        /// </summary>
        [MaxLength(20)]
        public string Code { get; set; }

        /// <summary>
        /// Whether category is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Display order
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        // Navigation properties
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
        public virtual ICollection<ProductCategory> SubCategories { get; set; } = new List<ProductCategory>();
    }
}
