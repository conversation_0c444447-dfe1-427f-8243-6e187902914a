<Application x:Class="AccountingSystem11.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="Views/MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Green" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- Custom Styles -->
                <ResourceDictionary>
                    <!-- Main Colors -->
                    <SolidColorBrush x:Key="PrimaryBrush" Color="#1976D2"/>
                    <SolidColorBrush x:Key="SecondaryBrush" Color="#388E3C"/>
                    <SolidColorBrush x:Key="AccentBrush" Color="#FF9800"/>
                    <SolidColorBrush x:Key="BackgroundBrush" Color="#FAFAFA"/>
                    <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
                    <SolidColorBrush x:Key="ErrorBrush" Color="#D32F2F"/>
                    <SolidColorBrush x:Key="SuccessBrush" Color="#388E3C"/>
                    <SolidColorBrush x:Key="WarningBrush" Color="#F57C00"/>
                    
                    <!-- Text Colors -->
                    <SolidColorBrush x:Key="TextPrimaryBrush" Color="#212121"/>
                    <SolidColorBrush x:Key="TextSecondaryBrush" Color="#757575"/>
                    
                    <!-- Card Style -->
                    <Style x:Key="CardStyle" TargetType="Border">
                        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
                        <Setter Property="CornerRadius" Value="8"/>
                        <Setter Property="Padding" Value="16"/>
                        <Setter Property="Margin" Value="8"/>
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
                            </Setter.Value>
                        </Setter>
                    </Style>
                    
                    <!-- Button Styles -->
                    <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="Margin" Value="4"/>
                        <Setter Property="Padding" Value="16,8"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                    </Style>
                    
                    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
                        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
                        <Setter Property="Margin" Value="4"/>
                        <Setter Property="Padding" Value="16,8"/>
                        <Setter Property="FontSize" Value="14"/>
                    </Style>
                    
                    <!-- Title Styles -->
                    <Style x:Key="PageTitleStyle" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="24"/>
                        <Setter Property="FontWeight" Value="Bold"/>
                        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
                        <Setter Property="Margin" Value="0,0,0,16"/>
                    </Style>

                    <Style x:Key="SectionTitleStyle" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="18"/>
                        <Setter Property="FontWeight" Value="SemiBold"/>
                        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
                        <Setter Property="Margin" Value="0,0,0,8"/>
                    </Style>

                    <!-- Menu Button Style -->
                    <Style x:Key="MenuButtonStyle" TargetType="Button">
                        <Setter Property="Background" Value="Transparent"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="BorderThickness" Value="0"/>
                        <Setter Property="Padding" Value="12,8"/>
                        <Setter Property="Margin" Value="0,2"/>
                        <Setter Property="HorizontalAlignment" Value="Stretch"/>
                        <Setter Property="HorizontalContentAlignment" Value="Right"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="Cursor" Value="Hand"/>
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                            CornerRadius="4"
                                            Padding="{TemplateBinding Padding}">
                                        <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                          VerticalAlignment="Center"/>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#33FFFFFF"/>
                                        </Trigger>
                                        <Trigger Property="IsPressed" Value="True">
                                            <Setter Property="Background" Value="#55FFFFFF"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>

                    <!-- Converters -->
                    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

                    <!-- Custom Converters would go here -->
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
