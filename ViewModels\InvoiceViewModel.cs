using AccountingSystem11.Services;
using AccountingSystem11.Models;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Input;

namespace AccountingSystem11.ViewModels
{
    /// <summary>
    /// Invoice view model (placeholder)
    /// </summary>
    public class InvoiceViewModel : BaseViewModel
    {
        private readonly IInvoiceService _invoiceService;
        private readonly ICustomerService _customerService;
        private readonly IProductService _productService;

        public InvoiceViewModel(IInvoiceService invoiceService, ICustomerService customerService, IProductService productService)
        {
            _invoiceService = invoiceService;
            _customerService = customerService;
            _productService = productService;

            Invoices = new ObservableCollection<Invoice>();
            LoadInvoicesCommand = new AsyncRelayCommand(LoadInvoicesAsync);
        }

        public ObservableCollection<Invoice> Invoices { get; }
        public ICommand LoadInvoicesCommand { get; }

        private async Task LoadInvoicesAsync()
        {
            await ExecuteAsync(async () =>
            {
                var invoices = await _invoiceService.GetAllInvoicesAsync();
                Invoices.Clear();
                foreach (var invoice in invoices)
                {
                    Invoices.Add(invoice);
                }
            }, "جاري تحميل الفواتير...");
        }
    }
}
