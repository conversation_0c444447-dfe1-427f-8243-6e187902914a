using System;
using System.ComponentModel.DataAnnotations;

namespace AccountingSystem11.Models
{
    /// <summary>
    /// Inventory transaction for tracking stock movements
    /// </summary>
    public class InventoryTransaction : BaseEntity
    {
        [Required]
        public int ProductId { get; set; }
        public virtual Product Product { get; set; }

        /// <summary>
        /// Related invoice item (if any)
        /// </summary>
        public int? InvoiceItemId { get; set; }
        public virtual InvoiceItem InvoiceItem { get; set; }

        [Required]
        public DateTime TransactionDate { get; set; } = DateTime.Now;

        [Required]
        [MaxLength(20)]
        public string TransactionNumber { get; set; }

        /// <summary>
        /// Transaction type
        /// </summary>
        public InventoryTransactionType TransactionType { get; set; }

        /// <summary>
        /// Quantity change (positive for in, negative for out)
        /// </summary>
        [Required]
        public decimal Quantity { get; set; }

        /// <summary>
        /// Unit cost
        /// </summary>
        public decimal UnitCost { get; set; } = 0;

        /// <summary>
        /// Total cost
        /// </summary>
        public decimal TotalCost { get; set; } = 0;

        /// <summary>
        /// Stock quantity before transaction
        /// </summary>
        public decimal StockBefore { get; set; } = 0;

        /// <summary>
        /// Stock quantity after transaction
        /// </summary>
        public decimal StockAfter { get; set; } = 0;

        /// <summary>
        /// Reference document number
        /// </summary>
        [MaxLength(50)]
        public string ReferenceNumber { get; set; }

        /// <summary>
        /// Transaction description
        /// </summary>
        [MaxLength(200)]
        public string Description { get; set; }

        /// <summary>
        /// Notes
        /// </summary>
        [MaxLength(500)]
        public string Notes { get; set; }

        /// <summary>
        /// Warehouse location
        /// </summary>
        [MaxLength(50)]
        public string Location { get; set; }

        /// <summary>
        /// Batch/Lot number
        /// </summary>
        [MaxLength(50)]
        public string BatchNumber { get; set; }

        /// <summary>
        /// Expiry date (for perishable items)
        /// </summary>
        public DateTime? ExpiryDate { get; set; }
    }

    public enum InventoryTransactionType
    {
        Purchase = 1,           // شراء
        Sale = 2,              // بيع
        Return = 3,            // مرتجع
        Adjustment = 4,        // تسوية
        Transfer = 5,          // نقل
        OpeningBalance = 6,    // رصيد افتتاحي
        Damage = 7,            // تالف
        Loss = 8,              // فقدان
        Production = 9,        // إنتاج
        Consumption = 10       // استهلاك
    }
}
