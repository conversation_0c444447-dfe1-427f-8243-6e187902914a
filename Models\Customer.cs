using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AccountingSystem11.Models
{
    /// <summary>
    /// Customer entity representing clients and customers
    /// </summary>
    public class Customer : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; }

        [MaxLength(100)]
        public string CompanyName { get; set; }

        [MaxLength(15)]
        public string Phone { get; set; }

        [MaxLength(15)]
        public string Mobile { get; set; }

        [MaxLength(100)]
        public string Email { get; set; }

        [MaxLength(200)]
        public string Address { get; set; }

        [MaxLength(50)]
        public string City { get; set; }

        [MaxLength(50)]
        public string Governorate { get; set; }

        [MaxLength(10)]
        public string PostalCode { get; set; }

        /// <summary>
        /// Tax Registration Number (الرقم الضريبي)
        /// </summary>
        [MaxLength(20)]
        public string TaxNumber { get; set; }

        /// <summary>
        /// Commercial Registration Number (رقم السجل التجاري)
        /// </summary>
        [MaxLength(20)]
        public string CommercialRegNumber { get; set; }

        /// <summary>
        /// National ID for individuals (الرقم القومي)
        /// </summary>
        [MaxLength(14)]
        public string NationalId { get; set; }

        /// <summary>
        /// Customer type: Individual, Company, Government
        /// </summary>
        public CustomerType CustomerType { get; set; } = CustomerType.Individual;

        /// <summary>
        /// Credit limit for the customer
        /// </summary>
        public decimal CreditLimit { get; set; } = 0;

        /// <summary>
        /// Current balance (positive = customer owes us, negative = we owe customer)
        /// </summary>
        public decimal Balance { get; set; } = 0;

        /// <summary>
        /// Default payment terms in days
        /// </summary>
        public int PaymentTerms { get; set; } = 30;

        /// <summary>
        /// Default discount percentage
        /// </summary>
        public decimal DefaultDiscount { get; set; } = 0;

        /// <summary>
        /// Notes about the customer
        /// </summary>
        [MaxLength(500)]
        public string Notes { get; set; }

        /// <summary>
        /// Whether customer is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
        public virtual ICollection<CustomerTransaction> Transactions { get; set; } = new List<CustomerTransaction>();
    }

    public enum CustomerType
    {
        Individual = 1,
        Company = 2,
        Government = 3
    }
}
