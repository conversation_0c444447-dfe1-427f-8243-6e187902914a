<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="AccountingSystem11.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="SavedUsername" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="RememberMe" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="DatabaseConnectionString" Type="System.String" Scope="Application">
      <Value Profile="(Default)">Server=(localdb)\MSSQLLocalDB;Database=AccountingSystem11;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true;</Value>
    </Setting>
    <Setting Name="Theme" Type="System.String" Scope="User">
      <Value Profile="(Default)">Light</Value>
    </Setting>
    <Setting Name="Language" Type="System.String" Scope="User">
      <Value Profile="(Default)">ar-EG</Value>
    </Setting>
    <Setting Name="AutoLogin" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="SessionTimeout" Type="System.Int32" Scope="Application">
      <Value Profile="(Default)">30</Value>
    </Setting>
  </Settings>
</SettingsFile>
