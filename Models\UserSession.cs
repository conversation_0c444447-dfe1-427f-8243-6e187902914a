using System;
using System.ComponentModel.DataAnnotations;

namespace AccountingSystem11.Models
{
    /// <summary>
    /// User session tracking
    /// </summary>
    public class UserSession : BaseEntity
    {
        [Required]
        public int UserId { get; set; }
        public virtual User User { get; set; }

        [Required]
        [MaxLength(100)]
        public string SessionToken { get; set; }

        [Required]
        public DateTime LoginTime { get; set; } = DateTime.Now;

        public DateTime? LogoutTime { get; set; }

        [MaxLength(45)]
        public string IpAddress { get; set; }

        [MaxLength(200)]
        public string UserAgent { get; set; }

        [MaxLength(100)]
        public string MachineName { get; set; }

        /// <summary>
        /// Whether session is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Session expiry time
        /// </summary>
        public DateTime ExpiryTime { get; set; } = DateTime.Now.AddHours(8);

        /// <summary>
        /// Last activity time
        /// </summary>
        public DateTime LastActivity { get; set; } = DateTime.Now;

        /// <summary>
        /// Check if session is expired
        /// </summary>
        /// <returns>True if session is expired</returns>
        public bool IsExpired()
        {
            return DateTime.Now > ExpiryTime || !IsActive;
        }

        /// <summary>
        /// Extend session expiry
        /// </summary>
        /// <param name="hours">Hours to extend</param>
        public void ExtendSession(int hours = 8)
        {
            ExpiryTime = DateTime.Now.AddHours(hours);
            LastActivity = DateTime.Now;
        }

        /// <summary>
        /// End the session
        /// </summary>
        public void EndSession()
        {
            IsActive = false;
            LogoutTime = DateTime.Now;
        }
    }
}
