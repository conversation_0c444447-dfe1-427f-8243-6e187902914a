using System.ComponentModel.DataAnnotations;

namespace AccountingSystem11.Models
{
    /// <summary>
    /// Invoice item representing a line item in an invoice
    /// </summary>
    public class InvoiceItem : BaseEntity
    {
        /// <summary>
        /// Reference to the invoice
        /// </summary>
        [Required]
        public int InvoiceId { get; set; }
        public virtual Invoice Invoice { get; set; }

        /// <summary>
        /// Reference to the product
        /// </summary>
        public int? ProductId { get; set; }
        public virtual Product Product { get; set; }

        /// <summary>
        /// Product code (for reference)
        /// </summary>
        [MaxLength(50)]
        public string ProductCode { get; set; }

        /// <summary>
        /// Product name (can be different from product master)
        /// </summary>
        [Required]
        [MaxLength(200)]
        public string ProductName { get; set; }

        /// <summary>
        /// Product description
        /// </summary>
        [MaxLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// Unit of measurement
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string Unit { get; set; } = "قطعة";

        /// <summary>
        /// Quantity
        /// </summary>
        [Required]
        public decimal Quantity { get; set; } = 1;

        /// <summary>
        /// Unit price
        /// </summary>
        [Required]
        public decimal UnitPrice { get; set; } = 0;

        /// <summary>
        /// Line total before discount
        /// </summary>
        public decimal LineTotal { get; set; } = 0;

        /// <summary>
        /// Discount amount for this line
        /// </summary>
        public decimal DiscountAmount { get; set; } = 0;

        /// <summary>
        /// Discount percentage for this line
        /// </summary>
        public decimal DiscountPercentage { get; set; } = 0;

        /// <summary>
        /// Amount after discount
        /// </summary>
        public decimal AmountAfterDiscount { get; set; } = 0;

        /// <summary>
        /// Tax rate for this item
        /// </summary>
        public decimal TaxRate { get; set; } = 14; // Default Egyptian VAT rate

        /// <summary>
        /// Tax amount for this line
        /// </summary>
        public decimal TaxAmount { get; set; } = 0;

        /// <summary>
        /// Total amount including tax
        /// </summary>
        public decimal TotalAmount { get; set; } = 0;

        /// <summary>
        /// Whether this item is taxable
        /// </summary>
        public bool IsTaxable { get; set; } = true;

        /// <summary>
        /// Line sequence number
        /// </summary>
        public int LineNumber { get; set; } = 1;

        /// <summary>
        /// Notes for this line item
        /// </summary>
        [MaxLength(200)]
        public string Notes { get; set; }

        /// <summary>
        /// Calculate line totals
        /// </summary>
        public void CalculateLineTotals()
        {
            // Calculate line total
            LineTotal = Quantity * UnitPrice;

            // Calculate discount
            if (DiscountPercentage > 0)
            {
                DiscountAmount = LineTotal * (DiscountPercentage / 100);
            }

            // Amount after discount
            AmountAfterDiscount = LineTotal - DiscountAmount;

            // Calculate tax if taxable
            if (IsTaxable)
            {
                TaxAmount = AmountAfterDiscount * (TaxRate / 100);
            }
            else
            {
                TaxAmount = 0;
            }

            // Total amount
            TotalAmount = AmountAfterDiscount + TaxAmount;
        }
    }
}
