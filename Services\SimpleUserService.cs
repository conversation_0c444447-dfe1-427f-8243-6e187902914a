using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.IO;
using System.Text.Json;
using BCrypt.Net;

namespace AccountingSystem11.Services
{
    public class SimpleUserService
    {
        private readonly string _usersFilePath;
        private List<SimpleUser> _users;

        public SimpleUserService()
        {
            _usersFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                                        "AccountingSystem11", "users.json");
            
            // إنشاء المجلد إذا لم يكن موجوداً
            Directory.CreateDirectory(Path.GetDirectoryName(_usersFilePath));
            
            LoadUsers();
            CreateDefaultAdmin();
        }

        public async Task<LoginResult> LoginAsync(string username, string password)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                {
                    return new LoginResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "يرجى إدخال اسم المستخدم وكلمة المرور"
                    };
                }

                var user = _users.Find(u => u.Username.Equals(username, StringComparison.OrdinalIgnoreCase));
                
                if (user == null)
                {
                    return new LoginResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "اسم المستخدم غير موجود"
                    };
                }

                if (!user.IsActive)
                {
                    return new LoginResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "الحساب معطل. يرجى الاتصال بالمدير"
                    };
                }

                // التحقق من كلمة المرور
                bool isPasswordValid = false;
                
                // إذا كانت كلمة المرور مشفرة
                if (user.Password.StartsWith("$2"))
                {
                    isPasswordValid = BCrypt.Net.BCrypt.Verify(password, user.Password);
                }
                else
                {
                    // للتوافق مع كلمات المرور غير المشفرة (الحساب الافتراضي)
                    isPasswordValid = user.Password == password;
                    
                    // تشفير كلمة المرور وحفظها
                    if (isPasswordValid)
                    {
                        user.Password = BCrypt.Net.BCrypt.HashPassword(password);
                        await SaveUsersAsync();
                    }
                }

                if (!isPasswordValid)
                {
                    return new LoginResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "كلمة المرور غير صحيحة"
                    };
                }

                // تحديث آخر تسجيل دخول
                user.LastLoginDate = DateTime.Now;
                await SaveUsersAsync();

                return new LoginResult
                {
                    IsSuccess = true,
                    User = new User
                    {
                        Id = user.Id,
                        Username = user.Username,
                        FullName = user.FullName,
                        Email = user.Email,
                        Role = user.Role,
                        IsActive = user.IsActive,
                        CreatedDate = user.CreatedDate,
                        LastLoginDate = user.LastLoginDate
                    }
                };
            }
            catch (Exception ex)
            {
                return new LoginResult
                {
                    IsSuccess = false,
                    ErrorMessage = $"حدث خطأ أثناء تسجيل الدخول: {ex.Message}"
                };
            }
        }

        public async Task<bool> CreateUserAsync(string username, string fullName, string email, string password, string role = "User")
        {
            try
            {
                // التحقق من عدم وجود المستخدم
                if (_users.Exists(u => u.Username.Equals(username, StringComparison.OrdinalIgnoreCase)))
                {
                    return false;
                }

                var newUser = new SimpleUser
                {
                    Id = _users.Count > 0 ? _users.Max(u => u.Id) + 1 : 1,
                    Username = username,
                    FullName = fullName,
                    Email = email,
                    Password = BCrypt.Net.BCrypt.HashPassword(password),
                    Role = role,
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    LastLoginDate = null
                };

                _users.Add(newUser);
                await SaveUsersAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ChangePasswordAsync(string username, string currentPassword, string newPassword)
        {
            try
            {
                var user = _users.Find(u => u.Username.Equals(username, StringComparison.OrdinalIgnoreCase));
                if (user == null) return false;

                // التحقق من كلمة المرور الحالية
                bool isCurrentPasswordValid = false;
                if (user.Password.StartsWith("$2"))
                {
                    isCurrentPasswordValid = BCrypt.Net.BCrypt.Verify(currentPassword, user.Password);
                }
                else
                {
                    isCurrentPasswordValid = user.Password == currentPassword;
                }

                if (!isCurrentPasswordValid) return false;

                // تحديث كلمة المرور
                user.Password = BCrypt.Net.BCrypt.HashPassword(newPassword);
                await SaveUsersAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public List<SimpleUser> GetAllUsers()
        {
            return new List<SimpleUser>(_users);
        }

        private void LoadUsers()
        {
            try
            {
                if (File.Exists(_usersFilePath))
                {
                    var json = File.ReadAllText(_usersFilePath);
                    _users = JsonSerializer.Deserialize<List<SimpleUser>>(json) ?? new List<SimpleUser>();
                }
                else
                {
                    _users = new List<SimpleUser>();
                }
            }
            catch
            {
                _users = new List<SimpleUser>();
            }
        }

        private async Task SaveUsersAsync()
        {
            try
            {
                var json = JsonSerializer.Serialize(_users, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(_usersFilePath, json);
            }
            catch
            {
                // تجاهل الأخطاء في الحفظ
            }
        }

        private void CreateDefaultAdmin()
        {
            // إنشاء حساب المدير الافتراضي إذا لم يكن موجوداً
            if (!_users.Exists(u => u.Username.Equals("admin", StringComparison.OrdinalIgnoreCase)))
            {
                var adminUser = new SimpleUser
                {
                    Id = 1,
                    Username = "admin",
                    FullName = "مدير النظام",
                    Email = "<EMAIL>",
                    Password = "admin123", // سيتم تشفيرها عند أول تسجيل دخول
                    Role = "Admin",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    LastLoginDate = null
                };

                _users.Add(adminUser);
                SaveUsersAsync().Wait();
            }
        }
    }

    // نموذج المستخدم البسيط
    public class SimpleUser
    {
        public int Id { get; set; }
        public string Username { get; set; }
        public string FullName { get; set; }
        public string Email { get; set; }
        public string Password { get; set; }
        public string Role { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? LastLoginDate { get; set; }
    }

    // نتيجة تسجيل الدخول
    public class LoginResult
    {
        public bool IsSuccess { get; set; }
        public User User { get; set; }
        public string ErrorMessage { get; set; }
    }

    // نموذج المستخدم للواجهة
    public class User
    {
        public int Id { get; set; }
        public string Username { get; set; }
        public string FullName { get; set; }
        public string Email { get; set; }
        public string Role { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? LastLoginDate { get; set; }
    }
}
