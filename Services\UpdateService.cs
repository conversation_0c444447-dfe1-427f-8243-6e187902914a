using System;
using System.Diagnostics;
using System.IO;
using System.Net.Http;
using System.Reflection;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;

namespace AccountingSystem11.Services
{
    public class UpdateService
    {
        private readonly string _updateServerUrl = "https://updates.system11.com/accounting";
        private readonly string _currentVersion;
        private readonly HttpClient _httpClient;

        public UpdateService()
        {
            _currentVersion = GetCurrentVersion();
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromMinutes(5);
        }

        public async Task<UpdateInfo> CheckForUpdatesAsync()
        {
            try
            {
                var requestUrl = $"{_updateServerUrl}/check?version={_currentVersion}&product=accounting11";
                var response = await _httpClient.GetStringAsync(requestUrl);
                
                var updateInfo = JsonSerializer.Deserialize<UpdateInfo>(response);
                return updateInfo;
            }
            catch (Exception ex)
            {
                return new UpdateInfo
                {
                    HasUpdate = false,
                    ErrorMessage = $"خطأ في التحقق من التحديثات: {ex.Message}"
                };
            }
        }

        public async Task<bool> DownloadAndInstallUpdateAsync(UpdateInfo updateInfo, IProgress<DownloadProgress> progress = null)
        {
            try
            {
                if (!updateInfo.HasUpdate) return false;

                // إنشاء مجلد التحديثات
                var updateFolder = Path.Combine(Path.GetTempPath(), "AccountingSystem11_Update");
                Directory.CreateDirectory(updateFolder);

                var updateFilePath = Path.Combine(updateFolder, "update.exe");

                // تحميل التحديث
                progress?.Report(new DownloadProgress { Percentage = 0, Status = "بدء التحميل..." });

                using var response = await _httpClient.GetAsync(updateInfo.DownloadUrl, HttpCompletionOption.ResponseHeadersRead);
                response.EnsureSuccessStatusCode();

                var totalBytes = response.Content.Headers.ContentLength ?? 0;
                var downloadedBytes = 0L;

                using var contentStream = await response.Content.ReadAsStreamAsync();
                using var fileStream = new FileStream(updateFilePath, FileMode.Create, FileAccess.Write, FileShare.None, 8192, true);

                var buffer = new byte[8192];
                int bytesRead;

                while ((bytesRead = await contentStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                {
                    await fileStream.WriteAsync(buffer, 0, bytesRead);
                    downloadedBytes += bytesRead;

                    if (totalBytes > 0)
                    {
                        var percentage = (int)((downloadedBytes * 100) / totalBytes);
                        progress?.Report(new DownloadProgress 
                        { 
                            Percentage = percentage, 
                            Status = $"تحميل... {percentage}%" 
                        });
                    }
                }

                progress?.Report(new DownloadProgress { Percentage = 100, Status = "اكتمل التحميل. جاري التثبيت..." });

                // تشغيل المثبت
                var startInfo = new ProcessStartInfo
                {
                    FileName = updateFilePath,
                    Arguments = "/SILENT /CLOSEAPPLICATIONS /RESTARTAPPLICATIONS",
                    UseShellExecute = true,
                    Verb = "runas" // تشغيل كمدير
                };

                Process.Start(startInfo);

                // إغلاق التطبيق الحالي
                Application.Current.Shutdown();

                return true;
            }
            catch (Exception ex)
            {
                progress?.Report(new DownloadProgress 
                { 
                    Percentage = 0, 
                    Status = $"خطأ في التحديث: {ex.Message}" 
                });
                return false;
            }
        }

        public async Task<bool> CheckAndPromptForUpdatesAsync()
        {
            try
            {
                var updateInfo = await CheckForUpdatesAsync();

                if (!updateInfo.HasUpdate)
                {
                    return false;
                }

                var message = $"يتوفر تحديث جديد لنظام المحاسبة 11!\n\n" +
                             $"الإصدار الحالي: {_currentVersion}\n" +
                             $"الإصدار الجديد: {updateInfo.LatestVersion}\n\n" +
                             $"المميزات الجديدة:\n{updateInfo.ReleaseNotes}\n\n" +
                             $"هل تريد تحميل وتثبيت التحديث الآن؟";

                var result = MessageBox.Show(
                    message,
                    "تحديث متوفر",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Information);

                if (result == MessageBoxResult.Yes)
                {
                    var progressWindow = new UpdateProgressWindow();
                    progressWindow.Show();

                    var progress = new Progress<DownloadProgress>(p =>
                    {
                        progressWindow.UpdateProgress(p.Percentage, p.Status);
                    });

                    var success = await DownloadAndInstallUpdateAsync(updateInfo, progress);
                    
                    progressWindow.Close();

                    if (!success)
                    {
                        MessageBox.Show(
                            "فشل في تحميل أو تثبيت التحديث.\nيرجى المحاولة لاحقاً أو تحميل التحديث يدوياً من الموقع.",
                            "خطأ في التحديث",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);
                    }

                    return success;
                }

                return false;
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في التحقق من التحديثات:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
                return false;
            }
        }

        private string GetCurrentVersion()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                return $"{version.Major}.{version.Minor}.{version.Build}";
            }
            catch
            {
                return "1.0.0";
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    public class UpdateInfo
    {
        public bool HasUpdate { get; set; }
        public string LatestVersion { get; set; }
        public string DownloadUrl { get; set; }
        public string ReleaseNotes { get; set; }
        public DateTime ReleaseDate { get; set; }
        public bool IsCritical { get; set; }
        public long FileSize { get; set; }
        public string ErrorMessage { get; set; }
    }

    public class DownloadProgress
    {
        public int Percentage { get; set; }
        public string Status { get; set; }
    }

    // نافذة تقدم التحديث
    public partial class UpdateProgressWindow : Window
    {
        public UpdateProgressWindow()
        {
            InitializeComponent();
        }

        public void UpdateProgress(int percentage, string status)
        {
            Dispatcher.Invoke(() =>
            {
                // تحديث شريط التقدم والحالة
                ProgressBar.Value = percentage;
                StatusText.Text = status;
            });
        }

        private void InitializeComponent()
        {
            Title = "تحديث نظام المحاسبة 11";
            Width = 400;
            Height = 200;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            ResizeMode = ResizeMode.NoResize;

            var grid = new System.Windows.Controls.Grid();
            grid.Margin = new Thickness(20);

            grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = System.Windows.GridLength.Auto });
            grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = new System.Windows.GridLength(20) });
            grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = System.Windows.GridLength.Auto });
            grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = new System.Windows.GridLength(20) });
            grid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = System.Windows.GridLength.Auto });

            var titleText = new System.Windows.Controls.TextBlock
            {
                Text = "جاري تحديث نظام المحاسبة 11...",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center
            };
            System.Windows.Controls.Grid.SetRow(titleText, 0);
            grid.Children.Add(titleText);

            ProgressBar = new System.Windows.Controls.ProgressBar
            {
                Height = 20,
                Minimum = 0,
                Maximum = 100
            };
            System.Windows.Controls.Grid.SetRow(ProgressBar, 2);
            grid.Children.Add(ProgressBar);

            StatusText = new System.Windows.Controls.TextBlock
            {
                Text = "جاري التحضير...",
                HorizontalAlignment = HorizontalAlignment.Center
            };
            System.Windows.Controls.Grid.SetRow(StatusText, 4);
            grid.Children.Add(StatusText);

            Content = grid;
        }

        public System.Windows.Controls.ProgressBar ProgressBar { get; private set; }
        public System.Windows.Controls.TextBlock StatusText { get; private set; }
    }
}
