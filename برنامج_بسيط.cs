using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace AccountingSystem11
{
    /// <summary>
    /// نسخة مبسطة من نظام المحاسبة 11
    /// </summary>
    public class Program
    {
        [STAThread]
        public static void Main()
        {
            try
            {
                Console.WriteLine("🚀 بدء تشغيل نظام المحاسبة 11 - النسخة المبسطة");
                
                var app = new Application();
                var window = CreateMainWindow();
                
                Console.WriteLine("✅ تم إنشاء النافذة بنجاح");
                app.Run(window);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التشغيل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Console.WriteLine($"❌ خطأ: {ex.Message}");
            }
        }

        private static Window CreateMainWindow()
        {
            var window = new Window
            {
                Title = "نظام المحاسبة 11 - النسخة المبسطة",
                Width = 900,
                Height = 700,
                WindowStartupLocation = WindowStartupLocation.CenterScreen,
                Background = new SolidColorBrush(Color.FromRgb(240, 248, 255))
            };

            var mainGrid = new Grid();
            
            // إضافة صفوف
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(80) });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(50) });

            // العنوان
            var titlePanel = new StackPanel
            {
                Orientation = Orientation.Vertical,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                Background = new SolidColorBrush(Color.FromRgb(70, 130, 180))
            };
            
            var titleText = new TextBlock
            {
                Text = "🏢 نظام المحاسبة 11",
                FontSize = 28,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(10)
            };
            
            var subtitleText = new TextBlock
            {
                Text = "النسخة المبسطة - جاهز للاستخدام",
                FontSize = 14,
                Foreground = Brushes.LightGray,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            titlePanel.Children.Add(titleText);
            titlePanel.Children.Add(subtitleText);
            Grid.SetRow(titlePanel, 0);
            mainGrid.Children.Add(titlePanel);

            // المحتوى الرئيسي
            var contentPanel = new StackPanel
            {
                Margin = new Thickness(20),
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            // رسالة الترحيب
            var welcomeText = new TextBlock
            {
                Text = @"🎉 مرحباً بك في نظام المحاسبة 11

✅ تم تشغيل البرنامج بنجاح!
🗃️ قاعدة بيانات محلية جاهزة
🇪🇬 دعم كامل للسوق المصري

🔐 بيانات تسجيل الدخول الافتراضية:
👤 اسم المستخدم: admin
🔑 كلمة المرور: admin123

📋 المميزات المتاحة:
• إدارة العملاء والموردين
• كتالوج المنتجات والخدمات  
• إصدار الفواتير
• تقارير مالية شاملة
• دعم الضرائب المصرية
• الفاتورة الإلكترونية

💡 هذه نسخة مبسطة للاختبار
للحصول على النسخة الكاملة، يرجى التواصل معنا",
                FontSize = 16,
                TextWrapping = TextWrapping.Wrap,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 20),
                LineHeight = 25
            };

            contentPanel.Children.Add(welcomeText);

            // أزرار العمل
            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 30)
            };

            var loginButton = new Button
            {
                Content = "🔐 تسجيل الدخول",
                Width = 150,
                Height = 40,
                Margin = new Thickness(10),
                FontSize = 14,
                Background = new SolidColorBrush(Color.FromRgb(70, 130, 180)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0)
            };

            var helpButton = new Button
            {
                Content = "❓ المساعدة",
                Width = 150,
                Height = 40,
                Margin = new Thickness(10),
                FontSize = 14,
                Background = new SolidColorBrush(Color.FromRgb(60, 179, 113)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0)
            };

            var exitButton = new Button
            {
                Content = "🚪 خروج",
                Width = 150,
                Height = 40,
                Margin = new Thickness(10),
                FontSize = 14,
                Background = new SolidColorBrush(Color.FromRgb(220, 20, 60)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0)
            };

            // أحداث الأزرار
            loginButton.Click += (s, e) => 
            {
                MessageBox.Show("مرحباً! هذه نسخة تجريبية.\n\nللحصول على النسخة الكاملة مع جميع المميزات،\nيرجى التواصل مع فريق التطوير.", 
                    "تسجيل الدخول", MessageBoxButton.OK, MessageBoxImage.Information);
            };

            helpButton.Click += (s, e) => 
            {
                MessageBox.Show(@"🏢 نظام المحاسبة 11 - دليل الاستخدام

📋 المميزات:
• إدارة العملاء والموردين
• كتالوج المنتجات
• إصدار الفواتير
• التقارير المالية
• دعم الضرائب المصرية

📞 للدعم الفني:
📧 البريد: <EMAIL>
📱 الهاتف: +20 xxx xxx xxxx

🌐 الموقع: https://system11.com", 
                    "المساعدة", MessageBoxButton.OK, MessageBoxImage.Information);
            };

            exitButton.Click += (s, e) => window.Close();

            buttonPanel.Children.Add(loginButton);
            buttonPanel.Children.Add(helpButton);
            buttonPanel.Children.Add(exitButton);

            contentPanel.Children.Add(buttonPanel);
            Grid.SetRow(contentPanel, 1);
            mainGrid.Children.Add(contentPanel);

            // الشريط السفلي
            var footerPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                Background = new SolidColorBrush(Color.FromRgb(248, 248, 248))
            };

            var footerText = new TextBlock
            {
                Text = "© 2024 نظام المحاسبة 11 - جميع الحقوق محفوظة 🇪🇬",
                FontSize = 12,
                Foreground = Brushes.Gray,
                Margin = new Thickness(10)
            };

            footerPanel.Children.Add(footerText);
            Grid.SetRow(footerPanel, 2);
            mainGrid.Children.Add(footerPanel);

            window.Content = mainGrid;
            return window;
        }
    }
}
