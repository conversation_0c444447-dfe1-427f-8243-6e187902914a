@echo off
title حل سريع للمشاكل - نظام المحاسبة 11
color 0A
echo.
echo ========================================================
echo       حل سريع للمشاكل - نظام المحاسبة 11
echo ========================================================
echo.

echo 🚀 حل سريع وفوري لجميع المشاكل!
echo ⚡ سيتم إصلاح كل شيء تلقائياً
echo.

echo [1] تنظيف شامل...
if exist "bin" (
    rmdir /s /q "bin" 2>nul
    echo ✅ تم حذف مجلد bin
)
if exist "obj" (
    rmdir /s /q "obj" 2>nul
    echo ✅ تم حذف مجلد obj
)

echo.
echo [2] إنشاء مجلدات أساسية...
if not exist "Views" mkdir "Views"
if not exist "Models" mkdir "Models"
if not exist "Services" mkdir "Services"
if not exist "Data" mkdir "Data"
if not exist "Properties" mkdir "Properties"
echo ✅ تم إنشاء المجلدات

echo.
echo [3] التحقق من .NET...
dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ .NET متوفر
    for /f "tokens=*" %%i in ('dotnet --version') do echo    الإصدار: %%i
) else (
    echo ❌ .NET غير متوفر
    echo 📥 جاري فتح رابط التحميل...
    start https://dotnet.microsoft.com/download/dotnet/6.0
    echo.
    echo ⏳ بعد تثبيت .NET، أعد تشغيل هذا الملف
    pause
    exit /b 1
)

echo.
echo [4] إصلاح ملف المشروع...
REM إنشاء ملف مشروع مبسط ومضمون
(
echo ^<Project Sdk="Microsoft.NET.Sdk"^>
echo.
echo   ^<PropertyGroup^>
echo     ^<OutputType^>WinExe^</OutputType^>
echo     ^<TargetFramework^>net6.0-windows^</TargetFramework^>
echo     ^<UseWPF^>true^</UseWPF^>
echo     ^<AssemblyTitle^>نظام المحاسبة 11^</AssemblyTitle^>
echo     ^<AssemblyDescription^>نظام محاسبة متكامل^</AssemblyDescription^>
echo     ^<AssemblyCompany^>شركة 11 للبرمجيات^</AssemblyCompany^>
echo     ^<AssemblyProduct^>نظام المحاسبة 11^</AssemblyProduct^>
echo     ^<AssemblyCopyright^>© 2024 شركة 11 للبرمجيات^</AssemblyCopyright^>
echo     ^<AssemblyVersion^>*******^</AssemblyVersion^>
echo     ^<FileVersion^>*******^</FileVersion^>
echo     ^<LangVersion^>latest^</LangVersion^>
echo     ^<Nullable^>enable^</Nullable^>
echo   ^</PropertyGroup^>
echo.
echo   ^<ItemGroup^>
echo     ^<PackageReference Include="MaterialDesignThemes" Version="4.9.0" /^>
echo     ^<PackageReference Include="MaterialDesignColors" Version="2.1.4" /^>
echo     ^<PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.14" /^>
echo     ^<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="7.0.14" /^>
echo     ^<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.14" /^>
echo     ^<PackageReference Include="EPPlus" Version="7.0.4" /^>
echo     ^<PackageReference Include="Newtonsoft.Json" Version="13.0.3" /^>
echo     ^<PackageReference Include="BCrypt.Net-Next" Version="4.0.3" /^>
echo   ^</ItemGroup^>
echo.
echo ^</Project^>
) > AccountingSystem11.csproj
echo ✅ تم إصلاح ملف المشروع

echo.
echo [5] إنشاء ملفات أساسية...

REM إنشاء App.xaml
if not exist "App.xaml" (
(
echo ^<Application x:Class="AccountingSystem11.App"
echo              xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
echo              xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
echo              StartupUri="Views/MainWindow.xaml"^>
echo     ^<Application.Resources^>
echo         ^<ResourceDictionary^>
echo             ^<ResourceDictionary.MergedDictionaries^>
echo                 ^<materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Lime" xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes" /^>
echo                 ^<ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" /^>
echo             ^</ResourceDictionary.MergedDictionaries^>
echo         ^</ResourceDictionary^>
echo     ^</Application.Resources^>
echo ^</Application^>
) > App.xaml
echo ✅ تم إنشاء App.xaml
)

REM إنشاء App.xaml.cs
if not exist "App.xaml.cs" (
(
echo using System.Windows;
echo.
echo namespace AccountingSystem11
echo {
echo     public partial class App : Application
echo     {
echo         protected override void OnStartup^(StartupEventArgs e^)
echo         {
echo             base.OnStartup^(e^);
echo         }
echo     }
echo }
) > App.xaml.cs
echo ✅ تم إنشاء App.xaml.cs
)

REM إنشاء MainWindow.xaml
if not exist "Views\MainWindow.xaml" (
(
echo ^<Window x:Class="AccountingSystem11.Views.MainWindow"
echo         xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
echo         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
echo         xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
echo         Title="نظام المحاسبة 11" Height="600" Width="800"
echo         WindowStartupLocation="CenterScreen"^>
echo     ^<Grid^>
echo         ^<TextBlock Text="🏢 مرحباً بك في نظام المحاسبة 11"
echo                    FontSize="24" FontWeight="Bold"
echo                    HorizontalAlignment="Center"
echo                    VerticalAlignment="Center"
echo                    Foreground="Blue"/^>
echo     ^</Grid^>
echo ^</Window^>
) > Views\MainWindow.xaml
echo ✅ تم إنشاء MainWindow.xaml
)

REM إنشاء MainWindow.xaml.cs
if not exist "Views\MainWindow.xaml.cs" (
(
echo using System.Windows;
echo.
echo namespace AccountingSystem11.Views
echo {
echo     public partial class MainWindow : Window
echo     {
echo         public MainWindow^(^)
echo         {
echo             InitializeComponent^(^);
echo         }
echo     }
echo }
) > Views\MainWindow.xaml.cs
echo ✅ تم إنشاء MainWindow.xaml.cs
)

echo.
echo [6] استعادة الحزم...
dotnet restore AccountingSystem11.csproj
if %errorlevel% equ 0 (
    echo ✅ تم استعادة الحزم بنجاح
) else (
    echo ⚠️  مشكلة في استعادة الحزم - سيتم المحاولة مرة أخرى
    timeout /t 3 /nobreak >nul
    dotnet restore AccountingSystem11.csproj --force
)

echo.
echo [7] بناء المشروع...
dotnet build AccountingSystem11.csproj -c Release
if %errorlevel% equ 0 (
    echo ✅ تم بناء المشروع بنجاح
    set "BUILD_SUCCESS=1"
) else (
    echo ⚠️  مشكلة في البناء - جاري المحاولة مع إعدادات مختلفة
    dotnet build AccountingSystem11.csproj -c Debug
    if %errorlevel% equ 0 (
        echo ✅ تم بناء المشروع في وضع Debug
        set "BUILD_SUCCESS=1"
    ) else (
        echo ❌ فشل البناء
        set "BUILD_SUCCESS=0"
    )
)

echo.
echo ========================================================
if "%BUILD_SUCCESS%"=="1" (
    echo 🎉 تم حل جميع المشاكل بنجاح!
    echo ========================================================
    echo.
    echo ✅ المشروع جاهز للتشغيل
    echo 🔐 بيانات تسجيل الدخول:
    echo    👤 اسم المستخدم: admin
    echo    🔑 كلمة المرور: admin123
    echo.
    
    set /p run_choice="هل تريد تشغيل البرنامج الآن؟ (y/n): "
    if /i "%run_choice%"=="y" (
        echo.
        echo 🚀 جاري تشغيل البرنامج...
        dotnet run --project AccountingSystem11.csproj
    )
) else (
    echo ❌ لا تزال هناك مشاكل
    echo ========================================================
    echo.
    echo 🔧 حلول إضافية:
    echo 1. تأكد من تثبيت Visual Studio 2022
    echo 2. شغل Command Prompt كـ Administrator
    echo 3. تأكد من الاتصال بالإنترنت
    echo 4. أعد تشغيل الكمبيوتر وحاول مرة أخرى
    echo.
    echo 📥 روابط مفيدة:
    echo Visual Studio: https://visualstudio.microsoft.com/downloads/
    echo .NET 6.0: https://dotnet.microsoft.com/download/dotnet/6.0
)

echo.
pause
