using AccountingSystem11.Data;
using AccountingSystem11.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.OleDb;
using System.Threading.Tasks;

namespace AccountingSystem11.Services
{
    /// <summary>
    /// Service for Access database operations
    /// </summary>
    public class AccessDataService
    {
        private readonly AccessDbContext _context;

        public AccessDataService()
        {
            _context = new AccessDbContext();
        }

        public async Task<bool> InitializeAsync()
        {
            return await _context.InitializeAsync();
        }

        // Customer operations
        public async Task<List<Customer>> GetCustomersAsync()
        {
            var customers = new List<Customer>();
            
            try
            {
                var query = "SELECT * FROM Customers WHERE IsActive = True ORDER BY Name";
                var dataTable = await _context.ExecuteQueryAsync(query);

                foreach (DataRow row in dataTable.Rows)
                {
                    customers.Add(new Customer
                    {
                        Id = Convert.ToInt32(row["ID"]),
                        Name = row["Name"].ToString(),
                        CompanyName = row["CompanyName"].ToString(),
                        Phone = row["Phone"].ToString(),
                        Mobile = row["Mobile"].ToString(),
                        Email = row["Email"].ToString(),
                        Address = row["Address"].ToString(),
                        City = row["City"].ToString(),
                        Governorate = row["Governorate"].ToString(),
                        TaxNumber = row["TaxNumber"].ToString(),
                        CustomerType = Enum.Parse<CustomerType>(row["CustomerType"].ToString()),
                        CreditLimit = Convert.ToDecimal(row["CreditLimit"]),
                        Balance = Convert.ToDecimal(row["Balance"]),
                        PaymentTerms = Convert.ToInt32(row["PaymentTerms"]),
                        IsActive = Convert.ToBoolean(row["IsActive"]),
                        CreatedAt = Convert.ToDateTime(row["CreatedAt"]),
                        CreatedBy = row["CreatedBy"].ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting customers: {ex.Message}");
            }

            return customers;
        }

        public async Task<bool> AddCustomerAsync(Customer customer)
        {
            try
            {
                var query = @"INSERT INTO Customers 
                    (Name, CompanyName, Phone, Mobile, Email, Address, City, Governorate, 
                     TaxNumber, CustomerType, CreditLimit, PaymentTerms, CreatedBy) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                var parameters = new[]
                {
                    new OleDbParameter("@name", customer.Name),
                    new OleDbParameter("@company", customer.CompanyName ?? ""),
                    new OleDbParameter("@phone", customer.Phone ?? ""),
                    new OleDbParameter("@mobile", customer.Mobile ?? ""),
                    new OleDbParameter("@email", customer.Email ?? ""),
                    new OleDbParameter("@address", customer.Address ?? ""),
                    new OleDbParameter("@city", customer.City ?? ""),
                    new OleDbParameter("@governorate", customer.Governorate ?? ""),
                    new OleDbParameter("@taxnumber", customer.TaxNumber ?? ""),
                    new OleDbParameter("@type", customer.CustomerType.ToString()),
                    new OleDbParameter("@credit", customer.CreditLimit),
                    new OleDbParameter("@terms", customer.PaymentTerms),
                    new OleDbParameter("@createdby", customer.CreatedBy ?? "System")
                };

                var result = await _context.ExecuteNonQueryAsync(query, parameters);
                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding customer: {ex.Message}");
                return false;
            }
        }

        // Product operations
        public async Task<List<Product>> GetProductsAsync()
        {
            var products = new List<Product>();
            
            try
            {
                var query = @"SELECT p.*, c.Name as CategoryName 
                             FROM Products p 
                             LEFT JOIN ProductCategories c ON p.CategoryID = c.ID 
                             WHERE p.IsActive = True 
                             ORDER BY p.Name";
                
                var dataTable = await _context.ExecuteQueryAsync(query);

                foreach (DataRow row in dataTable.Rows)
                {
                    products.Add(new Product
                    {
                        Id = Convert.ToInt32(row["ID"]),
                        Code = row["Code"].ToString(),
                        Name = row["Name"].ToString(),
                        Description = row["Description"].ToString(),
                        CategoryId = row["CategoryID"] != DBNull.Value ? Convert.ToInt32(row["CategoryID"]) : null,
                        Unit = row["Unit"].ToString(),
                        PurchasePrice = Convert.ToDecimal(row["PurchasePrice"]),
                        SellingPrice = Convert.ToDecimal(row["SellingPrice"]),
                        MinSellingPrice = Convert.ToDecimal(row["MinSellingPrice"]),
                        StockQuantity = Convert.ToDouble(row["StockQuantity"]),
                        MinStockLevel = Convert.ToDouble(row["MinStockLevel"]),
                        IsTaxable = Convert.ToBoolean(row["IsTaxable"]),
                        TaxRate = Convert.ToDouble(row["TaxRate"]),
                        IsActive = Convert.ToBoolean(row["IsActive"]),
                        CreatedAt = Convert.ToDateTime(row["CreatedAt"]),
                        CreatedBy = row["CreatedBy"].ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting products: {ex.Message}");
            }

            return products;
        }

        public async Task<bool> AddProductAsync(Product product)
        {
            try
            {
                var query = @"INSERT INTO Products 
                    (Code, Name, Description, CategoryID, Unit, PurchasePrice, SellingPrice, 
                     MinSellingPrice, StockQuantity, MinStockLevel, IsTaxable, TaxRate, CreatedBy) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                var parameters = new[]
                {
                    new OleDbParameter("@code", product.Code),
                    new OleDbParameter("@name", product.Name),
                    new OleDbParameter("@desc", product.Description ?? ""),
                    new OleDbParameter("@category", product.CategoryId.HasValue ? (object)product.CategoryId.Value : DBNull.Value),
                    new OleDbParameter("@unit", product.Unit ?? "قطعة"),
                    new OleDbParameter("@purchase", product.PurchasePrice),
                    new OleDbParameter("@selling", product.SellingPrice),
                    new OleDbParameter("@minselling", product.MinSellingPrice),
                    new OleDbParameter("@stock", product.StockQuantity),
                    new OleDbParameter("@minstock", product.MinStockLevel),
                    new OleDbParameter("@taxable", product.IsTaxable),
                    new OleDbParameter("@taxrate", product.TaxRate),
                    new OleDbParameter("@createdby", product.CreatedBy ?? "System")
                };

                var result = await _context.ExecuteNonQueryAsync(query, parameters);
                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding product: {ex.Message}");
                return false;
            }
        }

        // Category operations
        public async Task<List<ProductCategory>> GetCategoriesAsync()
        {
            var categories = new List<ProductCategory>();
            
            try
            {
                var query = "SELECT * FROM ProductCategories WHERE IsActive = True ORDER BY Name";
                var dataTable = await _context.ExecuteQueryAsync(query);

                foreach (DataRow row in dataTable.Rows)
                {
                    categories.Add(new ProductCategory
                    {
                        Id = Convert.ToInt32(row["ID"]),
                        Name = row["Name"].ToString(),
                        Code = row["Code"].ToString(),
                        Description = row["Description"].ToString(),
                        IsActive = Convert.ToBoolean(row["IsActive"]),
                        CreatedAt = Convert.ToDateTime(row["CreatedAt"]),
                        CreatedBy = row["CreatedBy"].ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting categories: {ex.Message}");
            }

            return categories;
        }

        // Statistics
        public async Task<Dictionary<string, object>> GetDashboardStatsAsync()
        {
            var stats = new Dictionary<string, object>();

            try
            {
                // Customer count
                var customerQuery = "SELECT COUNT(*) FROM Customers WHERE IsActive = True";
                var customerTable = await _context.ExecuteQueryAsync(customerQuery);
                stats["CustomerCount"] = Convert.ToInt32(customerTable.Rows[0][0]);

                // Product count
                var productQuery = "SELECT COUNT(*) FROM Products WHERE IsActive = True";
                var productTable = await _context.ExecuteQueryAsync(productQuery);
                stats["ProductCount"] = Convert.ToInt32(productTable.Rows[0][0]);

                // Invoice count
                var invoiceQuery = "SELECT COUNT(*) FROM Invoices";
                var invoiceTable = await _context.ExecuteQueryAsync(invoiceQuery);
                stats["InvoiceCount"] = Convert.ToInt32(invoiceTable.Rows[0][0]);

                // Total sales (this month)
                var salesQuery = @"SELECT ISNULL(SUM(TotalAmount), 0) FROM Invoices 
                                  WHERE MONTH(InvoiceDate) = MONTH(Date()) 
                                  AND YEAR(InvoiceDate) = YEAR(Date())";
                var salesTable = await _context.ExecuteQueryAsync(salesQuery);
                stats["TotalSales"] = Convert.ToDecimal(salesTable.Rows[0][0]);

                // Low stock products
                var lowStockQuery = "SELECT COUNT(*) FROM Products WHERE StockQuantity <= MinStockLevel AND IsActive = True";
                var lowStockTable = await _context.ExecuteQueryAsync(lowStockQuery);
                stats["LowStockCount"] = Convert.ToInt32(lowStockTable.Rows[0][0]);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting dashboard stats: {ex.Message}");
                // Return default values
                stats["CustomerCount"] = 0;
                stats["ProductCount"] = 0;
                stats["InvoiceCount"] = 0;
                stats["TotalSales"] = 0m;
                stats["LowStockCount"] = 0;
            }

            return stats;
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
