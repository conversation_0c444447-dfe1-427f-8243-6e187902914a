using System;
using System.Collections.Generic;
using System.Data.OleDb;
using System.Threading.Tasks;
using AccountingSystem11.Data;
using AccountingSystem11.Models;

namespace AccountingSystem11.Services
{
    /// <summary>
    /// خدمة البيانات للعمل مع قاعدة بيانات Access
    /// </summary>
    public class AccessDataService : IDisposable
    {
        private readonly AccessDbContext _context;

        public AccessDataService()
        {
            _context = new AccessDbContext();
        }

        public async Task<bool> InitializeAsync()
        {
            return await _context.InitializeDatabaseAsync();
        }

        // خدمات العملاء
        public async Task<List<Customer>> GetAllCustomersAsync()
        {
            var customers = new List<Customer>();

            try
            {
                using var connection = _context.GetConnection();
                using var command = new OleDbCommand("SELECT * FROM Customers WHERE IsDeleted = FALSE ORDER BY Name", connection);
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    customers.Add(new Customer
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Name = reader["Name"].ToString(),
                        CompanyName = reader["CompanyName"] == DBNull.Value ? null : reader["CompanyName"].ToString(),
                        Phone = reader["Phone"] == DBNull.Value ? null : reader["Phone"].ToString(),
                        Mobile = reader["Mobile"] == DBNull.Value ? null : reader["Mobile"].ToString(),
                        Email = reader["Email"] == DBNull.Value ? null : reader["Email"].ToString(),
                        Address = reader["Address"] == DBNull.Value ? null : reader["Address"].ToString(),
                        City = reader["City"] == DBNull.Value ? null : reader["City"].ToString(),
                        Governorate = reader["Governorate"] == DBNull.Value ? null : reader["Governorate"].ToString(),
                        TaxNumber = reader["TaxNumber"] == DBNull.Value ? null : reader["TaxNumber"].ToString(),
                        CustomerType = (CustomerType)Convert.ToInt32(reader["CustomerType"]),
                        CreditLimit = Convert.ToDecimal(reader["CreditLimit"]),
                        Balance = Convert.ToDecimal(reader["Balance"]),
                        PaymentTerms = Convert.ToInt32(reader["PaymentTerms"]),
                        IsActive = Convert.ToBoolean(reader["IsActive"]),
                        CreatedAt = reader["CreatedAt"] == DBNull.Value ? DateTime.Now : Convert.ToDateTime(reader["CreatedAt"])
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب العملاء: {ex.Message}");
            }

            return customers;
        }

        public async Task<Customer> AddCustomerAsync(Customer customer)
        {
            try
            {
                using var connection = _context.GetConnection();
                using var command = new OleDbCommand(@"
                    INSERT INTO Customers (Name, CompanyName, Phone, Mobile, Email, Address, City, Governorate, 
                                         TaxNumber, CustomerType, CreditLimit, PaymentTerms, IsActive, CreatedBy)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", connection);

                command.Parameters.AddWithValue("@Name", customer.Name);
                command.Parameters.AddWithValue("@CompanyName", customer.CompanyName ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Phone", customer.Phone ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Mobile", customer.Mobile ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Email", customer.Email ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Address", customer.Address ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@City", customer.City ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Governorate", customer.Governorate ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@TaxNumber", customer.TaxNumber ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@CustomerType", (int)customer.CustomerType);
                command.Parameters.AddWithValue("@CreditLimit", customer.CreditLimit);
                command.Parameters.AddWithValue("@PaymentTerms", customer.PaymentTerms);
                command.Parameters.AddWithValue("@IsActive", customer.IsActive);
                command.Parameters.AddWithValue("@CreatedBy", customer.CreatedBy ?? "System");

                await command.ExecuteNonQueryAsync();

                // جلب ID الجديد
                using var idCommand = new OleDbCommand("SELECT @@IDENTITY", connection);
                var newId = await idCommand.ExecuteScalarAsync();
                customer.Id = Convert.ToInt32(newId);

                return customer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة العميل: {ex.Message}");
                return null;
            }
        }

        // خدمات المنتجات
        public async Task<List<Product>> GetAllProductsAsync()
        {
            var products = new List<Product>();

            try
            {
                using var connection = _context.GetConnection();
                using var command = new OleDbCommand(@"
                    SELECT p.*, c.Name as CategoryName 
                    FROM Products p 
                    LEFT JOIN ProductCategories c ON p.CategoryId = c.Id 
                    WHERE p.IsDeleted = FALSE 
                    ORDER BY p.Name", connection);
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    products.Add(new Product
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Code = reader["Code"].ToString(),
                        Name = reader["Name"].ToString(),
                        Description = reader["Description"] == DBNull.Value ? null : reader["Description"].ToString(),
                        CategoryId = reader["CategoryId"] == DBNull.Value ? null : Convert.ToInt32(reader["CategoryId"]),
                        Unit = reader["Unit"].ToString(),
                        PurchasePrice = Convert.ToDecimal(reader["PurchasePrice"]),
                        SellingPrice = Convert.ToDecimal(reader["SellingPrice"]),
                        StockQuantity = Convert.ToDecimal(reader["StockQuantity"]),
                        MinStockLevel = Convert.ToDecimal(reader["MinStockLevel"]),
                        IsTaxable = Convert.ToBoolean(reader["IsTaxable"]),
                        IsActive = Convert.ToBoolean(reader["IsActive"]),
                        CreatedAt = reader["CreatedAt"] == DBNull.Value ? DateTime.Now : Convert.ToDateTime(reader["CreatedAt"])
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب المنتجات: {ex.Message}");
            }

            return products;
        }

        // خدمات فئات المنتجات
        public async Task<List<ProductCategory>> GetAllCategoriesAsync()
        {
            var categories = new List<ProductCategory>();

            try
            {
                using var connection = _context.GetConnection();
                using var command = new OleDbCommand("SELECT * FROM ProductCategories WHERE IsDeleted = FALSE ORDER BY Name", connection);
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    categories.Add(new ProductCategory
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Name = reader["Name"].ToString(),
                        Code = reader["Code"] == DBNull.Value ? null : reader["Code"].ToString(),
                        Description = reader["Description"] == DBNull.Value ? null : reader["Description"].ToString(),
                        IsActive = Convert.ToBoolean(reader["IsActive"]),
                        CreatedAt = reader["CreatedAt"] == DBNull.Value ? DateTime.Now : Convert.ToDateTime(reader["CreatedAt"])
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب الفئات: {ex.Message}");
            }

            return categories;
        }

        // خدمات المستخدمين
        public async Task<User> AuthenticateUserAsync(string username, string password)
        {
            try
            {
                using var connection = _context.GetConnection();
                using var command = new OleDbCommand("SELECT * FROM Users WHERE Username = ? AND IsActive = TRUE AND IsDeleted = FALSE", connection);
                command.Parameters.AddWithValue("@Username", username);

                using var reader = await command.ExecuteReaderAsync();

                if (await reader.ReadAsync())
                {
                    var user = new User
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Username = reader["Username"].ToString(),
                        FullName = reader["FullName"].ToString(),
                        Email = reader["Email"].ToString(),
                        PasswordHash = reader["PasswordHash"].ToString(),
                        Role = (UserRole)Convert.ToInt32(reader["Role"]),
                        IsActive = Convert.ToBoolean(reader["IsActive"]),
                        LastLoginDate = reader["LastLoginDate"] == DBNull.Value ? null : Convert.ToDateTime(reader["LastLoginDate"]),
                        FailedLoginAttempts = Convert.ToInt32(reader["FailedLoginAttempts"]),
                        LockedUntil = reader["LockedUntil"] == DBNull.Value ? null : Convert.ToDateTime(reader["LockedUntil"])
                    };

                    // التحقق من كلمة المرور
                    if (BCrypt.Net.BCrypt.Verify(password, user.PasswordHash))
                    {
                        // تحديث آخر تسجيل دخول
                        await UpdateLastLoginAsync(user.Id);
                        return user;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في المصادقة: {ex.Message}");
            }

            return null;
        }

        private async Task UpdateLastLoginAsync(int userId)
        {
            try
            {
                using var connection = _context.GetConnection();
                using var command = new OleDbCommand("UPDATE Users SET LastLoginDate = NOW(), FailedLoginAttempts = 0 WHERE Id = ?", connection);
                command.Parameters.AddWithValue("@Id", userId);
                await command.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث آخر تسجيل دخول: {ex.Message}");
            }
        }

        // إحصائيات لوحة التحكم
        public async Task<DashboardStats> GetDashboardStatsAsync()
        {
            var stats = new DashboardStats();

            try
            {
                using var connection = _context.GetConnection();

                // عدد العملاء
                using var customersCommand = new OleDbCommand("SELECT COUNT(*) FROM Customers WHERE IsActive = TRUE AND IsDeleted = FALSE", connection);
                stats.TotalCustomers = Convert.ToInt32(await customersCommand.ExecuteScalarAsync());

                // عدد المنتجات
                using var productsCommand = new OleDbCommand("SELECT COUNT(*) FROM Products WHERE IsActive = TRUE AND IsDeleted = FALSE", connection);
                stats.TotalProducts = Convert.ToInt32(await productsCommand.ExecuteScalarAsync());

                // المنتجات منخفضة المخزون
                using var lowStockCommand = new OleDbCommand("SELECT COUNT(*) FROM Products WHERE IsActive = TRUE AND IsDeleted = FALSE AND StockQuantity <= MinStockLevel", connection);
                stats.LowStockProducts = Convert.ToInt32(await lowStockCommand.ExecuteScalarAsync());

                // قيمة المخزون
                using var inventoryValueCommand = new OleDbCommand("SELECT SUM(StockQuantity * PurchasePrice) FROM Products WHERE IsActive = TRUE AND IsDeleted = FALSE", connection);
                var inventoryValue = await inventoryValueCommand.ExecuteScalarAsync();
                stats.InventoryValue = inventoryValue == DBNull.Value ? 0 : Convert.ToDecimal(inventoryValue);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب إحصائيات لوحة التحكم: {ex.Message}");
            }

            return stats;
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }

    // فئة الإحصائيات
    public class DashboardStats
    {
        public int TotalCustomers { get; set; }
        public int TotalProducts { get; set; }
        public int LowStockProducts { get; set; }
        public decimal InventoryValue { get; set; }
        public decimal TotalSales { get; set; } = 0;
        public decimal TotalProfit { get; set; } = 0;
        public int TotalInvoices { get; set; } = 0;
    }
}
