using AccountingSystem11.Services;
using System.Threading.Tasks;
using System.Windows.Input;

namespace AccountingSystem11.ViewModels
{
    /// <summary>
    /// Report view model (placeholder)
    /// </summary>
    public class ReportViewModel : BaseViewModel
    {
        private readonly IReportService _reportService;

        public ReportViewModel(IReportService reportService)
        {
            _reportService = reportService;

            LoadReportsCommand = new AsyncRelayCommand(LoadReportsAsync);
        }

        public ICommand LoadReportsCommand { get; }

        private async Task LoadReportsAsync()
        {
            await ExecuteAsync(async () =>
            {
                // Load reports data
                await Task.Delay(1000); // Placeholder
            }, "جاري تحميل التقارير...");
        }
    }
}
