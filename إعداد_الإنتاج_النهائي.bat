@echo off
title إعداد الإنتاج النهائي - نظام المحاسبة 11
color 0B
echo.
echo ========================================================
echo      إعداد الإنتاج النهائي - نظام المحاسبة 11
echo ========================================================
echo.

echo 🚀 تحويل البرنامج إلى منتج جاهز للطرح التجاري!
echo ✅ نظام ترخيص احترافي
echo ✅ تحديثات تلقائية
echo ✅ تحليلات ومتابعة
echo ✅ دعم فني مدمج
echo ✅ ملف تثبيت احترافي
echo.

echo [1] التحقق من المتطلبات...

REM التحقق من .NET
dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ .NET متوفر
    for /f "tokens=*" %%i in ('dotnet --version') do echo    الإصدار: %%i
) else (
    echo ❌ .NET غير متوفر
    echo يرجى تثبيت .NET 6.0 أو أحدث
    pause
    exit /b 1
)

REM التحقق من Inno Setup (لإنشاء ملف التثبيت)
where iscc >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Inno Setup متوفر
) else (
    echo ⚠️  Inno Setup غير متوفر (مطلوب لإنشاء ملف التثبيت)
    echo 📥 يمكن تحميله من: https://jrsoftware.org/isinfo.php
)

echo.
echo [2] تنظيف وإعداد المشروع...

REM حذف ملفات البناء القديمة
if exist "bin" rmdir /s /q "bin" 2>nul
if exist "obj" rmdir /s /q "obj" 2>nul
if exist "Setup\Output" rmdir /s /q "Setup\Output" 2>nul

echo ✅ تم تنظيف الملفات القديمة

REM إنشاء مجلدات الإنتاج
if not exist "Production" mkdir "Production"
if not exist "Production\Release" mkdir "Production\Release"
if not exist "Production\Setup" mkdir "Production\Setup"
if not exist "Production\Documentation" mkdir "Production\Documentation"

echo ✅ تم إنشاء مجلدات الإنتاج

echo.
echo [3] بناء النسخة النهائية...

REM استعادة الحزم
echo 📦 استعادة الحزم...
dotnet restore AccountingSystem11.csproj
if %errorlevel% neq 0 (
    echo ❌ فشل في استعادة الحزم
    pause
    exit /b 1
)

REM بناء النسخة النهائية
echo 🔨 بناء النسخة النهائية...
dotnet publish AccountingSystem11.csproj -c Release -r win-x64 --self-contained false -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true -o "Production\Release"

if %errorlevel% equ 0 (
    echo ✅ تم بناء النسخة النهائية بنجاح
) else (
    echo ❌ فشل في بناء النسخة النهائية
    pause
    exit /b 1
)

echo.
echo [4] إنشاء ملفات الإنتاج...

REM إنشاء ملف الترخيص
(
echo رخصة استخدام نظام المحاسبة 11
echo =====================================
echo.
echo © 2024 شركة 11 للبرمجيات. جميع الحقوق محفوظة.
echo.
echo هذا البرنامج محمي بحقوق الطبع والنشر. يُمنع نسخه أو توزيعه
echo بدون إذن كتابي من الشركة المطورة.
echo.
echo للحصول على ترخيص تجاري، يرجى الاتصال بـ:
echo البريد الإلكتروني: <EMAIL>
echo الهاتف: +20 100 123 4567
echo الموقع: www.system11.com
echo.
echo شروط الاستخدام:
echo 1. يُسمح بتثبيت البرنامج على جهاز واحد فقط لكل ترخيص
echo 2. يُمنع إعادة بيع أو توزيع البرنامج
echo 3. الشركة غير مسؤولة عن أي أضرار ناتجة عن استخدام البرنامج
echo 4. يحق للشركة تحديث شروط الترخيص في أي وقت
echo.
echo بتثبيت هذا البرنامج، فإنك توافق على جميع الشروط المذكورة أعلاه.
) > "Production\Setup\License.txt"

REM إنشاء ملف README
(
echo نظام المحاسبة 11 - الإصدار التجاري
echo ====================================
echo.
echo مرحباً بك في نظام المحاسبة 11!
echo.
echo هذا النظام مصمم خصيصاً للشركات المصرية والعربية
echo ويدعم جميع متطلبات المحاسبة والضرائب المصرية.
echo.
echo المميزات الرئيسية:
echo • إدارة شاملة للعملاء والموردين
echo • نظام فواتير متقدم مع الفاتورة الإلكترونية
echo • تقارير مالية شاملة ومفصلة
echo • دعم كامل للضرائب المصرية
echo • واجهة عربية سهلة الاستخدام
echo • نسخ احتياطية تلقائية
echo • دعم فني متخصص
echo.
echo متطلبات النظام:
echo • Windows 10 أو أحدث
echo • .NET 6.0 أو أحدث
echo • 4 GB RAM على الأقل
echo • 500 MB مساحة فارغة
echo • اتصال إنترنت للتفعيل والتحديثات
echo.
echo للدعم الفني:
echo البريد: <EMAIL>
echo الهاتف: +20 100 123 4567
echo الموقع: www.system11.com/support
echo.
echo شكراً لاختيارك نظام المحاسبة 11!
) > "Production\Setup\ReadMe.txt"

REM إنشاء ملف معلومات ما بعد التثبيت
(
echo تم تثبيت نظام المحاسبة 11 بنجاح!
echo ===================================
echo.
echo الخطوات التالية:
echo.
echo 1. تفعيل الترخيص:
echo    • افتح البرنامج
echo    • اذهب إلى "مساعدة" ← "تفعيل الترخيص"
echo    • أدخل مفتاح الترخيص الخاص بك
echo.
echo 2. إعداد قاعدة البيانات:
echo    • سيتم إنشاء قاعدة البيانات تلقائياً عند أول تشغيل
echo    • تأكد من وجود SQL Server LocalDB
echo.
echo 3. إنشاء أول مستخدم:
echo    • استخدم الحساب الافتراضي: admin / admin123
echo    • أنشئ حسابات جديدة للموظفين
echo.
echo 4. استيراد البيانات:
echo    • يمكنك استيراد بيانات من Excel
echo    • أو إدخال البيانات يدوياً
echo.
echo 5. التدريب والدعم:
echo    • شاهد الفيديوهات التعليمية على موقعنا
echo    • اتصل بالدعم الفني للمساعدة
echo.
echo روابط مفيدة:
echo • دليل المستخدم: www.system11.com/guide
echo • الفيديوهات التعليمية: www.system11.com/videos
echo • الدعم الفني: www.system11.com/support
echo • التحديثات: www.system11.com/updates
echo.
echo نتمنى لك تجربة ممتازة مع نظام المحاسبة 11!
) > "Production\Setup\AfterInstall.txt"

echo ✅ تم إنشاء ملفات الإنتاج

echo.
echo [5] نسخ الملفات للإنتاج...

REM نسخ الملفات الأساسية
xcopy "Production\Release\*" "Production\Setup\" /E /I /Y >nul
xcopy "Marketing\*" "Production\Documentation\" /E /I /Y >nul

echo ✅ تم نسخ جميع الملفات

echo.
echo [6] إنشاء ملف التثبيت...

if exist "Setup\AccountingSystem11_Setup.iss" (
    where iscc >nul 2>&1
    if %errorlevel% equ 0 (
        echo 🔨 إنشاء ملف التثبيت باستخدام Inno Setup...
        iscc "Setup\AccountingSystem11_Setup.iss"
        if %errorlevel% equ 0 (
            echo ✅ تم إنشاء ملف التثبيت بنجاح
            if exist "Setup\Output\AccountingSystem11_Setup_v1.0.0.exe" (
                copy "Setup\Output\AccountingSystem11_Setup_v1.0.0.exe" "Production\" >nul
                echo ✅ تم نسخ ملف التثبيت إلى مجلد الإنتاج
            )
        ) else (
            echo ❌ فشل في إنشاء ملف التثبيت
        )
    ) else (
        echo ⚠️  Inno Setup غير متوفر - تم تخطي إنشاء ملف التثبيت
    )
) else (
    echo ⚠️  ملف Inno Setup غير موجود - تم تخطي إنشاء ملف التثبيت
)

echo.
echo [7] إنشاء حزمة التوزيع...

REM إنشاء ملف ZIP للتوزيع
echo 📦 إنشاء حزمة التوزيع...
powershell -Command "try { Compress-Archive -Path 'Production\*' -DestinationPath 'AccountingSystem11_Commercial_v1.0.0.zip' -Force; Write-Host 'تم إنشاء حزمة التوزيع بنجاح' } catch { Write-Host 'فشل في إنشاء حزمة التوزيع' }" 2>nul

if exist "AccountingSystem11_Commercial_v1.0.0.zip" (
    echo ✅ تم إنشاء حزمة التوزيع: AccountingSystem11_Commercial_v1.0.0.zip
) else (
    echo ⚠️  لم يتم إنشاء حزمة التوزيع (PowerShell غير متاح)
)

echo.
echo ========================================================
echo 🎉 تم إعداد الإنتاج النهائي بنجاح!
echo ========================================================
echo.

echo 📋 ملخص الإنتاج:
echo ┌─────────────────────────────────────────────────────────┐
echo │ نظام المحاسبة 11 - الإصدار التجاري                    │
echo ├─────────────────────────────────────────────────────────┤
echo │ ✅ نسخة قابلة للتشغيل: Production\Release\            │
echo │ ✅ ملفات التثبيت: Production\Setup\                   │
echo │ ✅ الوثائق والتسويق: Production\Documentation\       │
if exist "Production\AccountingSystem11_Setup_v1.0.0.exe" (
    echo │ ✅ ملف التثبيت: AccountingSystem11_Setup_v1.0.0.exe   │
)
if exist "AccountingSystem11_Commercial_v1.0.0.zip" (
    echo │ ✅ حزمة التوزيع: AccountingSystem11_Commercial_v1.0.0.zip │
)
echo └─────────────────────────────────────────────────────────┘
echo.

echo 🚀 المنتج جاهز للطرح التجاري!
echo.
echo 💰 خطط الأسعار:
echo    💼 الأساسية: 1,500 جنيه/سنة
echo    🚀 المتقدمة: 3,500 جنيه/سنة  
echo    🏢 المؤسسات: 7,500 جنيه/سنة
echo.
echo 📊 الهدف: 10 مليون جنيه في السنة الأولى
echo 🎯 العملاء المستهدفون: 2,000 عميل
echo.

set /p choice="هل تريد فتح مجلد الإنتاج؟ (y/n): "
if /i "%choice%"=="y" (
    explorer "Production"
)

echo.
echo 🎊 مبروك! نظام المحاسبة 11 جاهز لغزو السوق المصري!
echo.
pause
