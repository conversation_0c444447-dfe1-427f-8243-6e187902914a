<Window x:Class="AccountingSystem11.Views.SupportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="الدعم الفني - نظام المحاسبة 11" 
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{materialDesign:MaterialDesignFont}">

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- الشريط الجانبي -->
        <Border Grid.Column="0" Background="{DynamicResource PrimaryHueMidBrush}">
            <StackPanel Margin="16">
                <!-- شعار الدعم -->
                <materialDesign:PackIcon Kind="HeadsetMic" 
                                       Width="64" Height="64"
                                       HorizontalAlignment="Center"
                                       Foreground="White"
                                       Margin="0,16,0,24"/>

                <TextBlock Text="الدعم الفني" 
                         Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                         Foreground="White"
                         HorizontalAlignment="Center"
                         Margin="0,0,0,32"/>

                <!-- قائمة الخيارات -->
                <ListBox x:Name="SupportOptionsListBox"
                       Background="Transparent"
                       BorderThickness="0"
                       SelectionChanged="SupportOptionsListBox_SelectionChanged">
                    <ListBox.ItemContainerStyle>
                        <Style TargetType="ListBoxItem" BasedOn="{StaticResource MaterialDesignListBoxItem}">
                            <Setter Property="Foreground" Value="White"/>
                            <Setter Property="Margin" Value="0,4"/>
                            <Setter Property="Padding" Value="12,8"/>
                        </Style>
                    </ListBox.ItemContainerStyle>

                    <ListBoxItem>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="MessageQuestion" Width="20" Height="20" Margin="0,0,8,0"/>
                            <TextBlock Text="الأسئلة الشائعة"/>
                        </StackPanel>
                    </ListBoxItem>

                    <ListBoxItem>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Email" Width="20" Height="20" Margin="0,0,8,0"/>
                            <TextBlock Text="إرسال تذكرة دعم"/>
                        </StackPanel>
                    </ListBoxItem>

                    <ListBoxItem>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Chat" Width="20" Height="20" Margin="0,0,8,0"/>
                            <TextBlock Text="الدردشة المباشرة"/>
                        </StackPanel>
                    </ListBoxItem>

                    <ListBoxItem>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Phone" Width="20" Height="20" Margin="0,0,8,0"/>
                            <TextBlock Text="الاتصال المباشر"/>
                        </StackPanel>
                    </ListBoxItem>

                    <ListBoxItem>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Book" Width="20" Height="20" Margin="0,0,8,0"/>
                            <TextBlock Text="دليل المستخدم"/>
                        </StackPanel>
                    </ListBoxItem>

                    <ListBoxItem>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Video" Width="20" Height="20" Margin="0,0,8,0"/>
                            <TextBlock Text="فيديوهات تعليمية"/>
                        </StackPanel>
                    </ListBoxItem>

                    <ListBoxItem>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Information" Width="20" Height="20" Margin="0,0,8,0"/>
                            <TextBlock Text="معلومات النظام"/>
                        </StackPanel>
                    </ListBoxItem>
                </ListBox>

                <!-- معلومات الاتصال السريع -->
                <Border Background="White" 
                      CornerRadius="8" 
                      Padding="12" 
                      Margin="0,32,0,0"
                      Opacity="0.9">
                    <StackPanel>
                        <TextBlock Text="🚨 دعم طارئ" 
                                 FontWeight="Bold" 
                                 Foreground="{DynamicResource PrimaryHueMidBrush}"
                                 Margin="0,0,0,8"/>
                        <TextBlock Text="📞 16567" 
                                 FontSize="12" 
                                 Margin="0,0,0,4"/>
                        <TextBlock Text="📧 <EMAIL>" 
                                 FontSize="12"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Border>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Column="1" VerticalScrollBarVisibility="Auto">
            <StackPanel x:Name="MainContentPanel" Margin="24">
                
                <!-- الأسئلة الشائعة (افتراضي) -->
                <StackPanel x:Name="FAQPanel">
                    <TextBlock Text="❓ الأسئلة الشائعة" 
                             Style="{DynamicResource MaterialDesignHeadline4TextBlock}"
                             Margin="0,0,0,24"/>

                    <!-- سؤال 1 -->
                    <Expander Header="كيف أقوم بتفعيل الترخيص؟" Margin="0,0,0,8">
                        <TextBlock TextWrapping="Wrap" Margin="16,8">
                            1. اذهب إلى قائمة "مساعدة" ← "تفعيل الترخيص"<LineBreak/>
                            2. أدخل مفتاح الترخيص الذي حصلت عليه<LineBreak/>
                            3. أدخل اسمك والبريد الإلكتروني<LineBreak/>
                            4. اضغط "تفعيل الترخيص"<LineBreak/>
                            5. ستظهر رسالة تأكيد عند نجاح التفعيل
                        </TextBlock>
                    </Expander>

                    <!-- سؤال 2 -->
                    <Expander Header="كيف أقوم بإنشاء فاتورة جديدة؟" Margin="0,0,0,8">
                        <TextBlock TextWrapping="Wrap" Margin="16,8">
                            1. اذهب إلى قسم "الفواتير"<LineBreak/>
                            2. اضغط على "فاتورة جديدة"<LineBreak/>
                            3. اختر العميل أو أضف عميل جديد<LineBreak/>
                            4. أضف المنتجات والكميات<LineBreak/>
                            5. تأكد من الضرائب والخصومات<LineBreak/>
                            6. احفظ الفاتورة واطبعها
                        </TextBlock>
                    </Expander>

                    <!-- سؤال 3 -->
                    <Expander Header="كيف أقوم بعمل نسخة احتياطية؟" Margin="0,0,0,8">
                        <TextBlock TextWrapping="Wrap" Margin="16,8">
                            1. اذهب إلى "الإعدادات" ← "النسخ الاحتياطي"<LineBreak/>
                            2. اختر مكان حفظ النسخة الاحتياطية<LineBreak/>
                            3. اضغط "إنشاء نسخة احتياطية"<LineBreak/>
                            4. انتظر حتى اكتمال العملية<LineBreak/>
                            5. احتفظ بالنسخة في مكان آمن
                        </TextBlock>
                    </Expander>

                    <!-- سؤال 4 -->
                    <Expander Header="ماذا أفعل إذا نسيت كلمة المرور؟" Margin="0,0,0,8">
                        <TextBlock TextWrapping="Wrap" Margin="16,8">
                            1. في شاشة تسجيل الدخول، اضغط "نسيت كلمة المرور"<LineBreak/>
                            2. أدخل اسم المستخدم<LineBreak/>
                            3. اتبع التعليمات لإعادة تعيين كلمة المرور<LineBreak/>
                            4. أو اتصل بالدعم الفني للمساعدة
                        </TextBlock>
                    </Expander>

                    <!-- سؤال 5 -->
                    <Expander Header="كيف أقوم بتحديث البرنامج؟" Margin="0,0,0,8">
                        <TextBlock TextWrapping="Wrap" Margin="16,8">
                            1. اذهب إلى "مساعدة" ← "التحقق من التحديثات"<LineBreak/>
                            2. إذا كان هناك تحديث، اضغط "تحميل"<LineBreak/>
                            3. انتظر حتى اكتمال التحميل<LineBreak/>
                            4. أعد تشغيل البرنامج<LineBreak/>
                            5. أو فعل التحديث التلقائي من الإعدادات
                        </TextBlock>
                    </Expander>
                </StackPanel>

                <!-- إرسال تذكرة دعم -->
                <StackPanel x:Name="TicketPanel" Visibility="Collapsed">
                    <TextBlock Text="📧 إرسال تذكرة دعم" 
                             Style="{DynamicResource MaterialDesignHeadline4TextBlock}"
                             Margin="0,0,0,24"/>

                    <TextBox x:Name="TicketSubjectTextBox"
                           materialDesign:HintAssist.Hint="موضوع المشكلة"
                           materialDesign:HintAssist.IsFloating="True"
                           Style="{DynamicResource MaterialDesignOutlinedTextBox}"
                           Margin="0,0,0,16"/>

                    <ComboBox x:Name="TicketPriorityComboBox"
                            materialDesign:HintAssist.Hint="أولوية المشكلة"
                            materialDesign:HintAssist.IsFloating="True"
                            Style="{DynamicResource MaterialDesignOutlinedComboBox}"
                            Margin="0,0,0,16">
                        <ComboBoxItem Content="منخفضة"/>
                        <ComboBoxItem Content="متوسطة" IsSelected="True"/>
                        <ComboBoxItem Content="عالية"/>
                        <ComboBoxItem Content="طارئة"/>
                    </ComboBox>

                    <TextBox x:Name="TicketDescriptionTextBox"
                           materialDesign:HintAssist.Hint="وصف تفصيلي للمشكلة"
                           materialDesign:HintAssist.IsFloating="True"
                           Style="{DynamicResource MaterialDesignOutlinedTextBox}"
                           AcceptsReturn="True"
                           TextWrapping="Wrap"
                           MinHeight="120"
                           Margin="0,0,0,16"/>

                    <Button x:Name="SendTicketButton"
                          Content="إرسال التذكرة"
                          Style="{DynamicResource MaterialDesignRaisedButton}"
                          HorizontalAlignment="Left"
                          Click="SendTicketButton_Click"/>
                </StackPanel>

                <!-- معلومات النظام -->
                <StackPanel x:Name="SystemInfoPanel" Visibility="Collapsed">
                    <TextBlock Text="💻 معلومات النظام" 
                             Style="{DynamicResource MaterialDesignHeadline4TextBlock}"
                             Margin="0,0,0,24"/>

                    <materialDesign:Card Padding="16" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock x:Name="SystemInfoText" 
                                     TextWrapping="Wrap"
                                     FontFamily="Consolas"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <Button Content="نسخ معلومات النظام"
                          Style="{DynamicResource MaterialDesignOutlinedButton}"
                          HorizontalAlignment="Left"
                          Click="CopySystemInfoButton_Click"/>
                </StackPanel>

            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>
