@echo off
title تحويل Python إلى EXE - Nuitka (الأسرع)
color 0D
echo.
echo ========================================================
echo    تحويل Python إلى EXE - Nuitka (الأسرع)
echo ========================================================
echo.

echo ⚡ Nuitka - أسرع من PyInstaller!
echo ✅ تحويل Python إلى C++ ثم EXE
echo 🚀 أداء أفضل وحجم أصغر
echo.

echo [1] التحقق من Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت
    echo 📥 يرجى تثبيت Python أولاً من: https://www.python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python مثبت

echo.
echo [2] التحقق من Nuitka...
python -c "import nuitka" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Nuitka غير مثبت
    echo 📦 جاري تثبيت Nuitka...
    pip install nuitka
    if %errorlevel% neq 0 (
        echo ❌ فشل تثبيت Nuitka
        echo 🔧 جرب: pip install --user nuitka
        pause
        exit /b 1
    )
)
echo ✅ Nuitka متوفر

echo.
echo [3] التحقق من C++ Compiler...
echo ⚠️  Nuitka يحتاج C++ compiler...
echo 💡 إذا لم يكن مثبت، سيتم تحميله تلقائياً

echo.
echo [4] التحقق من ملف البرنامج...
if not exist "accounting_system_python.py" (
    echo ❌ ملف البرنامج غير موجود
    echo يرجى التأكد من وجود ملف accounting_system_python.py
    pause
    exit /b 1
)
echo ✅ ملف البرنامج موجود

echo.
echo [5] إنشاء مجلد الإخراج...
if not exist "EXE_Nuitka" mkdir "EXE_Nuitka"
echo ✅ مجلد الإخراج جاهز

echo.
echo [6] تحويل Python إلى EXE باستخدام Nuitka...
echo ⏳ هذا قد يستغرق وقت أطول في أول مرة (تحميل compiler)...
echo.

REM تحويل باستخدام Nuitka
python -m nuitka ^
    --onefile ^
    --windows-disable-console ^
    --output-dir="EXE_Nuitka" ^
    --output-filename="نظام_المحاسبة_11_Nuitka.exe" ^
    --enable-plugin=tk-inter ^
    --include-module=sqlite3 ^
    --include-module=tkinter ^
    --include-module=hashlib ^
    --include-module=datetime ^
    accounting_system_python.py

if %errorlevel% equ 0 (
    echo.
    echo ========================================================
    echo 🎉 تم تحويل Python إلى EXE بنجاح باستخدام Nuitka!
    echo ========================================================
    echo.
    
    REM حساب حجم الملف
    for %%A in ("EXE_Nuitka\نظام_المحاسبة_11_Nuitka.exe") do (
        set "filesize=%%~zA"
        set /a "filesize_mb=%%~zA/1024/1024"
    )
    
    echo 📁 مكان الملف: EXE_Nuitka\
    echo 📄 اسم الملف: نظام_المحاسبة_11_Nuitka.exe
    echo 💾 حجم الملف: %filesize_mb% MB تقريباً
    echo.
    echo ⚡ مميزات Nuitka:
    echo    • أسرع في التشغيل من PyInstaller
    echo    • حجم أصغر
    echo    • أداء محسن
    echo    • تحويل إلى C++ ثم EXE
    echo    • لا يحتاج Python للتشغيل
    echo.
    
    REM إنشاء ملفات مساعدة
    echo 📋 إنشاء ملفات مساعدة...
    
    REM ملف تشغيل
    echo @echo off > "EXE_Nuitka\تشغيل.bat"
    echo title نظام المحاسبة 11 - Nuitka EXE >> "EXE_Nuitka\تشغيل.bat"
    echo color 0D >> "EXE_Nuitka\تشغيل.bat"
    echo echo ======================================== >> "EXE_Nuitka\تشغيل.bat"
    echo echo      نظام المحاسبة 11 - Nuitka EXE >> "EXE_Nuitka\تشغيل.bat"
    echo echo ======================================== >> "EXE_Nuitka\تشغيل.bat"
    echo echo. >> "EXE_Nuitka\تشغيل.bat"
    echo echo ⚡ محول بـ Nuitka - الأسرع! >> "EXE_Nuitka\تشغيل.bat"
    echo echo ✅ أداء محسن وحجم أصغر >> "EXE_Nuitka\تشغيل.bat"
    echo echo 🚀 جاري التشغيل... >> "EXE_Nuitka\تشغيل.bat"
    echo echo. >> "EXE_Nuitka\تشغيل.bat"
    echo "نظام_المحاسبة_11_Nuitka.exe" >> "EXE_Nuitka\تشغيل.bat"
    echo pause >> "EXE_Nuitka\تشغيل.bat"
    
    REM ملف README
    echo # نظام المحاسبة 11 - إصدار Nuitka EXE > "EXE_Nuitka\README.txt"
    echo. >> "EXE_Nuitka\README.txt"
    echo ## المميزات الخاصة بـ Nuitka: >> "EXE_Nuitka\README.txt"
    echo - أسرع في التشغيل من PyInstaller >> "EXE_Nuitka\README.txt"
    echo - حجم أصغر >> "EXE_Nuitka\README.txt"
    echo - أداء محسن >> "EXE_Nuitka\README.txt"
    echo - تحويل Python إلى C++ ثم EXE >> "EXE_Nuitka\README.txt"
    echo - لا يحتاج Python للتشغيل >> "EXE_Nuitka\README.txt"
    echo. >> "EXE_Nuitka\README.txt"
    echo ## طريقة التشغيل: >> "EXE_Nuitka\README.txt"
    echo 1. اضغط دبل كليك على تشغيل.bat >> "EXE_Nuitka\README.txt"
    echo 2. أو اضغط دبل كليك على نظام_المحاسبة_11_Nuitka.exe >> "EXE_Nuitka\README.txt"
    
    echo ✅ تم إنشاء الملفات المساعدة
    echo.
    
    REM إنشاء ملف مضغوط
    echo 📦 إنشاء ملف مضغوط...
    powershell -Command "Compress-Archive -Path 'EXE_Nuitka\*' -DestinationPath 'EXE_Nuitka\نظام_المحاسبة_11_Nuitka.zip' -Force" >nul 2>&1
    if exist "EXE_Nuitka\نظام_المحاسبة_11_Nuitka.zip" (
        echo ✅ تم إنشاء ملف مضغوط
    )
    
    echo.
    echo 🎯 الملفات الجاهزة:
    echo 📁 EXE_Nuitka\ - المجلد الكامل
    echo 📄 نظام_المحاسبة_11_Nuitka.exe - الملف التنفيذي (الأسرع)
    echo 📄 تشغيل.bat - ملف تشغيل سهل
    echo 📄 README.txt - تعليمات الاستخدام
    echo 📦 نظام_المحاسبة_11_Nuitka.zip - ملف مضغوط
    echo.
    
    set /p choice="هل تريد فتح مجلد الملفات؟ (y/n): "
    if /i "%choice%"=="y" (
        explorer "EXE_Nuitka"
    )
    
    echo.
    set /p choice2="هل تريد اختبار التشغيل؟ (y/n): "
    if /i "%choice2%"=="y" (
        cd "EXE_Nuitka"
        echo ⚡ جاري اختبار التشغيل السريع...
        "نظام_المحاسبة_11_Nuitka.exe"
    )
    
) else (
    echo.
    echo ❌ فشل التحويل باستخدام Nuitka
    echo.
    echo 🔧 الحلول:
    echo 1. تأكد من اتصال الإنترنت (لتحميل C++ compiler)
    echo 2. شغل كـ Administrator
    echo 3. تأكد من وجود مساحة كافية (1 GB)
    echo 4. جرب PyInstaller بدلاً من Nuitka
    echo.
    echo 💡 أو جرب الأمر اليدوي:
    echo python -m nuitka --onefile --windows-disable-console accounting_system_python.py
)

echo.
pause
