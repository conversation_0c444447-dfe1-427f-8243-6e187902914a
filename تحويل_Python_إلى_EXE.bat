@echo off
title تحويل Python إلى EXE - نظام المحاسبة 11
color 0B
echo.
echo ========================================================
echo      تحويل Python إلى EXE - نظام المحاسبة 11
echo ========================================================
echo.

echo 🔨 جاري تحويل برنامج Python إلى ملف .exe...
echo ✅ سيعمل بدون تثبيت Python
echo 🚀 ملف واحد مستقل
echo.

echo [1] التحقق من Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت
    echo 📥 يرجى تثبيت Python أولاً من: https://www.python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python مثبت

echo.
echo [2] التحقق من PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PyInstaller غير مثبت
    echo 📦 جاري تثبيت PyInstaller...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo ❌ فشل تثبيت PyInstaller
        echo 🔧 جرب: pip install --user pyinstaller
        pause
        exit /b 1
    )
)
echo ✅ PyInstaller متوفر

echo.
echo [3] التحقق من ملف البرنامج...
if not exist "accounting_system_python.py" (
    echo ❌ ملف البرنامج غير موجود
    echo يرجى التأكد من وجود ملف accounting_system_python.py
    pause
    exit /b 1
)
echo ✅ ملف البرنامج موجود

echo.
echo [4] إنشاء مجلد الإخراج...
if not exist "EXE_Python" mkdir "EXE_Python"
echo ✅ مجلد الإخراج جاهز

echo.
echo [5] تحويل Python إلى EXE...
echo ⏳ هذا قد يستغرق دقائق قليلة...
echo.

REM تحويل إلى ملف exe واحد
pyinstaller --onefile ^
    --windowed ^
    --name "نظام_المحاسبة_11_Python" ^
    --distpath "EXE_Python" ^
    --workpath "build_temp" ^
    --specpath "build_temp" ^
    --add-data "accounting_system_python.py;." ^
    --hidden-import tkinter ^
    --hidden-import sqlite3 ^
    --icon=NONE ^
    accounting_system_python.py

if %errorlevel% equ 0 (
    echo.
    echo ========================================================
    echo 🎉 تم تحويل Python إلى EXE بنجاح!
    echo ========================================================
    echo.
    
    REM حساب حجم الملف
    for %%A in ("EXE_Python\نظام_المحاسبة_11_Python.exe") do (
        set "filesize=%%~zA"
        set /a "filesize_mb=%%~zA/1024/1024"
    )
    
    echo 📁 مكان الملف: EXE_Python\
    echo 📄 اسم الملف: نظام_المحاسبة_11_Python.exe
    echo 💾 حجم الملف: %filesize_mb% MB تقريباً
    echo.
    echo ✅ المميزات:
    echo    • ملف واحد مستقل (.exe)
    echo    • لا يحتاج تثبيت Python
    echo    • يعمل على أي جهاز Windows
    echo    • واجهة رسومية كاملة
    echo    • قاعدة بيانات SQLite مدمجة
    echo    • سريع التشغيل
    echo.
    
    REM إنشاء ملفات مساعدة
    echo 📋 إنشاء ملفات مساعدة...
    
    REM ملف تشغيل
    echo @echo off > "EXE_Python\تشغيل.bat"
    echo title نظام المحاسبة 11 - Python EXE >> "EXE_Python\تشغيل.bat"
    echo color 0A >> "EXE_Python\تشغيل.bat"
    echo echo ======================================== >> "EXE_Python\تشغيل.bat"
    echo echo      نظام المحاسبة 11 - Python EXE >> "EXE_Python\تشغيل.bat"
    echo echo ======================================== >> "EXE_Python\تشغيل.bat"
    echo echo. >> "EXE_Python\تشغيل.bat"
    echo echo 🐍 برنامج Python محول إلى EXE >> "EXE_Python\تشغيل.bat"
    echo echo ✅ لا يحتاج تثبيت Python >> "EXE_Python\تشغيل.bat"
    echo echo 🚀 جاري التشغيل... >> "EXE_Python\تشغيل.bat"
    echo echo. >> "EXE_Python\تشغيل.bat"
    echo echo 🔐 بيانات تسجيل الدخول: >> "EXE_Python\تشغيل.bat"
    echo echo 👤 اسم المستخدم: admin >> "EXE_Python\تشغيل.bat"
    echo echo 🔑 كلمة المرور: admin123 >> "EXE_Python\تشغيل.bat"
    echo echo. >> "EXE_Python\تشغيل.bat"
    echo "نظام_المحاسبة_11_Python.exe" >> "EXE_Python\تشغيل.bat"
    echo if %%errorlevel%% neq 0 ( >> "EXE_Python\تشغيل.bat"
    echo     echo ❌ حدث خطأ في التشغيل >> "EXE_Python\تشغيل.bat"
    echo     pause >> "EXE_Python\تشغيل.bat"
    echo ^) >> "EXE_Python\تشغيل.bat"
    
    REM ملف README
    echo # نظام المحاسبة 11 - إصدار Python EXE > "EXE_Python\README.txt"
    echo. >> "EXE_Python\README.txt"
    echo ## المميزات: >> "EXE_Python\README.txt"
    echo - ملف واحد مستقل (.exe) >> "EXE_Python\README.txt"
    echo - لا يحتاج تثبيت Python >> "EXE_Python\README.txt"
    echo - واجهة رسومية كاملة >> "EXE_Python\README.txt"
    echo - قاعدة بيانات SQLite مدمجة >> "EXE_Python\README.txt"
    echo - إدارة العملاء والمنتجات >> "EXE_Python\README.txt"
    echo - لوحة تحكم تفاعلية >> "EXE_Python\README.txt"
    echo. >> "EXE_Python\README.txt"
    echo ## طريقة التشغيل: >> "EXE_Python\README.txt"
    echo 1. اضغط دبل كليك على تشغيل.bat >> "EXE_Python\README.txt"
    echo 2. أو اضغط دبل كليك على نظام_المحاسبة_11_Python.exe >> "EXE_Python\README.txt"
    echo. >> "EXE_Python\README.txt"
    echo ## بيانات تسجيل الدخول: >> "EXE_Python\README.txt"
    echo اسم المستخدم: admin >> "EXE_Python\README.txt"
    echo كلمة المرور: admin123 >> "EXE_Python\README.txt"
    echo. >> "EXE_Python\README.txt"
    echo ## المتطلبات: >> "EXE_Python\README.txt"
    echo - Windows 7 أو أحدث >> "EXE_Python\README.txt"
    echo - لا يحتاج تثبيت Python >> "EXE_Python\README.txt"
    echo - لا يحتاج .NET Framework >> "EXE_Python\README.txt"
    
    echo ✅ تم إنشاء الملفات المساعدة
    echo.
    
    REM تنظيف الملفات المؤقتة
    echo 🧹 تنظيف الملفات المؤقتة...
    if exist "build_temp" rmdir /s /q "build_temp" >nul 2>&1
    if exist "dist" rmdir /s /q "dist" >nul 2>&1
    if exist "*.spec" del "*.spec" >nul 2>&1
    echo ✅ تم التنظيف
    
    REM إنشاء ملف مضغوط
    echo 📦 إنشاء ملف مضغوط للتوزيع...
    powershell -Command "Compress-Archive -Path 'EXE_Python\*' -DestinationPath 'EXE_Python\نظام_المحاسبة_11_Python.zip' -Force" >nul 2>&1
    if exist "EXE_Python\نظام_المحاسبة_11_Python.zip" (
        echo ✅ تم إنشاء ملف مضغوط: نظام_المحاسبة_11_Python.zip
    )
    
    echo.
    echo 🎯 ملخص الملفات المُنشأة:
    echo 📁 EXE_Python\ - المجلد الكامل
    echo 📄 نظام_المحاسبة_11_Python.exe - الملف التنفيذي
    echo 📄 تشغيل.bat - ملف تشغيل سهل
    echo 📄 README.txt - تعليمات الاستخدام
    echo 📦 نظام_المحاسبة_11_Python.zip - ملف مضغوط للتوزيع
    echo.
    
    set /p choice="هل تريد فتح مجلد الملفات؟ (y/n): "
    if /i "%choice%"=="y" (
        explorer "EXE_Python"
    )
    
    echo.
    set /p choice2="هل تريد اختبار التشغيل؟ (y/n): "
    if /i "%choice2%"=="y" (
        cd "EXE_Python"
        echo 🚀 جاري اختبار التشغيل...
        echo ⚠️  قد يستغرق وقت أطول في أول تشغيل
        "نظام_المحاسبة_11_Python.exe"
    )
    
) else (
    echo.
    echo ❌ فشل تحويل Python إلى EXE
    echo.
    echo 🔧 جرب الحلول التالية:
    echo 1. تأكد من تثبيت Python بشكل صحيح
    echo 2. شغل Command Prompt كـ Administrator
    echo 3. تأكد من وجود مساحة كافية (500 MB)
    echo 4. جرب: pip install --upgrade pyinstaller
    echo.
    echo 💡 أو جرب الأمر اليدوي:
    echo pyinstaller --onefile --windowed accounting_system_python.py
)

echo.
pause
