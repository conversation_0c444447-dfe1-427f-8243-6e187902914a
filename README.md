# نظام المحاسبة 11 - Accounting System 11

نظام محاسبة متكامل مصمم خصيصاً للسوق المصري مع دعم كامل للضرائب المصرية والفاتورة الإلكترونية.

## المميزات الرئيسية

### 🏗️ التقنيات المستخدمة
- **C# .NET 6** - لغة برمجة احترافية عالية الأداء
- **WPF** - واجهة مستخدم حديثة وسريعة الاستجابة
- **Entity Framework Core** - قاعدة بيانات متقدمة
- **Material Design** - تصميم عصري وجذاب
- **SQL Server LocalDB** - قاعدة بيانات محلية موثوقة

### 💼 الوظائف الأساسية
- ✅ **إدارة العملاء** - إضافة وتعديل وحذف العملاء مع دعم الأرقام الضريبية
- ✅ **إدارة المنتجات** - كتالوج شامل للمنتجات مع الباركود والفئات
- ✅ **نظام الفواتير** - إنشاء وطباعة الفواتير مع حساب الضرائب تلقائياً
- ✅ **إدارة المخزون** - تتبع المخزون مع تنبيهات النفاد
- ✅ **التقارير المالية** - تقارير شاملة للمبيعات والأرباح
- ✅ **نظام المستخدمين** - إدارة الصلاحيات والأمان

### 🇪🇬 دعم السوق المصري
- **الجنيه المصري** - العملة الافتراضية
- **ضريبة القيمة المضافة** - حساب تلقائي بنسبة 14%
- **الفاتورة الإلكترونية** - جاهز للربط مع منظومة الضرائب المصرية
- **اللغة العربية** - واجهة باللغة العربية بالكامل
- **التاريخ الهجري والميلادي** - دعم كامل للتواريخ

### 📊 لوحة التحكم
- إحصائيات المبيعات اليومية والشهرية
- مؤشرات الأداء الرئيسية (KPIs)
- تنبيهات المخزون المنخفض
- الفواتير المتأخرة السداد
- الأنشطة الأخيرة

### 🏪 مناسب لجميع الأنشطة التجارية
- **سوبر ماركت** - إدارة المنتجات الاستهلاكية
- **محلات الملابس** - إدارة المقاسات والألوان
- **المطاعم** - إدارة الوجبات والمشروبات
- **الصيدليات** - إدارة الأدوية وتواريخ الانتهاء
- **ورش الصيانة** - إدارة الخدمات وقطع الغيار

## متطلبات التشغيل

### الحد الأدنى
- **نظام التشغيل**: Windows 7 SP1 أو أحدث
- **المعالج**: Intel Core i3 أو AMD equivalent
- **الذاكرة**: 4 GB RAM
- **التخزين**: 2 GB مساحة فارغة
- **.NET Runtime**: .NET 6.0 Desktop Runtime

### المستوى المُوصى به
- **نظام التشغيل**: Windows 10/11
- **المعالج**: Intel Core i5 أو أحدث
- **الذاكرة**: 8 GB RAM أو أكثر
- **التخزين**: 5 GB مساحة فارغة (SSD مُفضل)
- **الشاشة**: 1920x1080 أو أعلى

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
# تحميل وتثبيت .NET 6.0 Desktop Runtime
# من الموقع الرسمي: https://dotnet.microsoft.com/download/dotnet/6.0
```

### 2. تشغيل المشروع للمطورين
```bash
# استنساخ المشروع
git clone https://github.com/your-repo/AccountingSystem11.git

# الانتقال لمجلد المشروع
cd AccountingSystem11

# استعادة الحزم
dotnet restore

# تشغيل المشروع
dotnet run
```

### 3. بناء المشروع للإنتاج
```bash
# بناء المشروع
dotnet build --configuration Release

# نشر المشروع
dotnet publish --configuration Release --self-contained true --runtime win-x64
```

## الاستخدام

### تسجيل الدخول الافتراضي
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

⚠️ **مهم**: يُرجى تغيير كلمة المرور الافتراضية فور تسجيل الدخول الأول.

### الخطوات الأولى
1. **إعداد بيانات الشركة** - من قائمة الإعدادات
2. **إضافة فئات المنتجات** - تصنيف المنتجات
3. **إدخال المنتجات** - إضافة كتالوج المنتجات
4. **إضافة العملاء** - قاعدة بيانات العملاء
5. **إنشاء أول فاتورة** - بدء العمل

## هيكل المشروع

```
AccountingSystem11/
├── Models/              # نماذج البيانات
├── Views/               # واجهات المستخدم
├── ViewModels/          # منطق الواجهات
├── Services/            # الخدمات والمنطق التجاري
├── Data/                # قاعدة البيانات
├── Helpers/             # الوظائف المساعدة
├── Resources/           # الموارد والملفات
└── Reports/             # قوالب التقارير
```

## المساهمة في المشروع

نرحب بمساهماتكم في تطوير النظام:

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للـ branch (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## الدعم والمساعدة

### التوثيق
- [دليل المستخدم](docs/user-guide.md)
- [دليل المطور](docs/developer-guide.md)
- [الأسئلة الشائعة](docs/faq.md)

### التواصل
- **البريد الإلكتروني**: <EMAIL>
- **الموقع الإلكتروني**: https://system11.com
- **الدعم الفني**: +20 xxx xxx xxxx

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الإصدارات

### الإصدار 1.0.0 (الحالي)
- ✅ النظام الأساسي للمحاسبة
- ✅ إدارة العملاء والمنتجات
- ✅ نظام الفواتير مع الضرائب
- ✅ لوحة التحكم والتقارير الأساسية
- ✅ نظام المستخدمين والصلاحيات

### الإصدارات القادمة
- 🔄 **الإصدار 1.1.0** - تكامل Excel الكامل
- 🔄 **الإصدار 1.2.0** - الفاتورة الإلكترونية المصرية
- 🔄 **الإصدار 1.3.0** - تطبيق الهاتف المحمول
- 🔄 **الإصدار 2.0.0** - النسخة السحابية

## شكر خاص

شكر خاص لجميع المساهمين والمطورين الذين ساعدوا في إنجاز هذا المشروع.

---

**نظام المحاسبة 11** - حلول محاسبية ذكية للأعمال المصرية 🇪🇬
