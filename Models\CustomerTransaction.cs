using System;
using System.ComponentModel.DataAnnotations;

namespace AccountingSystem11.Models
{
    /// <summary>
    /// Customer transaction for tracking payments and credits
    /// </summary>
    public class CustomerTransaction : BaseEntity
    {
        [Required]
        public int CustomerId { get; set; }
        public virtual Customer Customer { get; set; }

        /// <summary>
        /// Related invoice (if any)
        /// </summary>
        public int? InvoiceId { get; set; }
        public virtual Invoice Invoice { get; set; }

        [Required]
        public DateTime TransactionDate { get; set; } = DateTime.Now;

        [Required]
        [MaxLength(20)]
        public string TransactionNumber { get; set; }

        /// <summary>
        /// Transaction type
        /// </summary>
        public TransactionType TransactionType { get; set; }

        /// <summary>
        /// Transaction amount (positive for debit, negative for credit)
        /// </summary>
        [Required]
        public decimal Amount { get; set; }

        /// <summary>
        /// Payment method used
        /// </summary>
        public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.Cash;

        /// <summary>
        /// Reference number (check number, transfer reference, etc.)
        /// </summary>
        [MaxLength(50)]
        public string ReferenceNumber { get; set; }

        /// <summary>
        /// Bank name (if applicable)
        /// </summary>
        [MaxLength(100)]
        public string BankName { get; set; }

        /// <summary>
        /// Transaction description
        /// </summary>
        [MaxLength(200)]
        public string Description { get; set; }

        /// <summary>
        /// Notes
        /// </summary>
        [MaxLength(500)]
        public string Notes { get; set; }

        /// <summary>
        /// Whether transaction is reconciled
        /// </summary>
        public bool IsReconciled { get; set; } = false;

        /// <summary>
        /// Reconciliation date
        /// </summary>
        public DateTime? ReconciliationDate { get; set; }
    }

    public enum TransactionType
    {
        Payment = 1,        // دفعة
        Receipt = 2,        // استلام
        CreditNote = 3,     // إشعار دائن
        DebitNote = 4,      // إشعار مدين
        Adjustment = 5,     // تسوية
        OpeningBalance = 6  // رصيد افتتاحي
    }
}
