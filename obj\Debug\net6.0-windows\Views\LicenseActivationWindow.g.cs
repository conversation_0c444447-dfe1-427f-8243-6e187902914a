﻿#pragma checksum "..\..\..\..\Views\LicenseActivationWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "CBC6270DFB4B5418554EE3305865F6395D1BCC56"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingSystem11.Views {
    
    
    /// <summary>
    /// LicenseActivationWindow
    /// </summary>
    public partial class LicenseActivationWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 52 "..\..\..\..\Views\LicenseActivationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentLicenseTitle;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\Views\LicenseActivationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LicenseTypeText;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\Views\LicenseActivationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ExpiryDateText;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\Views\LicenseActivationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FeaturesText;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\Views\LicenseActivationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CustomerNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\Views\LicenseActivationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CustomerEmailTextBox;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\Views\LicenseActivationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LicenseKeyTextBox;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\Views\LicenseActivationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ActivateButton;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\Views\LicenseActivationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BuyLicenseButton;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\Views\LicenseActivationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ContinueTrialButton;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\..\Views\LicenseActivationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusMessageTextBlock;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\..\..\Views\LicenseActivationWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar LoadingProgressBar;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingSystem11;V1.0.0.0;component/views/licenseactivationwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\LicenseActivationWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CurrentLicenseTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.LicenseTypeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.ExpiryDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.FeaturesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.CustomerNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.CustomerEmailTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.LicenseKeyTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 109 "..\..\..\..\Views\LicenseActivationWindow.xaml"
            this.LicenseKeyTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.LicenseKeyTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ActivateButton = ((System.Windows.Controls.Button)(target));
            
            #line 119 "..\..\..\..\Views\LicenseActivationWindow.xaml"
            this.ActivateButton.Click += new System.Windows.RoutedEventHandler(this.ActivateButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.BuyLicenseButton = ((System.Windows.Controls.Button)(target));
            
            #line 129 "..\..\..\..\Views\LicenseActivationWindow.xaml"
            this.BuyLicenseButton.Click += new System.Windows.RoutedEventHandler(this.BuyLicenseButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ContinueTrialButton = ((System.Windows.Controls.Button)(target));
            
            #line 135 "..\..\..\..\Views\LicenseActivationWindow.xaml"
            this.ContinueTrialButton.Click += new System.Windows.RoutedEventHandler(this.ContinueTrialButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.StatusMessageTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.LoadingProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

