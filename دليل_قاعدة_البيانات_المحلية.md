# 🗃️ دليل قاعدة البيانات المحلية - نظام المحاسبة 11

## 🎯 المميزات الجديدة

### ✅ قاعدة بيانات Access محلية
- **لا تحتاج إنترنت** - العمل بدون اتصال
- **سرعة عالية** - البيانات على جهازك مباشرة
- **أمان كامل** - بياناتك لا تخرج من جهازك
- **سهولة النسخ الاحتياطي** - ملف واحد فقط

### 🚀 التشغيل السريع

#### الطريقة الأسهل:
```
🖱️ اضغط دبل كليك على: تشغيل_محلي.bat
⏳ انتظر حتى يكتمل التحضير
🎉 سيفتح البرنامج تلقائياً
```

## 📋 المتطلبات

### 1. .NET 6.0 Desktop Runtime
```
🔗 الرابط: https://dotnet.microsoft.com/download/dotnet/6.0
📥 حمل: ".NET Desktop Runtime 6.0.x"
⚙️ ثبت على جهازك
```

### 2. Microsoft Access Database Engine
```
🔗 الرابط: https://www.microsoft.com/en-us/download/details.aspx?id=54920
📥 حمل: "Microsoft Access Database Engine 2016 Redistributable"
⚙️ ثبت على جهازك (مطلوب لقراءة ملفات Access)
```

## 🗂️ هيكل قاعدة البيانات

### الجداول الرئيسية:
- **Customers** - بيانات العملاء
- **Products** - كتالوج المنتجات  
- **ProductCategories** - فئات المنتجات
- **Suppliers** - بيانات الموردين
- **Users** - مستخدمي النظام

### مكان حفظ البيانات:
```
📁 المجلد: Data\
📄 الملف: AccountingSystem11.accdb
💾 الحجم: يبدأ من 2 MB تقريباً
```

## 🔐 بيانات تسجيل الدخول الافتراضية

```
👤 اسم المستخدم: admin
🔑 كلمة المرور: admin123
🔒 الصلاحية: مدير النظام (كامل الصلاحيات)
```

⚠️ **مهم جداً**: غير كلمة المرور فور تسجيل الدخول الأول!

## 📊 البيانات الأولية المُدخلة

### العملاء:
- عميل تجريبي واحد للاختبار

### فئات المنتجات:
- عام
- ملابس  
- أغذية ومشروبات
- إلكترونيات
- أدوية

### المستخدمين:
- مدير النظام (admin)

## 🛠️ إدارة قاعدة البيانات

### عرض البيانات:
```
📂 افتح: Data\AccountingSystem11.accdb
🖥️ باستخدام: Microsoft Access (إذا كان مثبت)
👀 أو أي برنامج يدعم ملفات .accdb
```

### النسخ الاحتياطي:
```
📁 انسخ مجلد: Data\
💾 احفظه في: مكان آمن (فلاشة، كلاود، إلخ)
🔄 كرر العملية: دورياً (يومياً/أسبوعياً)
```

### الاستعادة:
```
📁 انسخ مجلد Data المحفوظ
📂 الصقه في: مجلد البرنامج
🔄 استبدل الملفات الموجودة
🚀 شغل البرنامج
```

## ⚡ نصائح للأداء الأمثل

### لسرعة أكبر:
- 💾 استخدم SSD بدلاً من Hard Disk
- 🖥️ 8 GB RAM أو أكثر
- 🔄 أغلق البرامج غير المستخدمة

### للأمان:
- 🔒 غير كلمة المرور الافتراضية
- 💾 اعمل نسخ احتياطية دورية
- 🛡️ استخدم برنامج حماية من الفيروسات

## 🔧 حل المشاكل الشائعة

### المشكلة: "Provider not found"
```
✅ الحل: ثبت Microsoft Access Database Engine
🔗 الرابط: https://www.microsoft.com/en-us/download/details.aspx?id=54920
```

### المشكلة: "Database file not found"
```
✅ الحل 1: تأكد من وجود مجلد Data
✅ الحل 2: شغل البرنامج كـ Administrator
✅ الحل 3: تأكد من صلاحيات الكتابة في المجلد
```

### المشكلة: "Cannot open database"
```
✅ الحل 1: أغلق أي برنامج يستخدم ملف قاعدة البيانات
✅ الحل 2: أعد تشغيل الجهاز
✅ الحل 3: احذف ملف قاعدة البيانات ليتم إنشاؤه من جديد
```

### المشكلة: البرنامج بطيء
```
✅ الحل 1: أغلق البرامج الأخرى
✅ الحل 2: انقل مجلد البرنامج لـ SSD
✅ الحل 3: زيد الذاكرة RAM
```

## 📈 مراقبة الأداء

### حجم قاعدة البيانات:
- **البداية**: ~2 MB
- **بعد 100 عميل**: ~5 MB
- **بعد 1000 منتج**: ~10 MB
- **بعد 1000 فاتورة**: ~20 MB

### الحد الأقصى لـ Access:
- **حجم الملف**: 2 GB
- **عدد الجداول**: 32,768
- **عدد السجلات**: محدود بحجم الملف فقط

## 🔄 الترقية المستقبلية

### إذا احتجت قاعدة بيانات أكبر:
1. **SQL Server Express** - مجاني، أقوى من Access
2. **MySQL** - مفتوح المصدر
3. **PostgreSQL** - قوي ومجاني

### النقل من Access:
- البرنامج يدعم SQL Server أيضاً
- يمكن تصدير البيانات من Access
- استيراد البيانات في النظام الجديد

## 📞 الدعم الفني

### للمساعدة:
- 📧 البريد: <EMAIL>
- 📱 الهاتف: +20 xxx xxx xxxx
- 🌐 الموقع: https://system11.com

### الموارد المفيدة:
- [دليل المستخدم الكامل](user-guide.md)
- [الأسئلة الشائعة](faq.md)
- [فيديوهات تعليمية](tutorials.md)

---

**نظام المحاسبة 11** - قاعدة بيانات محلية آمنة وسريعة 🇪🇬
