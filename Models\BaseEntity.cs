using System;
using System.ComponentModel.DataAnnotations;

namespace AccountingSystem11.Models
{
    /// <summary>
    /// Base entity class for all database entities
    /// </summary>
    public abstract class BaseEntity
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        [MaxLength(100)]
        public string CreatedBy { get; set; }

        [MaxLength(100)]
        public string UpdatedBy { get; set; }

        public bool IsDeleted { get; set; } = false;

        public DateTime? DeletedAt { get; set; }

        [MaxLength(100)]
        public string DeletedBy { get; set; }

        /// <summary>
        /// Marks the entity as deleted (soft delete)
        /// </summary>
        /// <param name="deletedBy">User who deleted the entity</param>
        public virtual void Delete(string deletedBy = null)
        {
            IsDeleted = true;
            DeletedAt = DateTime.Now;
            DeletedBy = deletedBy;
        }

        /// <summary>
        /// Updates the entity's modification timestamp
        /// </summary>
        /// <param name="updatedBy">User who updated the entity</param>
        public virtual void Update(string updatedBy = null)
        {
            UpdatedAt = DateTime.Now;
            UpdatedBy = updatedBy;
        }
    }
}
