@echo off
title تثبيت متطلبات نظام المحاسبة 11
color 0E
echo.
echo ========================================================
echo         تثبيت متطلبات نظام المحاسبة 11
echo ========================================================
echo.
echo هذا الملف سيساعدك في تثبيت جميع المتطلبات اللازمة
echo لتشغيل نظام المحاسبة 11 مع قاعدة البيانات المحلية
echo.

echo [1] التحقق من .NET 6.0...
dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ .NET 6.0 مثبت بالفعل
    dotnet --version
) else (
    echo ❌ .NET 6.0 غير مثبت
    echo.
    echo 📥 جاري فتح صفحة التحميل...
    start https://dotnet.microsoft.com/download/dotnet/6.0
    echo.
    echo 📋 تعليمات التثبيت:
    echo    1. اختر ".NET Desktop Runtime 6.0.x"
    echo    2. حمل النسخة المناسبة لنظامك (x64 عادة)
    echo    3. ثبت البرنامج
    echo    4. أعد تشغيل هذا الملف للتحقق
    echo.
    pause
    exit /b 1
)

echo.
echo [2] التحقق من Microsoft Access Database Engine...

REM التحقق من وجود Access Database Engine
set "ACCESS_FOUND=0"

REM التحقق من Office 365/2019
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Office\ClickToRun\REGISTRY\MACHINE\Software\Classes\CLSID\{3BE786A4-5C15-11DB-B0DE-0800200C9A66}" >nul 2>&1
if %errorlevel% equ 0 set "ACCESS_FOUND=1"

REM التحقق من Access Database Engine المستقل
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Classes\CLSID\{3BE786A4-5C15-11DB-B0DE-0800200C9A66}" >nul 2>&1
if %errorlevel% equ 0 set "ACCESS_FOUND=1"

REM التحقق من النسخة 64-bit
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Classes\CLSID\{3BE786A4-5C15-11DB-B0DE-0800200C9A66}" >nul 2>&1
if %errorlevel% equ 0 set "ACCESS_FOUND=1"

if "%ACCESS_FOUND%"=="1" (
    echo ✅ Microsoft Access Database Engine مثبت
) else (
    echo ❌ Microsoft Access Database Engine غير مثبت
    echo.
    echo 📥 جاري فتح صفحة التحميل...
    start https://www.microsoft.com/en-us/download/details.aspx?id=54920
    echo.
    echo 📋 تعليمات التثبيت:
    echo    1. حمل "Microsoft Access Database Engine 2016 Redistributable"
    echo    2. اختر النسخة المناسبة:
    echo       - AccessDatabaseEngine.exe (32-bit)
    echo       - AccessDatabaseEngine_X64.exe (64-bit) - مُوصى به
    echo    3. ثبت البرنامج
    echo    4. أعد تشغيل هذا الملف للتحقق
    echo.
    echo ⚠️  ملاحظة: إذا كان لديك Office مثبت، قد تحتاج لاستخدام:
    echo    /quiet في نهاية أمر التثبيت
    echo.
    pause
    exit /b 1
)

echo.
echo [3] التحقق من مجلد البيانات...
if not exist "Data" (
    mkdir "Data"
    echo ✅ تم إنشاء مجلد البيانات
) else (
    echo ✅ مجلد البيانات موجود
)

echo.
echo [4] اختبار الاتصال بقاعدة البيانات...

REM إنشاء ملف VBS لاختبار الاتصال
echo Set conn = CreateObject("ADODB.Connection") > test_access.vbs
echo conn.Open "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=test.accdb;" >> test_access.vbs
echo WScript.Echo "SUCCESS" >> test_access.vbs
echo conn.Close >> test_access.vbs

REM تشغيل الاختبار
cscript //nologo test_access.vbs >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ اختبار الاتصال نجح
) else (
    echo ❌ فشل اختبار الاتصال
    echo.
    echo قد تحتاج لتثبيت Microsoft Access Database Engine
    echo أو إعادة تشغيل الجهاز بعد التثبيت
)

REM حذف ملف الاختبار
del test_access.vbs >nul 2>&1
del test.accdb >nul 2>&1

echo.
echo ========================================================
echo 🎉 تم فحص جميع المتطلبات!
echo ========================================================
echo.

if "%ACCESS_FOUND%"=="1" (
    echo ✅ جميع المتطلبات مثبتة بنجاح
    echo.
    echo 🚀 يمكنك الآن تشغيل البرنامج باستخدام:
    echo    📁 تشغيل_محلي.bat
    echo.
    echo أو
    echo.
    echo    📁 تشغيل_سريع.bat
    echo.
    
    set /p choice="هل تريد تشغيل البرنامج الآن؟ (y/n): "
    if /i "%choice%"=="y" (
        echo.
        echo 🚀 جاري تشغيل البرنامج...
        call تشغيل_محلي.bat
    )
) else (
    echo ⚠️  يرجى تثبيت المتطلبات المفقودة أولاً
    echo.
    echo 📋 قائمة المتطلبات:
    echo    1. .NET 6.0 Desktop Runtime
    echo    2. Microsoft Access Database Engine 2016
    echo.
    echo بعد التثبيت، أعد تشغيل هذا الملف
)

echo.
pause
