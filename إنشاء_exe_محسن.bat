@echo off
title إنشاء EXE محسن - نظام المحاسبة 11
color 0A
echo.
echo ========================================================
echo       إنشاء EXE محسن - نظام المحاسبة 11
echo ========================================================
echo.

echo 🔨 جاري إنشاء ملف .exe محسن...
echo.

REM إنشاء مجلد الإخراج
if not exist "Release" mkdir "Release"

echo [1] تنظيف شامل...
dotnet clean AccountingSystem11.csproj >nul 2>&1
if exist "bin" rmdir /s /q "bin" >nul 2>&1
if exist "obj" rmdir /s /q "obj" >nul 2>&1
echo ✅ تم التنظيف الشامل

echo.
echo [2] استعادة الحزم...
dotnet restore AccountingSystem11.csproj --verbosity minimal
if %errorlevel% neq 0 (
    echo ❌ فشل في استعادة الحزم
    pause
    exit /b 1
)
echo ✅ تم استعادة الحزم

echo.
echo [3] بناء محسن...
dotnet build AccountingSystem11.csproj ^
    --configuration Release ^
    --no-restore ^
    --verbosity minimal
if %errorlevel% neq 0 (
    echo ❌ فشل البناء
    echo.
    echo 🔧 جرب هذه الحلول:
    echo 1. شغل الملف كـ Administrator
    echo 2. أغلق مضاد الفيروسات مؤقتاً
    echo 3. تأكد من تثبيت .NET 6.0 SDK
    pause
    exit /b 1
)
echo ✅ تم البناء بنجاح

echo.
echo [4] نشر محسن مع ضغط...
echo ⏳ هذا قد يستغرق دقائق قليلة...

dotnet publish AccountingSystem11.csproj ^
    --configuration Release ^
    --runtime win-x64 ^
    --self-contained true ^
    --output "Release\AccountingSystem11_Optimized" ^
    --verbosity minimal ^
    -p:PublishSingleFile=true ^
    -p:PublishTrimmed=true ^
    -p:TrimMode=link ^
    -p:DebuggerSupport=false ^
    -p:EnableCompressionInSingleFile=true

if %errorlevel% equ 0 (
    echo.
    echo ========================================================
    echo 🎉 تم إنشاء ملف .exe محسن بنجاح!
    echo ========================================================
    echo.
    
    REM حساب حجم الملف
    for %%A in ("Release\AccountingSystem11_Optimized\AccountingSystem11.exe") do (
        set "filesize=%%~zA"
    )
    
    echo 📁 مكان الملف: Release\AccountingSystem11_Optimized\
    echo 📄 اسم الملف: AccountingSystem11.exe
    echo 💾 حجم الملف: %filesize% بايت
    echo.
    echo ✅ المميزات المحسنة:
    echo    • ملف واحد فقط (.exe)
    echo    • حجم أصغر (مضغوط)
    echo    • بدء تشغيل أسرع
    echo    • لا يحتاج .NET منفصل
    echo    • محسن للأداء
    echo.
    
    REM إنشاء مجلد البيانات
    if not exist "Release\AccountingSystem11_Optimized\Data" mkdir "Release\AccountingSystem11_Optimized\Data"
    
    REM إنشاء ملفات مساعدة
    echo 📋 إنشاء ملفات مساعدة...
    
    REM ملف تشغيل
    echo @echo off > "Release\AccountingSystem11_Optimized\تشغيل.bat"
    echo title نظام المحاسبة 11 >> "Release\AccountingSystem11_Optimized\تشغيل.bat"
    echo color 0A >> "Release\AccountingSystem11_Optimized\تشغيل.bat"
    echo echo ======================================== >> "Release\AccountingSystem11_Optimized\تشغيل.bat"
    echo echo      نظام المحاسبة 11 >> "Release\AccountingSystem11_Optimized\تشغيل.bat"
    echo echo ======================================== >> "Release\AccountingSystem11_Optimized\تشغيل.bat"
    echo echo. >> "Release\AccountingSystem11_Optimized\تشغيل.bat"
    echo echo 🚀 جاري التشغيل... >> "Release\AccountingSystem11_Optimized\تشغيل.bat"
    echo echo. >> "Release\AccountingSystem11_Optimized\تشغيل.bat"
    echo echo 🔐 بيانات تسجيل الدخول: >> "Release\AccountingSystem11_Optimized\تشغيل.bat"
    echo echo 👤 اسم المستخدم: admin >> "Release\AccountingSystem11_Optimized\تشغيل.bat"
    echo echo 🔑 كلمة المرور: admin123 >> "Release\AccountingSystem11_Optimized\تشغيل.bat"
    echo echo. >> "Release\AccountingSystem11_Optimized\تشغيل.bat"
    echo AccountingSystem11.exe >> "Release\AccountingSystem11_Optimized\تشغيل.bat"
    echo if %%errorlevel%% neq 0 ( >> "Release\AccountingSystem11_Optimized\تشغيل.bat"
    echo     echo ❌ حدث خطأ في التشغيل >> "Release\AccountingSystem11_Optimized\تشغيل.bat"
    echo     echo تأكد من تثبيت Access Database Engine >> "Release\AccountingSystem11_Optimized\تشغيل.bat"
    echo ^) >> "Release\AccountingSystem11_Optimized\تشغيل.bat"
    echo pause >> "Release\AccountingSystem11_Optimized\تشغيل.bat"
    
    REM ملف README
    echo # نظام المحاسبة 11 > "Release\AccountingSystem11_Optimized\README.txt"
    echo. >> "Release\AccountingSystem11_Optimized\README.txt"
    echo ## طريقة التشغيل: >> "Release\AccountingSystem11_Optimized\README.txt"
    echo 1. اضغط دبل كليك على تشغيل.bat >> "Release\AccountingSystem11_Optimized\README.txt"
    echo 2. أو اضغط دبل كليك على AccountingSystem11.exe >> "Release\AccountingSystem11_Optimized\README.txt"
    echo. >> "Release\AccountingSystem11_Optimized\README.txt"
    echo ## بيانات تسجيل الدخول: >> "Release\AccountingSystem11_Optimized\README.txt"
    echo اسم المستخدم: admin >> "Release\AccountingSystem11_Optimized\README.txt"
    echo كلمة المرور: admin123 >> "Release\AccountingSystem11_Optimized\README.txt"
    echo. >> "Release\AccountingSystem11_Optimized\README.txt"
    echo ## المتطلبات: >> "Release\AccountingSystem11_Optimized\README.txt"
    echo - Windows 7 أو أحدث >> "Release\AccountingSystem11_Optimized\README.txt"
    echo - Access Database Engine 2016 >> "Release\AccountingSystem11_Optimized\README.txt"
    
    echo ✅ تم إنشاء الملفات المساعدة
    echo.
    
    REM إنشاء ملف ZIP للتوزيع
    echo 📦 إنشاء ملف مضغوط للتوزيع...
    powershell -Command "Compress-Archive -Path 'Release\AccountingSystem11_Optimized\*' -DestinationPath 'Release\نظام_المحاسبة_11.zip' -Force" >nul 2>&1
    if exist "Release\نظام_المحاسبة_11.zip" (
        echo ✅ تم إنشاء ملف مضغوط: Release\نظام_المحاسبة_11.zip
    )
    
    echo.
    echo 🎯 ملخص الملفات المُنشأة:
    echo 📁 Release\AccountingSystem11_Optimized\ - المجلد الكامل
    echo 📄 AccountingSystem11.exe - الملف التنفيذي
    echo 📄 تشغيل.bat - ملف تشغيل سهل
    echo 📄 README.txt - تعليمات الاستخدام
    echo 📦 نظام_المحاسبة_11.zip - ملف مضغوط للتوزيع
    echo.
    
    set /p choice="هل تريد فتح مجلد الملفات؟ (y/n): "
    if /i "%choice%"=="y" (
        explorer "Release\AccountingSystem11_Optimized"
    )
    
    echo.
    set /p choice2="هل تريد اختبار التشغيل؟ (y/n): "
    if /i "%choice2%"=="y" (
        cd "Release\AccountingSystem11_Optimized"
        echo 🚀 جاري اختبار التشغيل...
        AccountingSystem11.exe
    )
    
) else (
    echo.
    echo ❌ فشل إنشاء ملف .exe المحسن
    echo.
    echo 🔧 جرب الحلول التالية:
    echo 1. استخدم "إنشاء_exe.bat" بدلاً من هذا الملف
    echo 2. تأكد من تثبيت .NET 6.0 SDK (وليس فقط Runtime)
    echo 3. شغل Command Prompt كـ Administrator
    echo 4. تأكد من وجود مساحة كافية (1 GB)
    echo.
    echo 💡 أو جرب الأمر اليدوي:
    echo dotnet publish AccountingSystem11.csproj -c Release -r win-x64 --self-contained
)

echo.
pause
