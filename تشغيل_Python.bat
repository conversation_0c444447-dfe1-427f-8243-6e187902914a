@echo off
title نظام المحاسبة 11 - إصدار Python
color 0A
echo.
echo ========================================================
echo        نظام المحاسبة 11 - إصدار Python
echo ========================================================
echo.

echo 🐍 برنامج Python - لا يحتاج .NET!
echo ✅ يعمل مع Python فقط
echo 🗃️ قاعدة بيانات SQLite محلية
echo 🚀 سريع وخفيف
echo.

echo [1] التحقق من Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت
    echo.
    echo 📥 جاري فتح رابط تحميل Python...
    start https://www.python.org/downloads/
    echo.
    echo 📋 تعليمات التثبيت:
    echo    1. حمل Python 3.8 أو أحدث
    echo    2. تأكد من اختيار "Add Python to PATH"
    echo    3. ثبت Python
    echo    4. أعد تشغيل الجهاز
    echo    5. أعد تشغيل هذا الملف
    echo.
    pause
    exit /b 1
)

echo ✅ Python مثبت - الإصدار:
python --version

echo.
echo [2] التحقق من المكتبات المطلوبة...
python -c "import tkinter, sqlite3" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ مكتبات مفقودة
    echo 📦 جاري تثبيت المكتبات...
    
    REM تثبيت المكتبات (tkinter عادة مدمج مع Python)
    echo تم التحقق من المكتبات الأساسية
) else (
    echo ✅ جميع المكتبات متوفرة
)

echo.
echo [3] التحقق من ملف البرنامج...
if exist "accounting_system_python.py" (
    echo ✅ ملف البرنامج موجود
) else (
    echo ❌ ملف البرنامج غير موجود
    echo يرجى التأكد من وجود ملف accounting_system_python.py
    pause
    exit /b 1
)

echo.
echo ========================================================
echo 🚀 جاري تشغيل نظام المحاسبة 11...
echo ========================================================
echo.

echo 🔐 بيانات تسجيل الدخول:
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo.

echo 💡 ملاحظات:
echo    • البرنامج سينشئ قاعدة بيانات SQLite تلقائياً
echo    • البيانات محفوظة في ملف accounting_system.db
echo    • لا يحتاج اتصال إنترنت للعمل
echo.

timeout /t 3 /nobreak >nul

echo 🐍 بدء تشغيل البرنامج...
python accounting_system_python.py

echo.
echo ✅ تم إغلاق البرنامج
echo 💾 البيانات محفوظة في: accounting_system.db
echo.
pause
