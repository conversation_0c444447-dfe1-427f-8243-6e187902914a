using Microsoft.EntityFrameworkCore;
using AccountingSystem11.Models;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace AccountingSystem11.Data
{
    /// <summary>
    /// Database context for the accounting system
    /// </summary>
    public class AccountingDbContext : DbContext
    {
        public AccountingDbContext(DbContextOptions<AccountingDbContext> options) : base(options)
        {
        }

        // Core entities
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<ProductCategory> ProductCategories { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<Invoice> Invoices { get; set; }
        public DbSet<InvoiceItem> InvoiceItems { get; set; }
        public DbSet<InvoicePayment> InvoicePayments { get; set; }

        // Transaction entities
        public DbSet<CustomerTransaction> CustomerTransactions { get; set; }
        public DbSet<SupplierTransaction> SupplierTransactions { get; set; }
        public DbSet<InventoryTransaction> InventoryTransactions { get; set; }

        // User management
        public DbSet<User> Users { get; set; }
        public DbSet<UserPermission> UserPermissions { get; set; }
        public DbSet<UserSession> UserSessions { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure decimal precision for monetary values
            ConfigureDecimalPrecision(modelBuilder);

            // Configure relationships
            ConfigureRelationships(modelBuilder);

            // Configure indexes
            ConfigureIndexes(modelBuilder);

            // Configure soft delete filter
            ConfigureSoftDelete(modelBuilder);

            // Seed initial data
            SeedInitialData(modelBuilder);
        }

        private void ConfigureDecimalPrecision(ModelBuilder modelBuilder)
        {
            // Customer
            modelBuilder.Entity<Customer>()
                .Property(c => c.CreditLimit)
                .HasPrecision(18, 2);
            modelBuilder.Entity<Customer>()
                .Property(c => c.Balance)
                .HasPrecision(18, 2);
            modelBuilder.Entity<Customer>()
                .Property(c => c.DefaultDiscount)
                .HasPrecision(5, 2);

            // Product
            modelBuilder.Entity<Product>()
                .Property(p => p.PurchasePrice)
                .HasPrecision(18, 2);
            modelBuilder.Entity<Product>()
                .Property(p => p.SellingPrice)
                .HasPrecision(18, 2);
            modelBuilder.Entity<Product>()
                .Property(p => p.MinSellingPrice)
                .HasPrecision(18, 2);
            modelBuilder.Entity<Product>()
                .Property(p => p.WholesalePrice)
                .HasPrecision(18, 2);
            modelBuilder.Entity<Product>()
                .Property(p => p.StockQuantity)
                .HasPrecision(18, 3);
            modelBuilder.Entity<Product>()
                .Property(p => p.MinStockLevel)
                .HasPrecision(18, 3);
            modelBuilder.Entity<Product>()
                .Property(p => p.MaxStockLevel)
                .HasPrecision(18, 3);
            modelBuilder.Entity<Product>()
                .Property(p => p.ReorderPoint)
                .HasPrecision(18, 3);
            modelBuilder.Entity<Product>()
                .Property(p => p.TaxRate)
                .HasPrecision(5, 2);

            // Invoice
            modelBuilder.Entity<Invoice>()
                .Property(i => i.Subtotal)
                .HasPrecision(18, 2);
            modelBuilder.Entity<Invoice>()
                .Property(i => i.DiscountAmount)
                .HasPrecision(18, 2);
            modelBuilder.Entity<Invoice>()
                .Property(i => i.DiscountPercentage)
                .HasPrecision(5, 2);
            modelBuilder.Entity<Invoice>()
                .Property(i => i.AmountAfterDiscount)
                .HasPrecision(18, 2);
            modelBuilder.Entity<Invoice>()
                .Property(i => i.VatAmount)
                .HasPrecision(18, 2);
            modelBuilder.Entity<Invoice>()
                .Property(i => i.VatRate)
                .HasPrecision(5, 2);
            modelBuilder.Entity<Invoice>()
                .Property(i => i.AdditionalTaxAmount)
                .HasPrecision(18, 2);
            modelBuilder.Entity<Invoice>()
                .Property(i => i.TotalTaxAmount)
                .HasPrecision(18, 2);
            modelBuilder.Entity<Invoice>()
                .Property(i => i.TotalAmount)
                .HasPrecision(18, 2);
            modelBuilder.Entity<Invoice>()
                .Property(i => i.PaidAmount)
                .HasPrecision(18, 2);
            modelBuilder.Entity<Invoice>()
                .Property(i => i.RemainingAmount)
                .HasPrecision(18, 2);
            modelBuilder.Entity<Invoice>()
                .Property(i => i.ExchangeRate)
                .HasPrecision(10, 4);

            // InvoiceItem
            modelBuilder.Entity<InvoiceItem>()
                .Property(ii => ii.Quantity)
                .HasPrecision(18, 3);
            modelBuilder.Entity<InvoiceItem>()
                .Property(ii => ii.UnitPrice)
                .HasPrecision(18, 2);
            modelBuilder.Entity<InvoiceItem>()
                .Property(ii => ii.LineTotal)
                .HasPrecision(18, 2);
            modelBuilder.Entity<InvoiceItem>()
                .Property(ii => ii.DiscountAmount)
                .HasPrecision(18, 2);
            modelBuilder.Entity<InvoiceItem>()
                .Property(ii => ii.DiscountPercentage)
                .HasPrecision(5, 2);
            modelBuilder.Entity<InvoiceItem>()
                .Property(ii => ii.AmountAfterDiscount)
                .HasPrecision(18, 2);
            modelBuilder.Entity<InvoiceItem>()
                .Property(ii => ii.TaxRate)
                .HasPrecision(5, 2);
            modelBuilder.Entity<InvoiceItem>()
                .Property(ii => ii.TaxAmount)
                .HasPrecision(18, 2);
            modelBuilder.Entity<InvoiceItem>()
                .Property(ii => ii.TotalAmount)
                .HasPrecision(18, 2);
        }

        private void ConfigureRelationships(ModelBuilder modelBuilder)
        {
            // Customer relationships
            modelBuilder.Entity<Invoice>()
                .HasOne(i => i.Customer)
                .WithMany(c => c.Invoices)
                .HasForeignKey(i => i.CustomerId)
                .OnDelete(DeleteBehavior.Restrict);

            // Product relationships
            modelBuilder.Entity<Product>()
                .HasOne(p => p.Category)
                .WithMany(c => c.Products)
                .HasForeignKey(p => p.CategoryId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Product>()
                .HasOne(p => p.Supplier)
                .WithMany(s => s.Products)
                .HasForeignKey(p => p.SupplierId)
                .OnDelete(DeleteBehavior.SetNull);

            // Invoice relationships
            modelBuilder.Entity<InvoiceItem>()
                .HasOne(ii => ii.Invoice)
                .WithMany(i => i.Items)
                .HasForeignKey(ii => ii.InvoiceId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<InvoiceItem>()
                .HasOne(ii => ii.Product)
                .WithMany(p => p.InvoiceItems)
                .HasForeignKey(ii => ii.ProductId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<InvoicePayment>()
                .HasOne(ip => ip.Invoice)
                .WithMany(i => i.Payments)
                .HasForeignKey(ip => ip.InvoiceId)
                .OnDelete(DeleteBehavior.Cascade);

            // User relationships
            modelBuilder.Entity<UserPermission>()
                .HasOne(up => up.User)
                .WithMany(u => u.Permissions)
                .HasForeignKey(up => up.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<UserSession>()
                .HasOne(us => us.User)
                .WithMany(u => u.Sessions)
                .HasForeignKey(us => us.UserId)
                .OnDelete(DeleteBehavior.Cascade);
        }

        private void ConfigureIndexes(ModelBuilder modelBuilder)
        {
            // Customer indexes
            modelBuilder.Entity<Customer>()
                .HasIndex(c => c.TaxNumber)
                .IsUnique()
                .HasFilter("[TaxNumber] IS NOT NULL");

            modelBuilder.Entity<Customer>()
                .HasIndex(c => c.Email)
                .IsUnique()
                .HasFilter("[Email] IS NOT NULL");

            // Product indexes
            modelBuilder.Entity<Product>()
                .HasIndex(p => p.Code)
                .IsUnique();

            modelBuilder.Entity<Product>()
                .HasIndex(p => p.Barcode)
                .IsUnique()
                .HasFilter("[Barcode] IS NOT NULL");

            // Invoice indexes
            modelBuilder.Entity<Invoice>()
                .HasIndex(i => i.InvoiceNumber)
                .IsUnique();

            modelBuilder.Entity<Invoice>()
                .HasIndex(i => i.InvoiceDate);

            modelBuilder.Entity<Invoice>()
                .HasIndex(i => i.EInvoiceUuid)
                .IsUnique()
                .HasFilter("[EInvoiceUuid] IS NOT NULL");

            // User indexes
            modelBuilder.Entity<User>()
                .HasIndex(u => u.Username)
                .IsUnique();

            modelBuilder.Entity<User>()
                .HasIndex(u => u.Email)
                .IsUnique();
        }

        private void ConfigureSoftDelete(ModelBuilder modelBuilder)
        {
            // Configure global query filter for soft delete
            modelBuilder.Entity<Customer>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Product>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<ProductCategory>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Supplier>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Invoice>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<InvoiceItem>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<User>().HasQueryFilter(e => !e.IsDeleted);
        }

        private void SeedInitialData(ModelBuilder modelBuilder)
        {
            // Seed default admin user
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    Id = 1,
                    Username = "admin",
                    FullName = "مدير النظام",
                    Email = "<EMAIL>",
                    PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                    Role = UserRole.Admin,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = "System"
                }
            );

            // Seed default product categories
            modelBuilder.Entity<ProductCategory>().HasData(
                new ProductCategory { Id = 1, Name = "عام", Code = "GEN", CreatedAt = DateTime.Now, CreatedBy = "System" },
                new ProductCategory { Id = 2, Name = "ملابس", Code = "CLO", CreatedAt = DateTime.Now, CreatedBy = "System" },
                new ProductCategory { Id = 3, Name = "أغذية", Code = "FOO", CreatedAt = DateTime.Now, CreatedBy = "System" },
                new ProductCategory { Id = 4, Name = "إلكترونيات", Code = "ELE", CreatedAt = DateTime.Now, CreatedBy = "System" },
                new ProductCategory { Id = 5, Name = "أدوية", Code = "MED", CreatedAt = DateTime.Now, CreatedBy = "System" }
            );
        }

        public override int SaveChanges()
        {
            UpdateTimestamps();
            return base.SaveChanges();
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateTimestamps();
            return base.SaveChangesAsync(cancellationToken);
        }

        private void UpdateTimestamps()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.Entity is BaseEntity && (e.State == EntityState.Added || e.State == EntityState.Modified));

            foreach (var entry in entries)
            {
                var entity = (BaseEntity)entry.Entity;

                if (entry.State == EntityState.Added)
                {
                    entity.CreatedAt = DateTime.Now;
                }
                else if (entry.State == EntityState.Modified)
                {
                    entity.UpdatedAt = DateTime.Now;
                }
            }
        }
    }
}
