using AccountingSystem11.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AccountingSystem11.Services
{
    /// <summary>
    /// Interface for customer service operations
    /// </summary>
    public interface ICustomerService
    {
        Task<IEnumerable<Customer>> GetAllCustomersAsync();
        Task<Customer> GetCustomerByIdAsync(int id);
        Task<Customer> GetCustomerByTaxNumberAsync(string taxNumber);
        Task<Customer> CreateCustomerAsync(Customer customer);
        Task<Customer> UpdateCustomerAsync(Customer customer);
        Task<bool> DeleteCustomerAsync(int id);
        Task<bool> CustomerExistsAsync(int id);
        Task<bool> TaxNumberExistsAsync(string taxNumber, int? excludeId = null);
        Task<bool> EmailExistsAsync(string email, int? excludeId = null);
        Task<IEnumerable<Customer>> SearchCustomersAsync(string searchTerm);
        Task<decimal> GetCustomerBalanceAsync(int customerId);
        Task<bool> UpdateCustomerBalanceAsync(int customerId, decimal amount);
        Task<IEnumerable<Customer>> GetCustomersWithOverdueInvoicesAsync();
        Task<IEnumerable<Customer>> GetTopCustomersByRevenueAsync(int count = 10);
    }
}
