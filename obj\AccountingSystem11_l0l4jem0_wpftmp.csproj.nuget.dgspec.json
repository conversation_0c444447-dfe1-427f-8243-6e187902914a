{"format": 1, "restore": {"D:\\اتن\\AccountingSystem11.csproj": {}}, "projects": {"D:\\اتن\\AccountingSystem11.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\اتن\\AccountingSystem11.csproj", "projectName": "AccountingSystem11", "projectPath": "D:\\اتن\\AccountingSystem11.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\اتن\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "EPPlus": {"target": "Package", "version": "[7.0.4, )"}, "LiveCharts.Wpf": {"target": "Package", "version": "[0.9.7, )"}, "MaterialDesignColors": {"target": "Package", "version": "[2.1.4, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.14, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[7.0.14, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.14, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[7.0.14, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "QRCoder": {"target": "Package", "version": "[1.4.3, )"}, "System.Data.OleDb": {"target": "Package", "version": "[7.0.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}}}}