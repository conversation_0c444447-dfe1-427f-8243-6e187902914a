@echo off
title إنشاء EXE بسيط
color 0E

echo 🔨 إنشاء ملف .exe بسيط...
echo.

REM إنشاء مجلد الإخراج
mkdir "EXE" 2>nul

echo ⏳ جاري البناء...
dotnet publish AccountingSystem11.csproj -c Release -o "EXE" --self-contained false

if %errorlevel% equ 0 (
    echo.
    echo ✅ تم إنشاء الملف!
    echo 📁 المكان: EXE\AccountingSystem11.exe
    echo.
    echo ⚠️ ملاحظة: يحتاج .NET 6.0 Runtime على الجهاز المستهدف
    echo.
    
    REM إنشاء ملف تشغيل
    echo @echo off > "EXE\تشغيل.bat"
    echo AccountingSystem11.exe >> "EXE\تشغيل.bat"
    echo pause >> "EXE\تشغيل.bat"
    
    explorer "EXE"
) else (
    echo ❌ فشل الإنشاء
)

pause
