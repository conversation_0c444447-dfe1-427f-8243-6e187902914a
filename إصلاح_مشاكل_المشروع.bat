@echo off
title إصلاح مشاكل المشروع - نظام المحاسبة 11
color 0C
echo.
echo ========================================================
echo      إصلاح مشاكل المشروع - نظام المحاسبة 11
echo ========================================================
echo.

echo 🔧 جاري إصلاح مشاكل تحميل المشروع...
echo ✅ تم إصلاح تضارب حزم Entity Framework
echo ✅ تم تبسيط إصدارات .NET المدعومة
echo ✅ تم توحيد إصدارات الحزم
echo.

echo [1] تنظيف المشروع...
if exist "bin" (
    rmdir /s /q "bin" 2>nul
    echo ✅ تم حذف مجلد bin
)

if exist "obj" (
    rmdir /s /q "obj" 2>nul
    echo ✅ تم حذف مجلد obj
)

echo.
echo [2] استعادة الحزم...
dotnet restore AccountingSystem11.csproj
if %errorlevel% equ 0 (
    echo ✅ تم استعادة الحزم بنجاح
) else (
    echo ❌ فشل في استعادة الحزم
    goto :error
)

echo.
echo [3] بناء المشروع...
dotnet build AccountingSystem11.csproj -c Release
if %errorlevel% equ 0 (
    echo ✅ تم بناء المشروع بنجاح
) else (
    echo ❌ فشل في بناء المشروع
    goto :error
)

echo.
echo ========================================================
echo 🎉 تم إصلاح جميع مشاكل المشروع بنجاح!
echo ========================================================
echo.

echo 📋 الإصدارات المدعومة الآن:
echo    ✅ .NET 6.0 (LTS)
echo    ✅ .NET 8.0 (الأحدث)
echo.

echo 📦 الحزم المثبتة:
echo    ✅ Entity Framework Core 7.0.14
echo    ✅ Material Design 4.9.0
echo    ✅ EPPlus 7.0.4
echo    ✅ LiveCharts 0.9.7
echo    ✅ BCrypt.Net 4.0.3
echo.

echo 🚀 يمكنك الآن فتح المشروع في Visual Studio بدون مشاكل!
echo.

set /p choice="هل تريد تشغيل البرنامج الآن؟ (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 🚀 جاري تشغيل البرنامج...
    dotnet run --project AccountingSystem11.csproj
)

goto :end

:error
echo.
echo ❌ حدثت مشاكل أثناء الإصلاح
echo.
echo 🔧 الحلول المقترحة:
echo 1. تأكد من تثبيت .NET 6.0 أو أحدث
echo 2. شغل Command Prompt كـ Administrator
echo 3. تأكد من الاتصال بالإنترنت لتحميل الحزم
echo 4. أغلق Visual Studio وأعد فتحه
echo.
echo 📥 روابط التحميل:
echo .NET 6.0: https://dotnet.microsoft.com/download/dotnet/6.0
echo .NET 8.0: https://dotnet.microsoft.com/download/dotnet/8.0
echo Visual Studio: https://visualstudio.microsoft.com/downloads/
echo.

:end
pause
