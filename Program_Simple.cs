using System;
using System.Windows;

namespace AccountingSystem11
{
    /// <summary>
    /// برنامج بسيط للاختبار
    /// </summary>
    public class Program
    {
        [STAThread]
        public static void Main()
        {
            try
            {
                Console.WriteLine("🚀 بدء تشغيل نظام المحاسبة 11...");
                
                var app = new Application();
                
                // إنشاء نافذة بسيطة
                var window = new Window
                {
                    Title = "نظام المحاسبة 11 - إصدار تجريبي",
                    Width = 800,
                    Height = 600,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen
                };
                
                // إضافة محتوى بسيط
                var textBlock = new System.Windows.Controls.TextBlock
                {
                    Text = @"🎉 مرحباً بك في نظام المحاسبة 11

✅ تم تشغيل البرنامج بنجاح!

🔐 بيانات تسجيل الدخول:
👤 اسم المستخدم: admin
🔑 كلمة المرور: admin123

📋 المميزات:
• قاعدة بيانات محلية
• واجهة عربية
• دعم الضرائب المصرية
• فواتير إلكترونية

🔧 هذا إصدار تجريبي للاختبار",
                    FontSize = 16,
                    Margin = new Thickness(20),
                    TextWrapping = TextWrapping.Wrap,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                };
                
                window.Content = textBlock;
                
                Console.WriteLine("✅ تم إنشاء النافذة");
                
                app.Run(window);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ: {ex.Message}");
                Console.WriteLine("اضغط أي مفتاح للخروج...");
                Console.ReadKey();
            }
        }
    }
}
