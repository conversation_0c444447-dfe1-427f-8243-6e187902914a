using AccountingSystem11.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AccountingSystem11.Services
{
    /// <summary>
    /// Interface for inventory management operations
    /// </summary>
    public interface IInventoryService
    {
        Task<IEnumerable<InventoryTransaction>> GetAllTransactionsAsync();
        Task<InventoryTransaction> GetTransactionByIdAsync(int id);
        Task<InventoryTransaction> CreateTransactionAsync(InventoryTransaction transaction);
        Task<InventoryTransaction> UpdateTransactionAsync(InventoryTransaction transaction);
        Task<bool> DeleteTransactionAsync(int id);
        Task<IEnumerable<InventoryTransaction>> GetTransactionsByProductAsync(int productId);
        Task<IEnumerable<InventoryTransaction>> GetTransactionsByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<InventoryTransaction>> GetTransactionsByTypeAsync(InventoryTransactionType type);
        Task<bool> AdjustStockAsync(int productId, decimal quantity, string reason, string location = null);
        Task<bool> TransferStockAsync(int productId, decimal quantity, string fromLocation, string toLocation, string reason);
        Task<decimal> GetCurrentStockAsync(int productId, string location = null);
        Task<decimal> GetStockValueAsync(int? productId = null, string location = null);
        Task<IEnumerable<Product>> GetLowStockProductsAsync();
        Task<IEnumerable<Product>> GetOutOfStockProductsAsync();
        Task<IEnumerable<Product>> GetOverStockProductsAsync();
        Task<InventoryValuationReport> GetInventoryValuationAsync(DateTime? asOfDate = null);
        Task<StockMovementReport> GetStockMovementReportAsync(DateTime startDate, DateTime endDate, int? productId = null);
        Task<string> GenerateNextTransactionNumberAsync(InventoryTransactionType type);
        Task<bool> ProcessSaleAsync(int productId, decimal quantity, decimal unitPrice, string invoiceNumber);
        Task<bool> ProcessPurchaseAsync(int productId, decimal quantity, decimal unitCost, string invoiceNumber);
        Task<bool> ProcessReturnAsync(int productId, decimal quantity, bool isSaleReturn, string invoiceNumber);
    }

    /// <summary>
    /// Inventory valuation report
    /// </summary>
    public class InventoryValuationReport
    {
        public DateTime ReportDate { get; set; }
        public decimal TotalValue { get; set; }
        public int TotalProducts { get; set; }
        public List<InventoryValuationItem> Items { get; set; } = new List<InventoryValuationItem>();
        public List<InventoryValuationSummary> CategorySummary { get; set; } = new List<InventoryValuationSummary>();
    }

    /// <summary>
    /// Inventory valuation item
    /// </summary>
    public class InventoryValuationItem
    {
        public int ProductId { get; set; }
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public string Category { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitCost { get; set; }
        public decimal TotalValue { get; set; }
        public string Location { get; set; }
        public DateTime LastTransactionDate { get; set; }
    }

    /// <summary>
    /// Inventory valuation summary by category
    /// </summary>
    public class InventoryValuationSummary
    {
        public string Category { get; set; }
        public int ProductCount { get; set; }
        public decimal TotalQuantity { get; set; }
        public decimal TotalValue { get; set; }
        public decimal Percentage { get; set; }
    }

    /// <summary>
    /// Stock movement report
    /// </summary>
    public class StockMovementReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int? ProductId { get; set; }
        public string ProductName { get; set; }
        public decimal OpeningStock { get; set; }
        public decimal TotalIn { get; set; }
        public decimal TotalOut { get; set; }
        public decimal ClosingStock { get; set; }
        public List<StockMovementItem> Movements { get; set; } = new List<StockMovementItem>();
    }

    /// <summary>
    /// Stock movement item
    /// </summary>
    public class StockMovementItem
    {
        public DateTime Date { get; set; }
        public string TransactionNumber { get; set; }
        public InventoryTransactionType Type { get; set; }
        public string Description { get; set; }
        public decimal QuantityIn { get; set; }
        public decimal QuantityOut { get; set; }
        public decimal Balance { get; set; }
        public string Reference { get; set; }
    }
}
