@echo off
title تحويل Python إلى EXE - cx_Freeze
color 0F
echo.
echo ========================================================
echo      تحويل Python إلى EXE - cx_Freeze
echo ========================================================
echo.

echo 🔧 cx_Freeze - بديل موثوق لـ PyInstaller
echo ✅ ملفات أصغر حجماً
echo 🚀 تحكم أكبر في العملية
echo.

echo [1] التحقق من Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت
    pause
    exit /b 1
)
echo ✅ Python مثبت

echo.
echo [2] التحقق من cx_Freeze...
python -c "import cx_Freeze" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ cx_Freeze غير مثبت
    echo 📦 جاري تثبيت cx_Freeze...
    pip install cx_Freeze
    if %errorlevel% neq 0 (
        echo ❌ فشل التثبيت
        pause
        exit /b 1
    )
)
echo ✅ cx_Freeze متوفر

echo.
echo [3] التحقق من الملفات...
if not exist "accounting_system_python.py" (
    echo ❌ ملف البرنامج غير موجود
    pause
    exit /b 1
)
if not exist "setup_cx_freeze.py" (
    echo ❌ ملف الإعداد غير موجود
    pause
    exit /b 1
)
echo ✅ جميع الملفات موجودة

echo.
echo [4] تحويل Python إلى EXE...
echo ⏳ جاري التحويل...

python setup_cx_freeze.py build

if %errorlevel% equ 0 (
    echo.
    echo ✅ تم التحويل بنجاح!
    echo 📁 الملفات في: EXE_cx_Freeze\
    
    REM إنشاء ملف تشغيل
    echo @echo off > "EXE_cx_Freeze\تشغيل.bat"
    echo "نظام_المحاسبة_11_cx_Freeze.exe" >> "EXE_cx_Freeze\تشغيل.bat"
    echo pause >> "EXE_cx_Freeze\تشغيل.bat"
    
    echo ✅ تم إنشاء ملف التشغيل
    
    set /p choice="هل تريد فتح المجلد؟ (y/n): "
    if /i "%choice%"=="y" (
        explorer "EXE_cx_Freeze"
    )
) else (
    echo ❌ فشل التحويل
)

pause
