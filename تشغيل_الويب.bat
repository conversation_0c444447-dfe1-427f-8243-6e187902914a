@echo off
title نظام المحاسبة 11 - إصدار الويب
color 0E
echo.
echo ========================================================
echo        نظام المحاسبة 11 - إصدار الويب
echo ========================================================
echo.

echo 🌐 برنامج ويب - لا يحتاج .NET!
echo ✅ يعمل في أي متصفح
echo 💾 حفظ محلي في المتصفح
echo 🚀 لا يحتاج تثبيت أي شيء
echo.

echo [1] التحقق من ملف الويب...
if exist "accounting_system_web.html" (
    echo ✅ ملف الويب موجود
) else (
    echo ❌ ملف الويب غير موجود
    echo يرجى التأكد من وجود ملف accounting_system_web.html
    pause
    exit /b 1
)

echo.
echo [2] البحث عن متصفح...
set "BROWSER_FOUND=0"

REM البحث عن Chrome
if exist "C:\Program Files\Google\Chrome\Application\chrome.exe" (
    set "BROWSER=C:\Program Files\Google\Chrome\Application\chrome.exe"
    set "BROWSER_NAME=Google Chrome"
    set "BROWSER_FOUND=1"
) else if exist "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" (
    set "BROWSER=C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
    set "BROWSER_NAME=Google Chrome"
    set "BROWSER_FOUND=1"
)

REM البحث عن Firefox إذا لم يوجد Chrome
if "%BROWSER_FOUND%"=="0" (
    if exist "C:\Program Files\Mozilla Firefox\firefox.exe" (
        set "BROWSER=C:\Program Files\Mozilla Firefox\firefox.exe"
        set "BROWSER_NAME=Mozilla Firefox"
        set "BROWSER_FOUND=1"
    ) else if exist "C:\Program Files (x86)\Mozilla Firefox\firefox.exe" (
        set "BROWSER=C:\Program Files (x86)\Mozilla Firefox\firefox.exe"
        set "BROWSER_NAME=Mozilla Firefox"
        set "BROWSER_FOUND=1"
    )
)

REM البحث عن Edge إذا لم يوجد Chrome أو Firefox
if "%BROWSER_FOUND%"=="0" (
    if exist "C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe" (
        set "BROWSER=C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe"
        set "BROWSER_NAME=Microsoft Edge"
        set "BROWSER_FOUND=1"
    )
)

if "%BROWSER_FOUND%"=="1" (
    echo ✅ تم العثور على متصفح: %BROWSER_NAME%
) else (
    echo ⚠️  لم يتم العثور على متصفح محدد
    echo سيتم استخدام المتصفح الافتراضي
    set "BROWSER=start"
    set "BROWSER_NAME=المتصفح الافتراضي"
)

echo.
echo ========================================================
echo 🌐 جاري فتح نظام المحاسبة 11 في المتصفح...
echo ========================================================
echo.

echo 🔐 بيانات تسجيل الدخول:
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo.

echo 💡 مميزات الإصدار الويب:
echo    • لا يحتاج تثبيت أي برامج
echo    • يعمل في أي متصفح حديث
echo    • واجهة متجاوبة (تعمل على الموبايل)
echo    • حفظ البيانات محلياً في المتصفح
echo    • سريع وآمن
echo.

echo 📱 يمكنك أيضاً:
echo    • فتح الملف من الموبايل
echo    • مشاركة الرابط مع الآخرين
echo    • العمل بدون اتصال إنترنت
echo.

timeout /t 3 /nobreak >nul

echo 🌐 فتح المتصفح: %BROWSER_NAME%
echo.

REM الحصول على المسار الكامل للملف
set "FULL_PATH=%CD%\accounting_system_web.html"

if "%BROWSER%"=="start" (
    start "" "%FULL_PATH%"
) else (
    "%BROWSER%" "%FULL_PATH%"
)

echo ✅ تم فتح البرنامج في المتصفح
echo.
echo 💡 إذا لم يفتح المتصفح تلقائياً:
echo    1. افتح أي متصفح
echo    2. اسحب ملف accounting_system_web.html إلى المتصفح
echo    3. أو اضغط Ctrl+O وحدد الملف
echo.
echo 🔄 لإعادة فتح البرنامج:
echo    اضغط دبل كليك على ملف accounting_system_web.html
echo.

pause
