# 🔧 دليل حل مشاكل نظام المحاسبة 11

## 🚨 المشاكل الشائعة وحلولها

### 1️⃣ المشكلة: "لم يعمل البرنامج"

#### الأسباب المحتملة:
- ❌ .NET غير مثبت أو إصدار خاطئ
- ❌ Access Database Engine غير مثبت
- ❌ أخطاء في البناء (Build Errors)
- ❌ مشاكل في الصلاحيات
- ❌ برامج مضاد الفيروسات تمنع التشغيل

#### الحلول خطوة بخطوة:

##### 🔍 الخطوة 1: التشخيص التلقائي
```
🖱️ اضغط دبل كليك على: تشخيص_المشكلة.bat
📋 سيخبرك بالضبط ما المشكلة
```

##### 🔧 الخطوة 2: تثبيت المتطلبات

**أ) تثبيت .NET 6.0:**
1. اذه<PERSON> إلى: https://dotnet.microsoft.com/download/dotnet/6.0
2. حمل ".NET Desktop Runtime 6.0.x"
3. ثبته وأعد تشغيل الجهاز

**ب) تثبيت Access Database Engine:**
1. اذهب إلى: https://www.microsoft.com/en-us/download/details.aspx?id=54920
2. حمل "AccessDatabaseEngine_X64.exe" (للأجهزة الحديثة)
3. ثبته وأعد تشغيل الجهاز

##### 🚀 الخطوة 3: التشغيل
```
🖱️ جرب هذه الملفات بالترتيب:
1. تشغيل_نهائي.bat
2. تشغيل_بسيط.bat  
3. اختبار_سريع.bat
```

---

### 2️⃣ المشكلة: "Build Failed" أو أخطاء البناء

#### الحلول:
1. **شغل كـ Administrator:**
   - كليك يمين على الملف
   - اختر "Run as administrator"

2. **أغلق مضاد الفيروسات مؤقتاً:**
   - أغلق Windows Defender أو أي مضاد فيروسات
   - جرب التشغيل مرة أخرى
   - أعد تشغيل مضاد الفيروسات

3. **تنظيف الملفات المؤقتة:**
   ```
   امسح هذه المجلدات:
   📁 bin\
   📁 obj\
   ```

4. **إعادة بناء المشروع:**
   ```
   dotnet clean
   dotnet restore
   dotnet build
   ```

---

### 3️⃣ المشكلة: "Provider not found" أو مشاكل قاعدة البيانات

#### الحلول:
1. **تأكد من تثبيت Access Database Engine**
2. **جرب إصدارات مختلفة:**
   - Access Database Engine 2016
   - Microsoft 365 Access Runtime
3. **تأكد من تطابق البت (32/64):**
   - إذا كان Office 32-bit، ثبت Engine 32-bit
   - إذا كان Office 64-bit، ثبت Engine 64-bit

---

### 4️⃣ المشكلة: النافذة لا تظهر

#### الحلول:
1. **تحقق من Task Manager:**
   - ابحث عن "AccountingSystem11" في العمليات
   - إذا كان موجود، أغلقه وأعد التشغيل

2. **تحقق من الشاشات المتعددة:**
   - قد تكون النافذة على شاشة أخرى
   - اضغط Alt+Tab للتنقل بين النوافذ

3. **أعد تشغيل الجهاز:**
   - أحياناً يحل مشاكل الذاكرة والعمليات

---

### 5️⃣ المشكلة: "Access Denied" أو مشاكل الصلاحيات

#### الحلول:
1. **شغل كـ Administrator:**
   - كليك يمين → Run as administrator

2. **تحقق من مجلد البرنامج:**
   - تأكد أنه ليس في مجلد محمي (مثل Program Files)
   - انقله لمجلد آخر (مثل Desktop أو Documents)

3. **تحقق من صلاحيات المجلد:**
   - كليك يمين على مجلد البرنامج
   - Properties → Security
   - تأكد أن لديك Full Control

---

## 🆘 إذا لم تنجح الحلول أعلاه

### الحل الطارئ - نسخة مبسطة:

1. **حمل .NET 6.0 Desktop Runtime** (إجباري)
2. **شغل هذا الأمر في Command Prompt:**
   ```
   cd "مجلد البرنامج"
   dotnet new wpf -n TestApp
   cd TestApp
   dotnet run
   ```

3. **إذا عمل، المشكلة في البرنامج نفسه**
4. **إذا لم يعمل، المشكلة في .NET أو النظام**

---

## 📞 طلب المساعدة

### معلومات مطلوبة عند طلب المساعدة:

1. **نظام التشغيل:**
   - Windows 7/8/10/11
   - 32-bit أو 64-bit

2. **رسالة الخطأ الكاملة:**
   - انسخ النص كاملاً
   - أو صورة للشاشة

3. **ما جربته:**
   - أي حلول جربتها من هذا الدليل

4. **نتيجة التشخيص:**
   - شغل `تشخيص_المشكلة.bat` وانسخ النتيجة

### طرق التواصل:
- 📧 البريد: <EMAIL>
- 📱 الهاتف: +20 xxx xxx xxxx
- 💬 الدردشة: متوفرة في الموقع

---

## ✅ نصائح لتجنب المشاكل

1. **ثبت المتطلبات أولاً** قبل تشغيل البرنامج
2. **شغل كـ Administrator** دائماً في البداية
3. **أغلق مضاد الفيروسات** أثناء التثبيت
4. **تأكد من وجود مساحة كافية** (1 GB على الأقل)
5. **أعد تشغيل الجهاز** بعد تثبيت المتطلبات

---

**نظام المحاسبة 11** - دليل حل المشاكل 🇪🇬
