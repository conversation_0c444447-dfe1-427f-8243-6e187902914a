@echo off
title نظام المحاسبة 11 - قاعدة بيانات محلية
color 0B
echo.
echo ========================================================
echo        نظام المحاسبة 11 - قاعدة بيانات محلية
echo ========================================================
echo.
echo 🗃️  يستخدم قاعدة بيانات Access محلية
echo 🚫 لا يحتاج اتصال بالإنترنت
echo 💾 البيانات محفوظة على جهازك
echo.

echo [1] جاري التحقق من .NET...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ❌ خطأ: .NET غير مثبت!
    echo.
    echo 📥 يرجى تحميل .NET 6.0 Desktop Runtime من:
    echo 🔗 https://dotnet.microsoft.com/download/dotnet/6.0
    echo.
    echo اختر: ".NET Desktop Runtime 6.0.x" للويندوز
    echo.
    pause
    start https://dotnet.microsoft.com/download/dotnet/6.0
    exit /b 1
)

echo ✅ تم العثور على .NET
echo.

echo [2] جاري التحقق من Microsoft Access Database Engine...
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Office\ClickToRun\REGISTRY\MACHINE\Software\Classes\CLSID\{3BE786A4-5C15-11DB-B0DE-0800200C9A66}" >nul 2>&1
if %errorlevel% neq 0 (
    reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Classes\CLSID\{3BE786A4-5C15-11DB-B0DE-0800200C9A66}" >nul 2>&1
    if %errorlevel% neq 0 (
        echo.
        echo ⚠️  تحذير: Microsoft Access Database Engine غير مثبت
        echo.
        echo 📥 لتشغيل قاعدة البيانات المحلية، يرجى تحميل:
        echo 🔗 Microsoft Access Database Engine 2016 Redistributable
        echo.
        echo سيتم فتح رابط التحميل...
        timeout /t 3 /nobreak >nul
        start https://www.microsoft.com/en-us/download/details.aspx?id=54920
        echo.
        echo بعد التثبيت، أعد تشغيل هذا الملف
        pause
        exit /b 1
    )
)

echo ✅ تم العثور على Access Database Engine
echo.

echo [3] جاري إنشاء مجلد البيانات...
if not exist "Data" mkdir Data
echo ✅ تم إنشاء مجلد البيانات
echo.

echo [4] جاري تحضير البرنامج...
dotnet restore --verbosity quiet
if %errorlevel% neq 0 (
    echo ❌ خطأ في تحضير البرنامج
    echo جرب تشغيل الملف كـ Administrator
    pause
    exit /b 1
)

echo ✅ تم تحضير البرنامج
echo.

echo [5] جاري بناء البرنامج...
dotnet build --verbosity quiet
if %errorlevel% neq 0 (
    echo ❌ خطأ في بناء البرنامج
    pause
    exit /b 1
)

echo ✅ تم بناء البرنامج بنجاح
echo.

echo ========================================================
echo 🎉 جاري تشغيل نظام المحاسبة 11...
echo ========================================================
echo.
echo 🗃️  قاعدة البيانات: Access محلية
echo 📁 مكان البيانات: Data\AccountingSystem11.accdb
echo.
echo 🔐 بيانات تسجيل الدخول:
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo.
echo ⚠️  مهم: غير كلمة المرور بعد تسجيل الدخول!
echo.
echo 💡 نصائح:
echo    • البيانات محفوظة محلياً على جهازك
echo    • لا تحتاج اتصال بالإنترنت للعمل
echo    • احفظ نسخة احتياطية من مجلد Data دورياً
echo.
echo ========================================================

timeout /t 5 /nobreak >nul
dotnet run

echo.
echo تم إغلاق البرنامج
echo.
echo 💾 البيانات محفوظة في: Data\AccountingSystem11.accdb
echo 🔄 لإعادة التشغيل، اضغط على هذا الملف مرة أخرى
echo.
pause
