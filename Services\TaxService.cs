using AccountingSystem11.Data;
using AccountingSystem11.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;
using System.Text.RegularExpressions;

namespace AccountingSystem11.Services
{
    /// <summary>
    /// Tax service implementation for Egyptian tax system
    /// </summary>
    public class TaxService : ITaxService
    {
        private readonly AccountingDbContext _context;
        private const decimal DEFAULT_VAT_RATE = 14m; // Egyptian VAT rate

        public TaxService(AccountingDbContext context)
        {
            _context = context;
        }

        public decimal CalculateVat(decimal amount, decimal vatRate = DEFAULT_VAT_RATE)
        {
            return Math.Round(amount * (vatRate / 100), 2);
        }

        public decimal CalculateTotalTax(decimal amount, decimal vatRate = DEFAULT_VAT_RATE, decimal additionalTaxRate = 0)
        {
            var vat = CalculateVat(amount, vatRate);
            var additionalTax = Math.Round(amount * (additionalTaxRate / 100), 2);
            return vat + additionalTax;
        }

        public decimal GetCurrentVatRate()
        {
            return DEFAULT_VAT_RATE;
        }

        public async Task<TaxSettings> GetTaxSettingsAsync(BusinessType businessType)
        {
            // For now, return default settings
            // In a real implementation, this would be stored in database
            return await Task.FromResult(new TaxSettings
            {
                BusinessType = businessType,
                VatRate = DEFAULT_VAT_RATE,
                AdditionalTaxRate = 0,
                IsVatRegistered = true,
                EffectiveDate = DateTime.Now,
                IsActive = true
            });
        }

        public async Task<bool> UpdateTaxSettingsAsync(TaxSettings settings)
        {
            // Implementation would store settings in database
            return await Task.FromResult(true);
        }

        public async Task<TaxReport> GenerateTaxReportAsync(DateTime startDate, DateTime endDate)
        {
            var invoices = await _context.Invoices
                .Include(i => i.Customer)
                .Where(i => i.InvoiceType == InvoiceType.Sales &&
                           i.InvoiceDate >= startDate &&
                           i.InvoiceDate <= endDate &&
                           i.Status == InvoiceStatus.Approved)
                .ToListAsync();

            var report = new TaxReport
            {
                StartDate = startDate,
                EndDate = endDate,
                TotalSales = invoices.Sum(i => i.TotalAmount),
                TaxableAmount = invoices.Sum(i => i.AmountAfterDiscount),
                VatAmount = invoices.Sum(i => i.VatAmount),
                AdditionalTaxAmount = invoices.Sum(i => i.AdditionalTaxAmount),
                TotalTaxAmount = invoices.Sum(i => i.TotalTaxAmount),
                InvoiceCount = invoices.Count
            };

            foreach (var invoice in invoices)
            {
                report.Items.Add(new TaxReportItem
                {
                    InvoiceNumber = invoice.InvoiceNumber,
                    InvoiceDate = invoice.InvoiceDate,
                    CustomerName = invoice.Customer.Name,
                    CustomerTaxNumber = invoice.Customer.TaxNumber,
                    Amount = invoice.AmountAfterDiscount,
                    VatAmount = invoice.VatAmount,
                    TotalAmount = invoice.TotalAmount
                });
            }

            return report;
        }

        public async Task<VatReturnData> GetVatReturnDataAsync(DateTime startDate, DateTime endDate)
        {
            var salesInvoices = await _context.Invoices
                .Where(i => i.InvoiceType == InvoiceType.Sales &&
                           i.InvoiceDate >= startDate &&
                           i.InvoiceDate <= endDate &&
                           i.Status == InvoiceStatus.Approved)
                .ToListAsync();

            var purchaseInvoices = await _context.Invoices
                .Where(i => i.InvoiceType == InvoiceType.Purchase &&
                           i.InvoiceDate >= startDate &&
                           i.InvoiceDate <= endDate &&
                           i.Status == InvoiceStatus.Approved)
                .ToListAsync();

            var outputVat = salesInvoices.Sum(i => i.VatAmount);
            var inputVat = purchaseInvoices.Sum(i => i.VatAmount);

            return new VatReturnData
            {
                PeriodStart = startDate,
                PeriodEnd = endDate,
                OutputVat = outputVat,
                InputVat = inputVat,
                NetVat = outputVat - inputVat,
                TaxableSupplies = salesInvoices.Sum(i => i.AmountAfterDiscount),
                ExemptSupplies = 0, // Would need to calculate based on exempt products
                ZeroRatedSupplies = 0 // Would need to calculate based on zero-rated products
            };
        }

        public bool ValidateTaxNumber(string taxNumber)
        {
            if (string.IsNullOrWhiteSpace(taxNumber))
                return false;

            // Egyptian tax number validation
            // Remove any non-digit characters
            var cleanNumber = Regex.Replace(taxNumber, @"[^\d]", "");

            // Egyptian tax numbers are typically 9 digits
            if (cleanNumber.Length != 9)
                return false;

            // Basic validation - all digits
            return Regex.IsMatch(cleanNumber, @"^\d{9}$");
        }

        public string FormatTaxNumber(string taxNumber)
        {
            if (string.IsNullOrWhiteSpace(taxNumber))
                return string.Empty;

            // Remove any non-digit characters
            var cleanNumber = Regex.Replace(taxNumber, @"[^\d]", "");

            // Format as XXX-XXX-XXX
            if (cleanNumber.Length == 9)
            {
                return $"{cleanNumber.Substring(0, 3)}-{cleanNumber.Substring(3, 3)}-{cleanNumber.Substring(6, 3)}";
            }

            return cleanNumber;
        }

        public bool IsProductTaxExempt(int productId)
        {
            // Implementation would check product tax exemption status
            // For now, assume all products are taxable
            return false;
        }

        public async Task<decimal> GetProductTaxRateAsync(int productId)
        {
            var product = await _context.Products.FindAsync(productId);
            
            if (product == null || !product.IsTaxable)
                return 0;

            return product.TaxRate ?? DEFAULT_VAT_RATE;
        }
    }
}
