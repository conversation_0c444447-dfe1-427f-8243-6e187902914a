using AccountingSystem11.Services;
using AccountingSystem11.Models;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Input;

namespace AccountingSystem11.ViewModels
{
    /// <summary>
    /// Customer view model (placeholder)
    /// </summary>
    public class CustomerViewModel : BaseViewModel
    {
        private readonly ICustomerService _customerService;

        public CustomerViewModel(ICustomerService customerService)
        {
            _customerService = customerService;

            Customers = new ObservableCollection<Customer>();
            LoadCustomersCommand = new AsyncRelayCommand(LoadCustomersAsync);
        }

        public ObservableCollection<Customer> Customers { get; }
        public ICommand LoadCustomersCommand { get; }

        private async Task LoadCustomersAsync()
        {
            await ExecuteAsync(async () =>
            {
                var customers = await _customerService.GetAllCustomersAsync();
                Customers.Clear();
                foreach (var customer in customers)
                {
                    Customers.Add(customer);
                }
            }, "جاري تحميل العملاء...");
        }
    }
}
