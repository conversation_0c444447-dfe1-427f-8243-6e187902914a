using System.ComponentModel.DataAnnotations;

namespace AccountingSystem11.Models
{
    /// <summary>
    /// User permission for granular access control
    /// </summary>
    public class UserPermission : BaseEntity
    {
        [Required]
        public int UserId { get; set; }
        public virtual User User { get; set; }

        [Required]
        public Permission Permission { get; set; }

        /// <summary>
        /// Whether permission is granted
        /// </summary>
        public bool IsGranted { get; set; } = true;

        /// <summary>
        /// Permission notes
        /// </summary>
        [MaxLength(200)]
        public string Notes { get; set; }
    }

    public enum Permission
    {
        // Customer Management
        ViewCustomers = 1,
        CreateCustomers = 2,
        EditCustomers = 3,
        DeleteCustomers = 4,

        // Product Management
        ViewProducts = 10,
        CreateProducts = 11,
        EditProducts = 12,
        DeleteProducts = 13,
        ManageCategories = 14,

        // Invoice Management
        ViewInvoices = 20,
        CreateInvoices = 21,
        EditInvoices = 22,
        DeleteInvoices = 23,
        PrintInvoices = 24,
        EmailInvoices = 25,
        CancelInvoices = 26,

        // Payment Management
        ViewPayments = 30,
        CreatePayments = 31,
        EditPayments = 32,
        DeletePayments = 33,

        // Inventory Management
        ViewInventory = 40,
        ManageInventory = 41,
        ViewInventoryTransactions = 42,
        CreateInventoryTransactions = 43,
        EditInventoryTransactions = 44,

        // Supplier Management
        ViewSuppliers = 50,
        CreateSuppliers = 51,
        EditSuppliers = 52,
        DeleteSuppliers = 53,

        // Reports
        ViewSalesReports = 60,
        ViewFinancialReports = 61,
        ViewInventoryReports = 62,
        ViewTaxReports = 63,
        ViewCustomerReports = 64,
        ViewSupplierReports = 65,
        ExportReports = 66,

        // System Administration
        ManageUsers = 70,
        ManagePermissions = 71,
        ManageSettings = 72,
        ViewSystemLogs = 73,
        BackupDatabase = 74,
        RestoreDatabase = 75,

        // E-Invoice
        SubmitEInvoices = 80,
        ViewEInvoiceStatus = 81,
        CancelEInvoices = 82,

        // Excel Integration
        ImportFromExcel = 90,
        ExportToExcel = 91,

        // Advanced Features
        ManageTaxSettings = 100,
        ViewDashboard = 101,
        ManageCompanySettings = 102
    }
}
