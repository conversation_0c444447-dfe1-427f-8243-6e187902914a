using AccountingSystem11.Data;
using AccountingSystem11.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AccountingSystem11.Services
{
    /// <summary>
    /// User service implementation
    /// </summary>
    public class UserService : IUserService
    {
        private readonly AccountingDbContext _context;

        public UserService(AccountingDbContext context)
        {
            _context = context;
        }

        public async Task<User> AuthenticateAsync(string username, string password)
        {
            if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                return null;

            var user = await _context.Users
                .Include(u => u.Permissions)
                .FirstOrDefaultAsync(u => u.Username == username && u.IsActive);

            if (user == null)
                return null;

            // Check if account is locked
            if (user.IsLocked())
                return null;

            // Verify password
            if (!BCrypt.Net.BCrypt.Verify(password, user.PasswordHash))
            {
                // Increment failed login attempts
                user.FailedLoginAttempts++;
                
                // Lock account after 5 failed attempts
                if (user.FailedLoginAttempts >= 5)
                {
                    user.LockedUntil = DateTime.Now.AddMinutes(30);
                }
                
                await _context.SaveChangesAsync();
                return null;
            }

            // Reset failed login attempts on successful login
            user.FailedLoginAttempts = 0;
            user.LastLoginDate = DateTime.Now;
            user.LockedUntil = null;
            
            await _context.SaveChangesAsync();
            return user;
        }

        public async Task<User> GetUserByIdAsync(int id)
        {
            return await _context.Users
                .Include(u => u.Permissions)
                .Include(u => u.Sessions)
                .FirstOrDefaultAsync(u => u.Id == id);
        }

        public async Task<User> GetUserByUsernameAsync(string username)
        {
            if (string.IsNullOrWhiteSpace(username))
                return null;

            return await _context.Users
                .Include(u => u.Permissions)
                .FirstOrDefaultAsync(u => u.Username == username);
        }

        public async Task<User> CreateUserAsync(User user, string password)
        {
            if (user == null)
                throw new ArgumentNullException(nameof(user));

            if (string.IsNullOrWhiteSpace(password))
                throw new ArgumentException("كلمة المرور مطلوبة", nameof(password));

            // Validate unique constraints
            var existingUsername = await UserExistsAsync(user.Username);
            if (existingUsername)
                throw new InvalidOperationException("اسم المستخدم موجود بالفعل");

            var existingEmail = await EmailExistsAsync(user.Email);
            if (existingEmail)
                throw new InvalidOperationException("البريد الإلكتروني موجود بالفعل");

            // Hash password
            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(password);
            user.CreatedAt = DateTime.Now;

            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            return user;
        }

        public async Task<User> UpdateUserAsync(User user)
        {
            if (user == null)
                throw new ArgumentNullException(nameof(user));

            var existingUser = await _context.Users.FindAsync(user.Id);
            if (existingUser == null)
                throw new InvalidOperationException("المستخدم غير موجود");

            // Validate unique constraints
            var existingUsername = await UserExistsAsync(user.Username);
            if (existingUsername && existingUser.Username != user.Username)
                throw new InvalidOperationException("اسم المستخدم موجود بالفعل");

            var existingEmail = await EmailExistsAsync(user.Email, user.Id);
            if (existingEmail)
                throw new InvalidOperationException("البريد الإلكتروني موجود بالفعل");

            // Update properties (excluding password)
            existingUser.Username = user.Username;
            existingUser.FullName = user.FullName;
            existingUser.Email = user.Email;
            existingUser.Phone = user.Phone;
            existingUser.Mobile = user.Mobile;
            existingUser.Role = user.Role;
            existingUser.IsActive = user.IsActive;
            existingUser.Notes = user.Notes;
            existingUser.Update();

            await _context.SaveChangesAsync();
            return existingUser;
        }

        public async Task<bool> DeleteUserAsync(int id)
        {
            var user = await _context.Users.FindAsync(id);
            if (user == null)
                return false;

            // Don't allow deleting the last admin user
            if (user.Role == UserRole.Admin)
            {
                var adminCount = await _context.Users
                    .CountAsync(u => u.Role == UserRole.Admin && u.IsActive && u.Id != id);
                
                if (adminCount == 0)
                    throw new InvalidOperationException("لا يمكن حذف آخر مدير في النظام");
            }

            user.Delete();
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
                return false;

            // Verify current password
            if (!BCrypt.Net.BCrypt.Verify(currentPassword, user.PasswordHash))
                return false;

            // Update password
            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
            user.MustChangePassword = false;
            user.Update();

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ResetPasswordAsync(int userId, string newPassword)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
                return false;

            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
            user.MustChangePassword = true;
            user.Update();

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> LockUserAsync(int userId, int lockDurationMinutes = 30)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
                return false;

            user.LockedUntil = DateTime.Now.AddMinutes(lockDurationMinutes);
            user.Update();

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> UnlockUserAsync(int userId)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
                return false;

            user.LockedUntil = null;
            user.FailedLoginAttempts = 0;
            user.Update();

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<User>> GetAllUsersAsync()
        {
            return await _context.Users
                .Include(u => u.Permissions)
                .OrderBy(u => u.FullName)
                .ToListAsync();
        }

        public async Task<IEnumerable<User>> GetActiveUsersAsync()
        {
            return await _context.Users
                .Include(u => u.Permissions)
                .Where(u => u.IsActive)
                .OrderBy(u => u.FullName)
                .ToListAsync();
        }

        public async Task<bool> UserExistsAsync(string username)
        {
            if (string.IsNullOrWhiteSpace(username))
                return false;

            return await _context.Users.AnyAsync(u => u.Username == username);
        }

        public async Task<bool> EmailExistsAsync(string email, int? excludeId = null)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            var query = _context.Users.Where(u => u.Email == email);
            
            if (excludeId.HasValue)
                query = query.Where(u => u.Id != excludeId.Value);

            return await query.AnyAsync();
        }

        public async Task<bool> HasPermissionAsync(int userId, Permission permission)
        {
            var user = await _context.Users
                .Include(u => u.Permissions)
                .FirstOrDefaultAsync(u => u.Id == userId);

            return user?.HasPermission(permission) ?? false;
        }

        public async Task<bool> GrantPermissionAsync(int userId, Permission permission)
        {
            var existingPermission = await _context.UserPermissions
                .FirstOrDefaultAsync(up => up.UserId == userId && up.Permission == permission);

            if (existingPermission != null)
            {
                existingPermission.IsGranted = true;
                existingPermission.Update();
            }
            else
            {
                var userPermission = new UserPermission
                {
                    UserId = userId,
                    Permission = permission,
                    IsGranted = true,
                    CreatedAt = DateTime.Now
                };
                _context.UserPermissions.Add(userPermission);
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RevokePermissionAsync(int userId, Permission permission)
        {
            var existingPermission = await _context.UserPermissions
                .FirstOrDefaultAsync(up => up.UserId == userId && up.Permission == permission);

            if (existingPermission != null)
            {
                existingPermission.IsGranted = false;
                existingPermission.Update();
                await _context.SaveChangesAsync();
            }

            return true;
        }

        public async Task<IEnumerable<Permission>> GetUserPermissionsAsync(int userId)
        {
            return await _context.UserPermissions
                .Where(up => up.UserId == userId && up.IsGranted)
                .Select(up => up.Permission)
                .ToListAsync();
        }

        public async Task<UserSession> CreateSessionAsync(int userId, string ipAddress, string userAgent)
        {
            var session = new UserSession
            {
                UserId = userId,
                SessionToken = Guid.NewGuid().ToString(),
                LoginTime = DateTime.Now,
                IpAddress = ipAddress,
                UserAgent = userAgent,
                IsActive = true,
                ExpiryTime = DateTime.Now.AddHours(8),
                CreatedAt = DateTime.Now
            };

            _context.UserSessions.Add(session);
            await _context.SaveChangesAsync();

            return session;
        }

        public async Task<bool> EndSessionAsync(string sessionToken)
        {
            var session = await _context.UserSessions
                .FirstOrDefaultAsync(s => s.SessionToken == sessionToken);

            if (session != null)
            {
                session.EndSession();
                await _context.SaveChangesAsync();
                return true;
            }

            return false;
        }

        public async Task<bool> IsSessionValidAsync(string sessionToken)
        {
            var session = await _context.UserSessions
                .FirstOrDefaultAsync(s => s.SessionToken == sessionToken);

            return session != null && !session.IsExpired();
        }

        public async Task<User> GetUserBySessionTokenAsync(string sessionToken)
        {
            var session = await _context.UserSessions
                .Include(s => s.User)
                    .ThenInclude(u => u.Permissions)
                .FirstOrDefaultAsync(s => s.SessionToken == sessionToken);

            if (session != null && !session.IsExpired())
            {
                // Extend session
                session.ExtendSession();
                await _context.SaveChangesAsync();
                return session.User;
            }

            return null;
        }
    }
}
