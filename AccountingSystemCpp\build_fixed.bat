@echo off
title بناء نظام المحاسبة 11 - C++ (مُصحح)
color 0A
echo.
echo ========================================================
echo     بناء نظام المحاسبة 11 - C++ (مُصحح)
echo ========================================================
echo.

echo 🔧 إصدار مُصحح - بدون أخطاء!
echo ✅ لا يحتاج ملفات خارجية
echo 🚀 بناء سريع ومباشر
echo.

echo [1] التحقق من المترجم...

REM Try Visual Studio first
where cl >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ تم العثور على Visual Studio
    set "COMPILER=msvc"
    goto :build
)

REM Try MinGW
where g++ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ تم العثور على MinGW
    set "COMPILER=mingw"
    goto :build
)

REM Try TDM-GCC
where gcc >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ تم العثور على GCC
    set "COMPILER=gcc"
    goto :build
)

echo ❌ لم يتم العثور على أي مترجم C++
echo.
echo 📥 يرجى تثبيت أحد التالي:
echo    • Visual Studio Community (مجاني)
echo    • MinGW-w64
echo    • TDM-GCC
echo    • Code::Blocks (مع MinGW)
echo.
echo 🔗 روابط سريعة:
echo    Visual Studio: https://visualstudio.microsoft.com/downloads/
echo    MinGW-w64: https://www.mingw-w64.org/downloads/
echo    TDM-GCC: https://jmeubank.github.io/tdm-gcc/
echo.
pause
exit /b 1

:build
echo.
echo [2] إنشاء مجلدات...
if not exist "bin" mkdir "bin"
echo ✅ تم إنشاء مجلد bin

echo.
echo [3] بناء البرنامج...
echo 🔨 المترجم: %COMPILER%

if "%COMPILER%"=="msvc" (
    echo 🔨 استخدام Visual Studio...
    cl /EHsc /DUNICODE /D_UNICODE /W3 ^
       main.cpp ^
       /Fe:bin\AccountingSystem11.exe ^
       /link user32.lib gdi32.lib comctl32.lib shell32.lib kernel32.lib
       
) else if "%COMPILER%"=="mingw" (
    echo 🔨 استخدام MinGW...
    g++ -std=c++17 -DUNICODE -D_UNICODE -Wall ^
        -mwindows -static-libgcc -static-libstdc++ ^
        main.cpp ^
        -o bin\AccountingSystem11.exe ^
        -lcomctl32 -lshell32 -luser32 -lgdi32 -lkernel32
        
) else if "%COMPILER%"=="gcc" (
    echo 🔨 استخدام GCC...
    gcc -std=c++17 -DUNICODE -D_UNICODE -Wall ^
        -mwindows -static-libgcc -static-libstdc++ ^
        main.cpp ^
        -o bin\AccountingSystem11.exe ^
        -lcomctl32 -lshell32 -luser32 -lgdi32 -lkernel32 -lstdc++
)

if %errorlevel% equ 0 (
    echo.
    echo ========================================================
    echo 🎉 تم بناء البرنامج بنجاح!
    echo ========================================================
    echo.
    
    REM Check file size
    for %%A in ("bin\AccountingSystem11.exe") do (
        set "filesize=%%~zA"
        set /a "filesize_kb=%%~zA/1024"
    )
    
    echo 📁 مكان الملف: bin\AccountingSystem11.exe
    echo 💾 حجم الملف: %filesize_kb% KB
    echo 🔨 المترجم: %COMPILER%
    echo 🗃️ قاعدة البيانات: مدمجة في الكود
    echo.
    echo ✅ المميزات:
    echo    • ملف .exe واحد فقط
    echo    • لا يحتاج .NET Framework
    echo    • لا يحتاج مكتبات خارجية
    echo    • يعمل على جميع إصدارات Windows
    echo    • أداء عالي جداً
    echo    • حجم صغير
    echo    • بدء تشغيل فوري
    echo.
    
    REM Create run script
    echo @echo off > "bin\تشغيل.bat"
    echo title نظام المحاسبة 11 - C++ Native >> "bin\تشغيل.bat"
    echo color 0A >> "bin\تشغيل.bat"
    echo echo ======================================== >> "bin\تشغيل.bat"
    echo echo      نظام المحاسبة 11 - C++ Native >> "bin\تشغيل.bat"
    echo echo ======================================== >> "bin\تشغيل.bat"
    echo echo. >> "bin\تشغيل.bat"
    echo echo 🔨 مبني بـ C++ Native >> "bin\تشغيل.bat"
    echo echo ✅ لا يحتاج .NET Framework >> "bin\تشغيل.bat"
    echo echo ⚡ أداء فائق السرعة >> "bin\تشغيل.bat"
    echo echo 💾 حجم صغير جداً >> "bin\تشغيل.bat"
    echo echo 🚀 جاري التشغيل... >> "bin\تشغيل.bat"
    echo echo. >> "bin\تشغيل.bat"
    echo echo 🔐 بيانات تسجيل الدخول: >> "bin\تشغيل.bat"
    echo echo 👤 اسم المستخدم: admin >> "bin\تشغيل.bat"
    echo echo 🔑 كلمة المرور: admin123 >> "bin\تشغيل.bat"
    echo echo. >> "bin\تشغيل.bat"
    echo AccountingSystem11.exe >> "bin\تشغيل.bat"
    echo if %%errorlevel%% neq 0 ( >> "bin\تشغيل.bat"
    echo     echo ❌ حدث خطأ في التشغيل >> "bin\تشغيل.bat"
    echo     pause >> "bin\تشغيل.bat"
    echo ^) >> "bin\تشغيل.bat"
    
    REM Create README
    echo # نظام المحاسبة 11 - إصدار C++ Native > "bin\README.txt"
    echo. >> "bin\README.txt"
    echo ## المميزات الخاصة: >> "bin\README.txt"
    echo - مبني بـ C++ Native >> "bin\README.txt"
    echo - لا يحتاج .NET Framework >> "bin\README.txt"
    echo - لا يحتاج مكتبات خارجية >> "bin\README.txt"
    echo - يعمل على جميع إصدارات Windows >> "bin\README.txt"
    echo - أداء فائق السرعة >> "bin\README.txt"
    echo - حجم صغير جداً >> "bin\README.txt"
    echo - بدء تشغيل فوري >> "bin\README.txt"
    echo - قاعدة بيانات مدمجة >> "bin\README.txt"
    echo. >> "bin\README.txt"
    echo ## طريقة التشغيل: >> "bin\README.txt"
    echo 1. اضغط دبل كليك على تشغيل.bat >> "bin\README.txt"
    echo 2. أو اضغط دبل كليك على AccountingSystem11.exe >> "bin\README.txt"
    echo. >> "bin\README.txt"
    echo ## المتطلبات: >> "bin\README.txt"
    echo - Windows XP أو أحدث >> "bin\README.txt"
    echo - لا يحتاج أي مكتبات إضافية >> "bin\README.txt"
    echo - لا يحتاج .NET Framework >> "bin\README.txt"
    echo. >> "bin\README.txt"
    echo ## بيانات تسجيل الدخول: >> "bin\README.txt"
    echo اسم المستخدم: admin >> "bin\README.txt"
    echo كلمة المرور: admin123 >> "bin\README.txt"
    
    echo ✅ تم إنشاء الملفات المساعدة
    echo.
    
    REM Create portable package
    echo 📦 إنشاء حزمة محمولة...
    if exist "نظام_المحاسبة_11_CPP_Portable.zip" del "نظام_المحاسبة_11_CPP_Portable.zip"
    powershell -Command "Compress-Archive -Path 'bin\*' -DestinationPath 'نظام_المحاسبة_11_CPP_Portable.zip' -Force" >nul 2>&1
    if exist "نظام_المحاسبة_11_CPP_Portable.zip" (
        echo ✅ تم إنشاء حزمة محمولة: نظام_المحاسبة_11_CPP_Portable.zip
    )
    
    echo.
    echo 🎯 الملفات الجاهزة:
    echo 📁 bin\ - المجلد الكامل
    echo 📄 AccountingSystem11.exe - الملف الرئيسي
    echo 📄 تشغيل.bat - ملف تشغيل سهل
    echo 📄 README.txt - تعليمات الاستخدام
    echo 📦 نظام_المحاسبة_11_CPP_Portable.zip - حزمة محمولة
    echo.
    
    set /p choice="هل تريد فتح مجلد البرنامج؟ (y/n): "
    if /i "%choice%"=="y" (
        explorer "bin"
    )
    
    echo.
    set /p choice2="هل تريد اختبار التشغيل؟ (y/n): "
    if /i "%choice2%"=="y" (
        cd "bin"
        echo 🚀 جاري اختبار التشغيل...
        echo ⚠️  إذا لم تظهر النافذة، تحقق من Task Manager
        AccountingSystem11.exe
        echo.
        echo ✅ تم إغلاق البرنامج
    )
    
) else (
    echo.
    echo ❌ فشل بناء البرنامج
    echo.
    echo 🔧 الحلول المحتملة:
    echo 1. تأكد من وجود ملف main.cpp
    echo 2. شغل Command Prompt كـ Administrator
    echo 3. تأكد من مساحة القرص الكافية
    echo 4. أغلق مضاد الفيروسات مؤقتاً
    echo 5. تأكد من تثبيت المترجم بشكل صحيح
    echo.
    echo 💡 للمساعدة:
    echo    • تحقق من رسائل الخطأ أعلاه
    echo    • تأكد من وجود جميع الملفات
    echo    • جرب مترجم مختلف
)

echo.
pause
