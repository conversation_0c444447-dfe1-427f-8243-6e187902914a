using AccountingSystem11.Services;
using AccountingSystem11.Models;
using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Input;

namespace AccountingSystem11.ViewModels
{
    /// <summary>
    /// Product view model (placeholder)
    /// </summary>
    public class ProductViewModel : BaseViewModel
    {
        private readonly IProductService _productService;

        public ProductViewModel(IProductService productService)
        {
            _productService = productService;

            Products = new ObservableCollection<Product>();
            LoadProductsCommand = new AsyncRelayCommand(LoadProductsAsync);
        }

        public ObservableCollection<Product> Products { get; }
        public ICommand LoadProductsCommand { get; }

        private async Task LoadProductsAsync()
        {
            try
            {
                var products = await _productService.GetAllProductsAsync();
                Products.Clear();
                foreach (var product in products)
                {
                    Products.Add(product);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل المنتجات: {ex.Message}");
            }
        }
    }
}
