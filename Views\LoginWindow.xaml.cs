using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using AccountingSystem11.Services;
using AccountingSystem11.Models;

namespace AccountingSystem11.Views
{
    public partial class LoginWindow : Window
    {
        private readonly IUserService _userService;

        public LoginWindow()
        {
            InitializeComponent();
            _userService = new UserService();
            
            // تحميل بيانات تسجيل الدخول المحفوظة
            LoadSavedCredentials();
            
            // إعداد أحداث لوحة المفاتيح
            UsernameTextBox.KeyDown += OnKeyDown;
            PasswordBox.KeyDown += OnKeyDown;
            
            // تركيز على حقل اسم المستخدم
            UsernameTextBox.Focus();
        }

        private void LoadSavedCredentials()
        {
            try
            {
                // تحميل بيانات تسجيل الدخول المحفوظة من الإعدادات
                var savedUsername = Properties.Settings.Default.SavedUsername;
                var rememberMe = Properties.Settings.Default.RememberMe;

                if (rememberMe && !string.IsNullOrEmpty(savedUsername))
                {
                    UsernameTextBox.Text = savedUsername;
                    RememberMeCheckBox.IsChecked = true;
                    PasswordBox.Focus();
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل البيانات المحفوظة: {ex.Message}");
            }
        }

        private void OnKeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                LoginButton_Click(sender, e);
            }
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            await PerformLogin();
        }

        private async Task PerformLogin()
        {
            try
            {
                // إخفاء رسالة الخطأ السابقة
                HideError();
                
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(UsernameTextBox.Text))
                {
                    ShowError("يرجى إدخال اسم المستخدم");
                    UsernameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(PasswordBox.Password))
                {
                    ShowError("يرجى إدخال كلمة المرور");
                    PasswordBox.Focus();
                    return;
                }

                // إظهار شريط التحميل
                ShowLoading(true);

                // محاولة تسجيل الدخول
                var loginResult = await _userService.LoginAsync(UsernameTextBox.Text, PasswordBox.Password);

                if (loginResult.IsSuccess)
                {
                    // حفظ بيانات تسجيل الدخول إذا كان المستخدم يريد ذلك
                    if (RememberMeCheckBox.IsChecked == true)
                    {
                        SaveCredentials();
                    }
                    else
                    {
                        ClearSavedCredentials();
                    }

                    // إظهار رسالة نجاح
                    ShowSuccess($"مرحباً {loginResult.User.FullName}!");

                    // تأخير قصير لإظهار رسالة النجاح
                    await Task.Delay(1000);

                    // فتح النافذة الرئيسية
                    var mainWindow = new MainWindow();
                    mainWindow.Show();

                    // إغلاق نافذة تسجيل الدخول
                    this.Close();
                }
                else
                {
                    ShowError(loginResult.ErrorMessage ?? "اسم المستخدم أو كلمة المرور غير صحيحة");
                    PasswordBox.Clear();
                    PasswordBox.Focus();
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء تسجيل الدخول: {ex.Message}");
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private void SaveCredentials()
        {
            try
            {
                Properties.Settings.Default.SavedUsername = UsernameTextBox.Text;
                Properties.Settings.Default.RememberMe = true;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في حفظ البيانات: {ex.Message}");
            }
        }

        private void ClearSavedCredentials()
        {
            try
            {
                Properties.Settings.Default.SavedUsername = string.Empty;
                Properties.Settings.Default.RememberMe = false;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في مسح البيانات المحفوظة: {ex.Message}");
            }
        }

        private void CreateAccountButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var createAccountWindow = new CreateAccountWindow();
                createAccountWindow.Owner = this;
                
                if (createAccountWindow.ShowDialog() == true)
                {
                    // إذا تم إنشاء الحساب بنجاح، املأ البيانات
                    UsernameTextBox.Text = createAccountWindow.CreatedUsername;
                    PasswordBox.Focus();
                    ShowSuccess("تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول.");
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في فتح نافذة إنشاء الحساب: {ex.Message}");
            }
        }

        private void ForgotPasswordButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var resetPasswordWindow = new ResetPasswordWindow();
                resetPasswordWindow.Owner = this;
                resetPasswordWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في فتح نافذة استعادة كلمة المرور: {ex.Message}");
            }
        }

        private void ShowError(string message)
        {
            ErrorMessageTextBlock.Text = $"❌ {message}";
            ErrorMessageTextBlock.Foreground = System.Windows.Media.Brushes.Red;
            ErrorMessageTextBlock.Visibility = Visibility.Visible;
        }

        private void ShowSuccess(string message)
        {
            ErrorMessageTextBlock.Text = $"✅ {message}";
            ErrorMessageTextBlock.Foreground = System.Windows.Media.Brushes.Green;
            ErrorMessageTextBlock.Visibility = Visibility.Visible;
        }

        private void HideError()
        {
            ErrorMessageTextBlock.Visibility = Visibility.Collapsed;
        }

        private void ShowLoading(bool show)
        {
            LoadingProgressBar.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
            LoginButton.IsEnabled = !show;
            CreateAccountButton.IsEnabled = !show;
            ForgotPasswordButton.IsEnabled = !show;
        }

        protected override void OnClosed(EventArgs e)
        {
            // إذا تم إغلاق النافذة بدون تسجيل دخول، أغلق التطبيق
            if (Application.Current.MainWindow == null || !Application.Current.MainWindow.IsVisible)
            {
                Application.Current.Shutdown();
            }
            base.OnClosed(e);
        }
    }

    // نتيجة تسجيل الدخول
    public class LoginResult
    {
        public bool IsSuccess { get; set; }
        public User User { get; set; }
        public string ErrorMessage { get; set; }
    }
}
