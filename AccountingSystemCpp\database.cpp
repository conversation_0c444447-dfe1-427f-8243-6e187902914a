#include "database.h"
#include <windows.h>
#include <iostream>
#include <codecvt>
#include <locale>

Database::Database() : db(nullptr) {
    // Get application directory
    wchar_t path[MAX_PATH];
    GetModuleFileName(nullptr, path, MAX_PATH);
    std::wstring fullPath(path);
    std::wstring appDir = fullPath.substr(0, fullPath.find_last_of(L"\\"));
    dbPath = appDir + L"\\accounting_system.db";
}

Database::~Database() {
    if (db) {
        sqlite3_close(db);
    }
}

bool Database::Initialize() {
    std::string dbPathStr = WStringToString(dbPath);
    
    int result = sqlite3_open(dbPathStr.c_str(), &db);
    if (result != SQLITE_OK) {
        return false;
    }

    if (!CreateTables()) {
        return false;
    }

    if (!SeedInitialData()) {
        return false;
    }

    return true;
}

bool Database::CreateTables() {
    std::vector<std::string> createTableQueries = {
        R"(CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            company TEXT,
            phone TEXT,
            mobile TEXT,
            email TEXT,
            address TEXT,
            city TEXT,
            tax_number TEXT,
            credit_limit REAL DEFAULT 0,
            balance REAL DEFAULT 0,
            is_active INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        ))",

        R"(CREATE TABLE IF NOT EXISTS product_categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            code TEXT UNIQUE,
            description TEXT,
            is_active INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        ))",

        R"(CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            description TEXT,
            category_id INTEGER,
            unit TEXT DEFAULT 'قطعة',
            purchase_price REAL DEFAULT 0,
            selling_price REAL DEFAULT 0,
            stock_quantity REAL DEFAULT 0,
            min_stock_level REAL DEFAULT 0,
            is_taxable INTEGER DEFAULT 1,
            tax_rate REAL DEFAULT 14.0,
            is_active INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES product_categories (id)
        ))",

        R"(CREATE TABLE IF NOT EXISTS invoices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_number TEXT UNIQUE NOT NULL,
            customer_id INTEGER,
            invoice_date DATE NOT NULL,
            due_date DATE,
            subtotal REAL DEFAULT 0,
            tax_amount REAL DEFAULT 0,
            discount_amount REAL DEFAULT 0,
            total_amount REAL DEFAULT 0,
            status TEXT DEFAULT 'draft',
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers (id)
        ))",

        R"(CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            full_name TEXT NOT NULL,
            email TEXT,
            role TEXT DEFAULT 'user',
            is_active INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        ))"
    };

    for (const auto& query : createTableQueries) {
        if (!ExecuteSQL(query)) {
            return false;
        }
    }

    return true;
}

bool Database::SeedInitialData() {
    // Check if admin user exists
    sqlite3_stmt* stmt;
    const char* checkUserQuery = "SELECT COUNT(*) FROM users WHERE username = 'admin'";
    
    if (sqlite3_prepare_v2(db, checkUserQuery, -1, &stmt, nullptr) == SQLITE_OK) {
        if (sqlite3_step(stmt) == SQLITE_ROW) {
            int count = sqlite3_column_int(stmt, 0);
            sqlite3_finalize(stmt);
            
            if (count == 0) {
                // Create admin user (password: admin123)
                const char* createUserQuery = R"(
                    INSERT INTO users (username, password_hash, full_name, email, role) 
                    VALUES ('admin', 'admin123', 'مدير النظام', '<EMAIL>', 'admin')
                )";
                ExecuteSQL(createUserQuery);
            }
        } else {
            sqlite3_finalize(stmt);
        }
    }

    // Seed categories
    const char* checkCatQuery = "SELECT COUNT(*) FROM product_categories";
    if (sqlite3_prepare_v2(db, checkCatQuery, -1, &stmt, nullptr) == SQLITE_OK) {
        if (sqlite3_step(stmt) == SQLITE_ROW) {
            int count = sqlite3_column_int(stmt, 0);
            sqlite3_finalize(stmt);
            
            if (count == 0) {
                std::vector<std::string> categoryQueries = {
                    "INSERT INTO product_categories (name, code, description) VALUES ('عام', 'GEN', 'فئة عامة للمنتجات')",
                    "INSERT INTO product_categories (name, code, description) VALUES ('ملابس', 'CLO', 'الملابس والأزياء')",
                    "INSERT INTO product_categories (name, code, description) VALUES ('أغذية', 'FOD', 'المواد الغذائية')",
                    "INSERT INTO product_categories (name, code, description) VALUES ('إلكترونيات', 'ELE', 'الأجهزة الإلكترونية')"
                };
                
                for (const auto& query : categoryQueries) {
                    ExecuteSQL(query);
                }
            }
        } else {
            sqlite3_finalize(stmt);
        }
    }

    // Seed sample customers
    const char* checkCustomerQuery = "SELECT COUNT(*) FROM customers";
    if (sqlite3_prepare_v2(db, checkCustomerQuery, -1, &stmt, nullptr) == SQLITE_OK) {
        if (sqlite3_step(stmt) == SQLITE_ROW) {
            int count = sqlite3_column_int(stmt, 0);
            sqlite3_finalize(stmt);
            
            if (count == 0) {
                std::vector<std::string> customerQueries = {
                    R"(INSERT INTO customers (name, company, phone, mobile, email, city) 
                       VALUES ('عميل تجريبي', 'شركة تجريبية', '02-12345678', '01012345678', '<EMAIL>', 'القاهرة'))",
                    R"(INSERT INTO customers (name, company, phone, mobile, email, city) 
                       VALUES ('أحمد محمد', 'شركة النور', '02-87654321', '01098765432', '<EMAIL>', 'الإسكندرية'))",
                    R"(INSERT INTO customers (name, company, phone, mobile, email, city) 
                       VALUES ('فاطمة علي', 'مؤسسة الأمل', '02-55555555', '01055555555', '<EMAIL>', 'الجيزة'))"
                };
                
                for (const auto& query : customerQueries) {
                    ExecuteSQL(query);
                }
            }
        } else {
            sqlite3_finalize(stmt);
        }
    }

    return true;
}

std::vector<Customer> Database::GetCustomers() {
    std::vector<Customer> customers;
    sqlite3_stmt* stmt;
    
    const char* query = "SELECT id, name, company, phone, mobile, email, address, city, tax_number, credit_limit, balance, is_active FROM customers WHERE is_active = 1 ORDER BY name";
    
    if (sqlite3_prepare_v2(db, query, -1, &stmt, nullptr) == SQLITE_OK) {
        while (sqlite3_step(stmt) == SQLITE_ROW) {
            Customer customer;
            customer.id = sqlite3_column_int(stmt, 0);
            
            const char* name = (const char*)sqlite3_column_text(stmt, 1);
            customer.name = name ? StringToWString(name) : L"";
            
            const char* company = (const char*)sqlite3_column_text(stmt, 2);
            customer.company = company ? StringToWString(company) : L"";
            
            const char* phone = (const char*)sqlite3_column_text(stmt, 3);
            customer.phone = phone ? StringToWString(phone) : L"";
            
            const char* mobile = (const char*)sqlite3_column_text(stmt, 4);
            customer.mobile = mobile ? StringToWString(mobile) : L"";
            
            const char* email = (const char*)sqlite3_column_text(stmt, 5);
            customer.email = email ? StringToWString(email) : L"";
            
            const char* address = (const char*)sqlite3_column_text(stmt, 6);
            customer.address = address ? StringToWString(address) : L"";
            
            const char* city = (const char*)sqlite3_column_text(stmt, 7);
            customer.city = city ? StringToWString(city) : L"";
            
            const char* taxNumber = (const char*)sqlite3_column_text(stmt, 8);
            customer.taxNumber = taxNumber ? StringToWString(taxNumber) : L"";
            
            customer.creditLimit = sqlite3_column_double(stmt, 9);
            customer.balance = sqlite3_column_double(stmt, 10);
            customer.isActive = sqlite3_column_int(stmt, 11) != 0;
            
            customers.push_back(customer);
        }
        sqlite3_finalize(stmt);
    }
    
    return customers;
}

bool Database::AddCustomer(const Customer& customer) {
    sqlite3_stmt* stmt;
    const char* query = R"(
        INSERT INTO customers (name, company, phone, mobile, email, address, city, tax_number, credit_limit) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    )";
    
    if (sqlite3_prepare_v2(db, query, -1, &stmt, nullptr) == SQLITE_OK) {
        sqlite3_bind_text(stmt, 1, WStringToString(customer.name).c_str(), -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 2, WStringToString(customer.company).c_str(), -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 3, WStringToString(customer.phone).c_str(), -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 4, WStringToString(customer.mobile).c_str(), -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 5, WStringToString(customer.email).c_str(), -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 6, WStringToString(customer.address).c_str(), -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 7, WStringToString(customer.city).c_str(), -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 8, WStringToString(customer.taxNumber).c_str(), -1, SQLITE_STATIC);
        sqlite3_bind_double(stmt, 9, customer.creditLimit);
        
        int result = sqlite3_step(stmt);
        sqlite3_finalize(stmt);
        
        return result == SQLITE_DONE;
    }
    
    return false;
}

int Database::GetCustomerCount() {
    sqlite3_stmt* stmt;
    const char* query = "SELECT COUNT(*) FROM customers WHERE is_active = 1";
    
    if (sqlite3_prepare_v2(db, query, -1, &stmt, nullptr) == SQLITE_OK) {
        if (sqlite3_step(stmt) == SQLITE_ROW) {
            int count = sqlite3_column_int(stmt, 0);
            sqlite3_finalize(stmt);
            return count;
        }
        sqlite3_finalize(stmt);
    }
    
    return 0;
}

int Database::GetProductCount() {
    sqlite3_stmt* stmt;
    const char* query = "SELECT COUNT(*) FROM products WHERE is_active = 1";
    
    if (sqlite3_prepare_v2(db, query, -1, &stmt, nullptr) == SQLITE_OK) {
        if (sqlite3_step(stmt) == SQLITE_ROW) {
            int count = sqlite3_column_int(stmt, 0);
            sqlite3_finalize(stmt);
            return count;
        }
        sqlite3_finalize(stmt);
    }
    
    return 0;
}

int Database::GetInvoiceCount() {
    sqlite3_stmt* stmt;
    const char* query = "SELECT COUNT(*) FROM invoices";
    
    if (sqlite3_prepare_v2(db, query, -1, &stmt, nullptr) == SQLITE_OK) {
        if (sqlite3_step(stmt) == SQLITE_ROW) {
            int count = sqlite3_column_int(stmt, 0);
            sqlite3_finalize(stmt);
            return count;
        }
        sqlite3_finalize(stmt);
    }
    
    return 0;
}

double Database::GetTotalSales() {
    sqlite3_stmt* stmt;
    const char* query = "SELECT COALESCE(SUM(total_amount), 0) FROM invoices WHERE strftime('%Y-%m', invoice_date) = strftime('%Y-%m', 'now')";
    
    if (sqlite3_prepare_v2(db, query, -1, &stmt, nullptr) == SQLITE_OK) {
        if (sqlite3_step(stmt) == SQLITE_ROW) {
            double total = sqlite3_column_double(stmt, 0);
            sqlite3_finalize(stmt);
            return total;
        }
        sqlite3_finalize(stmt);
    }
    
    return 0.0;
}

std::vector<Product> Database::GetProducts() {
    // Implementation similar to GetCustomers
    return std::vector<Product>();
}

std::vector<Invoice> Database::GetInvoices() {
    // Implementation similar to GetCustomers
    return std::vector<Invoice>();
}

bool Database::ExecuteSQL(const std::string& sql) {
    char* errorMessage = nullptr;
    int result = sqlite3_exec(db, sql.c_str(), nullptr, nullptr, &errorMessage);
    
    if (result != SQLITE_OK) {
        if (errorMessage) {
            sqlite3_free(errorMessage);
        }
        return false;
    }
    
    return true;
}

std::string Database::WStringToString(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();
    
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), nullptr, 0, nullptr, nullptr);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, nullptr, nullptr);
    return strTo;
}

std::wstring Database::StringToWString(const std::string& str) {
    if (str.empty()) return std::wstring();
    
    int size_needed = MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), nullptr, 0);
    std::wstring wstrTo(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), &wstrTo[0], size_needed);
    return wstrTo;
}

// Placeholder implementations for other methods
bool Database::UpdateCustomer(const Customer& customer) { return true; }
bool Database::DeleteCustomer(int id) { return true; }
bool Database::AddProduct(const Product& product) { return true; }
bool Database::UpdateProduct(const Product& product) { return true; }
bool Database::DeleteProduct(int id) { return true; }
bool Database::AddInvoice(const Invoice& invoice) { return true; }
bool Database::UpdateInvoice(const Invoice& invoice) { return true; }
bool Database::DeleteInvoice(int id) { return true; }
int Database::GetLowStockCount() { return 0; }
bool Database::BackupDatabase(const std::wstring& backupPath) { return true; }
bool Database::RestoreDatabase(const std::wstring& backupPath) { return true; }
