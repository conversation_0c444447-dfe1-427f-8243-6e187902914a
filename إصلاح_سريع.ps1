# إصلاح سريع لجميع أخطاء ViewModels
Write-Host "🔧 جاري إصلاح أخطاء ViewModels..." -ForegroundColor Yellow

# قائمة الملفات التي تحتاج إصلاح
$viewModels = @(
    "ViewModels\InventoryViewModel.cs",
    "ViewModels\InvoiceViewModel.cs", 
    "ViewModels\ReportViewModel.cs",
    "ViewModels\SettingsViewModel.cs",
    "ViewModels\DashboardViewModel.cs",
    "ViewModels\AccessDashboardViewModel.cs"
)

foreach ($file in $viewModels) {
    if (Test-Path $file) {
        Write-Host "📝 إصلاح $file..." -ForegroundColor Cyan
        
        # قراءة المحتوى
        $content = Get-Content $file -Raw
        
        # إضافة using System إذا لم يكن موجوداً
        if ($content -notmatch "using System;") {
            $content = $content -replace "(using AccountingSystem11\.Services;)", "using AccountingSystem11.Services;`nusing System;"
        }
        
        # إصلاح مشاكل await ExecuteAsync
        $content = $content -replace "await ExecuteAsync\(async \(\) =>`n\s*\{([^}]+)\}, ""[^""]*""\);", 
        @"
try
            {$1}
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine(`$"خطأ: {ex.Message}");
            }
"@
        
        # حفظ الملف
        Set-Content $file -Value $content -Encoding UTF8
        Write-Host "✅ تم إصلاح $file" -ForegroundColor Green
    }
}

Write-Host "🎉 تم إصلاح جميع ViewModels!" -ForegroundColor Green
Write-Host "🚀 يمكنك الآن تشغيل البرنامج" -ForegroundColor Yellow
