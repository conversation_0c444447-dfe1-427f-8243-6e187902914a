using AccountingSystem11.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AccountingSystem11.Services
{
    /// <summary>
    /// Interface for Egyptian E-Invoice system integration
    /// </summary>
    public interface IEInvoiceService
    {
        /// <summary>
        /// Submit invoice to Egyptian Tax Authority
        /// </summary>
        Task<EInvoiceSubmissionResult> SubmitInvoiceAsync(Invoice invoice);

        /// <summary>
        /// Cancel submitted invoice
        /// </summary>
        Task<EInvoiceSubmissionResult> CancelInvoiceAsync(Invoice invoice, string reason);

        /// <summary>
        /// Get invoice status from Tax Authority
        /// </summary>
        Task<EInvoiceStatusResult> GetInvoiceStatusAsync(string eInvoiceUuid);

        /// <summary>
        /// Validate invoice before submission
        /// </summary>
        Task<EInvoiceValidationResult> ValidateInvoiceAsync(Invoice invoice);

        /// <summary>
        /// Generate QR code for invoice
        /// </summary>
        Task<string> GenerateQRCodeAsync(Invoice invoice);

        /// <summary>
        /// Get company registration status
        /// </summary>
        Task<CompanyRegistrationStatus> GetCompanyRegistrationStatusAsync();

        /// <summary>
        /// Register company for E-Invoice system
        /// </summary>
        Task<CompanyRegistrationResult> RegisterCompanyAsync(CompanyRegistrationData data);

        /// <summary>
        /// Get E-Invoice settings
        /// </summary>
        Task<EInvoiceSettings> GetEInvoiceSettingsAsync();

        /// <summary>
        /// Update E-Invoice settings
        /// </summary>
        Task<bool> UpdateEInvoiceSettingsAsync(EInvoiceSettings settings);

        /// <summary>
        /// Test connection to Tax Authority API
        /// </summary>
        Task<bool> TestConnectionAsync();

        /// <summary>
        /// Get submission statistics
        /// </summary>
        Task<EInvoiceStatistics> GetSubmissionStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// Bulk submit invoices
        /// </summary>
        Task<List<EInvoiceSubmissionResult>> BulkSubmitInvoicesAsync(List<Invoice> invoices);

        /// <summary>
        /// Download invoice PDF from Tax Authority
        /// </summary>
        Task<byte[]> DownloadInvoicePdfAsync(string eInvoiceUuid);
    }

    /// <summary>
    /// E-Invoice submission result
    /// </summary>
    public class EInvoiceSubmissionResult
    {
        public bool IsSuccess { get; set; }
        public string EInvoiceUuid { get; set; }
        public string SubmissionId { get; set; }
        public DateTime SubmissionDate { get; set; }
        public string ErrorCode { get; set; }
        public string ErrorMessage { get; set; }
        public string ResponseData { get; set; }
        public List<string> Warnings { get; set; } = new List<string>();
    }

    /// <summary>
    /// E-Invoice status result
    /// </summary>
    public class EInvoiceStatusResult
    {
        public bool IsSuccess { get; set; }
        public EInvoiceStatus Status { get; set; }
        public DateTime StatusDate { get; set; }
        public string StatusMessage { get; set; }
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// E-Invoice validation result
    /// </summary>
    public class EInvoiceValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public string Message { get; set; }
    }

    /// <summary>
    /// Company registration status
    /// </summary>
    public class CompanyRegistrationStatus
    {
        public bool IsRegistered { get; set; }
        public string RegistrationNumber { get; set; }
        public DateTime? RegistrationDate { get; set; }
        public string Status { get; set; }
        public string Message { get; set; }
    }

    /// <summary>
    /// Company registration data
    /// </summary>
    public class CompanyRegistrationData
    {
        public string CompanyName { get; set; }
        public string TaxNumber { get; set; }
        public string CommercialRegNumber { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string Governorate { get; set; }
        public string PostalCode { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public BusinessType BusinessType { get; set; }
        public string ContactPersonName { get; set; }
        public string ContactPersonPhone { get; set; }
        public string ContactPersonEmail { get; set; }
    }

    /// <summary>
    /// Company registration result
    /// </summary>
    public class CompanyRegistrationResult
    {
        public bool IsSuccess { get; set; }
        public string RegistrationNumber { get; set; }
        public DateTime RegistrationDate { get; set; }
        public string ErrorMessage { get; set; }
        public string Message { get; set; }
    }

    /// <summary>
    /// E-Invoice system settings
    /// </summary>
    public class EInvoiceSettings
    {
        public int Id { get; set; }
        public bool IsEnabled { get; set; } = false;
        public string ApiUrl { get; set; }
        public string ClientId { get; set; }
        public string ClientSecret { get; set; }
        public string TaxNumber { get; set; }
        public string RegistrationNumber { get; set; }
        public bool IsTestMode { get; set; } = true;
        public int TimeoutSeconds { get; set; } = 30;
        public bool AutoSubmit { get; set; } = false;
        public bool GenerateQRCode { get; set; } = true;
        public DateTime? LastSyncDate { get; set; }
        public string CertificatePath { get; set; }
        public string CertificatePassword { get; set; }
    }

    /// <summary>
    /// E-Invoice submission statistics
    /// </summary>
    public class EInvoiceStatistics
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int TotalSubmitted { get; set; }
        public int SuccessfulSubmissions { get; set; }
        public int FailedSubmissions { get; set; }
        public int PendingSubmissions { get; set; }
        public int CancelledInvoices { get; set; }
        public decimal SuccessRate { get; set; }
        public List<EInvoiceStatisticsItem> DailyStats { get; set; } = new List<EInvoiceStatisticsItem>();
    }

    /// <summary>
    /// E-Invoice statistics item
    /// </summary>
    public class EInvoiceStatisticsItem
    {
        public DateTime Date { get; set; }
        public int Submitted { get; set; }
        public int Successful { get; set; }
        public int Failed { get; set; }
        public decimal Amount { get; set; }
    }
}
