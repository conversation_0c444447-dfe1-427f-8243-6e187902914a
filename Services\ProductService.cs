using AccountingSystem11.Data;
using AccountingSystem11.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AccountingSystem11.Services
{
    /// <summary>
    /// Product service implementation
    /// </summary>
    public class ProductService : IProductService
    {
        private readonly AccountingDbContext _context;

        public ProductService(AccountingDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Product>> GetAllProductsAsync()
        {
            return await _context.Products
                .Include(p => p.Category)
                .Include(p => p.Supplier)
                .Where(p => p.IsActive)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<Product> GetProductByIdAsync(int id)
        {
            return await _context.Products
                .Include(p => p.Category)
                .Include(p => p.Supplier)
                .Include(p => p.InvoiceItems)
                .Include(p => p.InventoryTransactions)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<Product> GetProductByCodeAsync(string code)
        {
            if (string.IsNullOrWhiteSpace(code))
                return null;

            return await _context.Products
                .Include(p => p.Category)
                .Include(p => p.Supplier)
                .FirstOrDefaultAsync(p => p.Code == code);
        }

        public async Task<Product> GetProductByBarcodeAsync(string barcode)
        {
            if (string.IsNullOrWhiteSpace(barcode))
                return null;

            return await _context.Products
                .Include(p => p.Category)
                .Include(p => p.Supplier)
                .FirstOrDefaultAsync(p => p.Barcode == barcode);
        }

        public async Task<Product> CreateProductAsync(Product product)
        {
            if (product == null)
                throw new ArgumentNullException(nameof(product));

            // Validate unique constraints
            var existingCode = await CodeExistsAsync(product.Code);
            if (existingCode)
                throw new InvalidOperationException("كود المنتج موجود بالفعل");

            if (!string.IsNullOrWhiteSpace(product.Barcode))
            {
                var existingBarcode = await BarcodeExistsAsync(product.Barcode);
                if (existingBarcode)
                    throw new InvalidOperationException("الباركود موجود بالفعل");
            }

            product.CreatedAt = DateTime.Now;
            _context.Products.Add(product);
            await _context.SaveChangesAsync();

            return product;
        }

        public async Task<Product> UpdateProductAsync(Product product)
        {
            if (product == null)
                throw new ArgumentNullException(nameof(product));

            var existingProduct = await _context.Products.FindAsync(product.Id);
            if (existingProduct == null)
                throw new InvalidOperationException("المنتج غير موجود");

            // Validate unique constraints
            var existingCode = await CodeExistsAsync(product.Code, product.Id);
            if (existingCode)
                throw new InvalidOperationException("كود المنتج موجود بالفعل");

            if (!string.IsNullOrWhiteSpace(product.Barcode))
            {
                var existingBarcode = await BarcodeExistsAsync(product.Barcode, product.Id);
                if (existingBarcode)
                    throw new InvalidOperationException("الباركود موجود بالفعل");
            }

            // Update properties
            existingProduct.Code = product.Code;
            existingProduct.Name = product.Name;
            existingProduct.Description = product.Description;
            existingProduct.CategoryId = product.CategoryId;
            existingProduct.Unit = product.Unit;
            existingProduct.PurchasePrice = product.PurchasePrice;
            existingProduct.SellingPrice = product.SellingPrice;
            existingProduct.MinSellingPrice = product.MinSellingPrice;
            existingProduct.WholesalePrice = product.WholesalePrice;
            existingProduct.MinStockLevel = product.MinStockLevel;
            existingProduct.MaxStockLevel = product.MaxStockLevel;
            existingProduct.ReorderPoint = product.ReorderPoint;
            existingProduct.Barcode = product.Barcode;
            existingProduct.ImagePath = product.ImagePath;
            existingProduct.TaxRate = product.TaxRate;
            existingProduct.IsTaxable = product.IsTaxable;
            existingProduct.IsService = product.IsService;
            existingProduct.IsActive = product.IsActive;
            existingProduct.Location = product.Location;
            existingProduct.SupplierId = product.SupplierId;
            existingProduct.Notes = product.Notes;
            existingProduct.Update();

            await _context.SaveChangesAsync();
            return existingProduct;
        }

        public async Task<bool> DeleteProductAsync(int id)
        {
            var product = await _context.Products.FindAsync(id);
            if (product == null)
                return false;

            // Check if product has invoice items
            var hasInvoiceItems = await _context.InvoiceItems.AnyAsync(ii => ii.ProductId == id);
            if (hasInvoiceItems)
                throw new InvalidOperationException("لا يمكن حذف المنتج لوجود فواتير مرتبطة به");

            product.Delete();
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ProductExistsAsync(int id)
        {
            return await _context.Products.AnyAsync(p => p.Id == id);
        }

        public async Task<bool> CodeExistsAsync(string code, int? excludeId = null)
        {
            if (string.IsNullOrWhiteSpace(code))
                return false;

            var query = _context.Products.Where(p => p.Code == code);
            
            if (excludeId.HasValue)
                query = query.Where(p => p.Id != excludeId.Value);

            return await query.AnyAsync();
        }

        public async Task<bool> BarcodeExistsAsync(string barcode, int? excludeId = null)
        {
            if (string.IsNullOrWhiteSpace(barcode))
                return false;

            var query = _context.Products.Where(p => p.Barcode == barcode);
            
            if (excludeId.HasValue)
                query = query.Where(p => p.Id != excludeId.Value);

            return await query.AnyAsync();
        }

        public async Task<IEnumerable<Product>> SearchProductsAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllProductsAsync();

            searchTerm = searchTerm.Trim().ToLower();

            return await _context.Products
                .Include(p => p.Category)
                .Include(p => p.Supplier)
                .Where(p => p.IsActive && 
                           (p.Name.ToLower().Contains(searchTerm) ||
                            p.Code.ToLower().Contains(searchTerm) ||
                            p.Description.ToLower().Contains(searchTerm) ||
                            p.Barcode.Contains(searchTerm)))
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> GetProductsByCategoryAsync(int categoryId)
        {
            return await _context.Products
                .Include(p => p.Category)
                .Include(p => p.Supplier)
                .Where(p => p.IsActive && p.CategoryId == categoryId)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> GetLowStockProductsAsync()
        {
            return await _context.Products
                .Include(p => p.Category)
                .Where(p => p.IsActive && p.StockQuantity <= p.MinStockLevel)
                .OrderBy(p => p.StockQuantity)
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> GetOutOfStockProductsAsync()
        {
            return await _context.Products
                .Include(p => p.Category)
                .Where(p => p.IsActive && p.StockQuantity <= 0)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<bool> UpdateStockQuantityAsync(int productId, decimal quantity)
        {
            var product = await _context.Products.FindAsync(productId);
            if (product == null)
                return false;

            product.StockQuantity = quantity;
            product.Update();
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> AdjustStockAsync(int productId, decimal adjustment, string reason)
        {
            var product = await _context.Products.FindAsync(productId);
            if (product == null)
                return false;

            product.StockQuantity += adjustment;
            product.Update();
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<Product>> GetTopSellingProductsAsync(int count = 10)
        {
            var topProductIds = await _context.InvoiceItems
                .Where(ii => ii.Invoice.InvoiceType == InvoiceType.Sales &&
                            ii.Invoice.Status == InvoiceStatus.Approved)
                .GroupBy(ii => ii.ProductId)
                .Select(g => new { ProductId = g.Key, TotalQuantity = g.Sum(ii => ii.Quantity) })
                .OrderByDescending(x => x.TotalQuantity)
                .Take(count)
                .Select(x => x.ProductId)
                .ToListAsync();

            return await _context.Products
                .Include(p => p.Category)
                .Where(p => topProductIds.Contains(p.Id))
                .ToListAsync();
        }

        public async Task<decimal> GetProductStockValueAsync()
        {
            return await _context.Products
                .Where(p => p.IsActive)
                .SumAsync(p => p.StockQuantity * p.PurchasePrice);
        }

        public async Task<string> GenerateNextProductCodeAsync()
        {
            var lastProduct = await _context.Products
                .OrderByDescending(p => p.Id)
                .FirstOrDefaultAsync();

            var nextNumber = (lastProduct?.Id ?? 0) + 1;
            return $"PRD{nextNumber:D6}";
        }
    }
}
