@echo off
title اختبار فوري - نظام المحاسبة 11
color 0E
echo.
echo ========================================================
echo           اختبار فوري - نظام المحاسبة 11
echo ========================================================
echo.

echo 🔍 جاري فحص النظام...
echo.

echo [1] فحص .NET...
dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ .NET مثبت - الإصدار:
    dotnet --version
) else (
    echo ❌ .NET غير مثبت
    echo.
    echo 🔧 الحل السريع:
    echo 1. اذهب إلى: https://dotnet.microsoft.com/download/dotnet/6.0
    echo 2. حمل ".NET Desktop Runtime 6.0.x"
    echo 3. ثبته وأعد تشغيل الجهاز
    echo 4. أعد تشغيل هذا الملف
    echo.
    start https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
)

echo.
echo [2] اختبار البرنامج البسيط...
if exist "البرنامج_البسيط.csproj" (
    echo ✅ البرنامج البسيط موجود
    echo 🚀 جاري التشغيل...
    echo.
    echo 💡 إذا لم تظهر النافذة خلال 10 ثوانٍ:
    echo    • تحقق من Task Manager
    echo    • قد تكون النافذة مخفية خلف نوافذ أخرى
    echo    • اضغط Alt+Tab للتنقل بين النوافذ
    echo.
    
    timeout /t 3 /nobreak >nul
    dotnet run --project البرنامج_البسيط.csproj
    
    echo.
    echo ✅ تم إغلاق البرنامج البسيط
) else (
    echo ❌ البرنامج البسيط غير موجود
    echo 🔧 جاري إنشاؤه...
    
    REM إنشاء برنامج بسيط جداً
    echo using System; > test_simple.cs
    echo using System.Windows; >> test_simple.cs
    echo using System.Windows.Controls; >> test_simple.cs
    echo. >> test_simple.cs
    echo namespace Test >> test_simple.cs
    echo { >> test_simple.cs
    echo     class Program >> test_simple.cs
    echo     { >> test_simple.cs
    echo         [System.STAThread] >> test_simple.cs
    echo         static void Main() >> test_simple.cs
    echo         { >> test_simple.cs
    echo             var app = new Application(); >> test_simple.cs
    echo             var window = new Window(); >> test_simple.cs
    echo             window.Title = "نظام المحاسبة 11 - اختبار"; >> test_simple.cs
    echo             window.Width = 600; >> test_simple.cs
    echo             window.Height = 400; >> test_simple.cs
    echo             window.WindowStartupLocation = WindowStartupLocation.CenterScreen; >> test_simple.cs
    echo             var text = new TextBlock(); >> test_simple.cs
    echo             text.Text = "🎉 البرنامج يعمل!\n\nنظام المحاسبة 11\nاختبار ناجح"; >> test_simple.cs
    echo             text.FontSize = 20; >> test_simple.cs
    echo             text.HorizontalAlignment = HorizontalAlignment.Center; >> test_simple.cs
    echo             text.VerticalAlignment = VerticalAlignment.Center; >> test_simple.cs
    echo             text.TextAlignment = TextAlignment.Center; >> test_simple.cs
    echo             window.Content = text; >> test_simple.cs
    echo             app.Run(window); >> test_simple.cs
    echo         } >> test_simple.cs
    echo     } >> test_simple.cs
    echo } >> test_simple.cs
    
    REM إنشاء مشروع بسيط
    echo ^<Project Sdk="Microsoft.NET.Sdk"^> > test_simple.csproj
    echo   ^<PropertyGroup^> >> test_simple.csproj
    echo     ^<OutputType^>WinExe^</OutputType^> >> test_simple.csproj
    echo     ^<TargetFramework^>net6.0-windows^</TargetFramework^> >> test_simple.csproj
    echo     ^<UseWPF^>true^</UseWPF^> >> test_simple.csproj
    echo   ^</PropertyGroup^> >> test_simple.csproj
    echo ^</Project^> >> test_simple.csproj
    
    echo ✅ تم إنشاء برنامج اختبار بسيط
    echo 🚀 جاري التشغيل...
    echo.
    
    dotnet run --project test_simple.csproj
    
    echo.
    echo ✅ تم إغلاق برنامج الاختبار
)

echo.
echo [3] اختبار البرنامج الأصلي...
if exist "AccountingSystem11.csproj" (
    echo ⚠️  البرنامج الأصلي موجود لكن قد يحتوي على أخطاء
    echo.
    set /p choice="هل تريد محاولة تشغيل البرنامج الأصلي؟ (y/n): "
    if /i "%choice%"=="y" (
        echo 🔧 جاري المحاولة...
        dotnet run --project AccountingSystem11.csproj
    )
) else (
    echo ❌ البرنامج الأصلي غير موجود
)

echo.
echo ========================================================
echo 📋 ملخص النتائج
echo ========================================================
echo.

dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ .NET: يعمل
) else (
    echo ❌ .NET: غير مثبت
)

if exist "test_simple.csproj" (
    echo ✅ برنامج الاختبار: تم إنشاؤه
) else if exist "البرنامج_البسيط.csproj" (
    echo ✅ البرنامج البسيط: موجود
) else (
    echo ❌ لا يوجد برنامج للاختبار
)

echo.
echo 💡 التوصيات:
echo.

if exist "test_simple.csproj" (
    echo 🎯 إذا عمل برنامج الاختبار:
    echo    • النظام سليم والمشكلة في البرنامج الأصلي
    echo    • استخدم البرنامج البسيط بدلاً من المعقد
    echo.
    echo 🎯 إذا لم يعمل برنامج الاختبار:
    echo    • المشكلة في النظام أو .NET
    echo    • ثبت .NET 6.0 Desktop Runtime
    echo    • أعد تشغيل الجهاز
)

echo.
echo 📞 للمساعدة الإضافية:
echo    • أخبرني ماذا حدث بالضبط
echo    • هل ظهرت نافذة؟
echo    • هل ظهرت رسالة خطأ؟
echo    • ما نوع نظام التشغيل؟

echo.
pause
