[Setup]
; معلومات التطبيق
AppName=نظام المحاسبة 11
AppVersion=1.0.0
AppPublisher=شركة 11 للبرمجيات
AppPublisherURL=https://www.system11.com
AppSupportURL=https://support.system11.com
AppUpdatesURL=https://updates.system11.com
AppCopyright=© 2024 شركة 11 للبرمجيات. جميع الحقوق محفوظة.

; معلومات التثبيت
DefaultDirName={autopf}\AccountingSystem11
DefaultGroupName=نظام المحاسبة 11
AllowNoIcons=yes
LicenseFile=License.txt
InfoBeforeFile=ReadMe.txt
InfoAfterFile=AfterInstall.txt
OutputDir=Output
OutputBaseFilename=AccountingSystem11_Setup_v1.0.0
SetupIconFile=icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern

; متطلبات النظام
MinVersion=6.1sp1
ArchitecturesAllowed=x86 x64
ArchitecturesInstallIn64BitMode=x64

; إعدادات التثبيت
PrivilegesRequired=admin
DisableProgramGroupPage=yes
DisableReadyPage=no
DisableFinishedPage=no
DisableWelcomePage=no

; إعدادات إلغاء التثبيت
UninstallDisplayIcon={app}\AccountingSystem11.exe
UninstallDisplayName=نظام المحاسبة 11
CreateUninstallRegKey=yes

; إعدادات اللغة
ShowLanguageDialog=auto

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1
Name: "associatefiles"; Description: "ربط ملفات البيانات بالبرنامج"; GroupDescription: "إعدادات إضافية"
Name: "autostart"; Description: "تشغيل البرنامج مع بدء Windows"; GroupDescription: "إعدادات إضافية"; Flags: unchecked

[Files]
; الملفات الأساسية
Source: "..\bin\Release\net6.0-windows\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "License.txt"; DestDir: "{app}"; Flags: ignoreversion
Source: "ReadMe.txt"; DestDir: "{app}"; Flags: ignoreversion
Source: "icon.ico"; DestDir: "{app}"; Flags: ignoreversion

; ملفات قاعدة البيانات
Source: "Database\*"; DestDir: "{app}\Database"; Flags: ignoreversion recursesubdirs createallsubdirs

; ملفات التقارير
Source: "Reports\*"; DestDir: "{app}\Reports"; Flags: ignoreversion recursesubdirs createallsubdirs

; ملفات المساعدة
Source: "Help\*"; DestDir: "{app}\Help"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{group}\نظام المحاسبة 11"; Filename: "{app}\AccountingSystem11.exe"; IconFilename: "{app}\icon.ico"
Name: "{group}\دليل المستخدم"; Filename: "{app}\Help\UserGuide.pdf"
Name: "{group}\الدعم الفني"; Filename: "https://support.system11.com"
Name: "{group}\{cm:UninstallProgram,نظام المحاسبة 11}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\نظام المحاسبة 11"; Filename: "{app}\AccountingSystem11.exe"; IconFilename: "{app}\icon.ico"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\نظام المحاسبة 11"; Filename: "{app}\AccountingSystem11.exe"; IconFilename: "{app}\icon.ico"; Tasks: quicklaunchicon

[Registry]
; تسجيل البرنامج في النظام
Root: HKLM; Subkey: "SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\AccountingSystem11"; ValueType: string; ValueName: "DisplayName"; ValueData: "نظام المحاسبة 11"
Root: HKLM; Subkey: "SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\AccountingSystem11"; ValueType: string; ValueName: "DisplayVersion"; ValueData: "1.0.0"
Root: HKLM; Subkey: "SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\AccountingSystem11"; ValueType: string; ValueName: "Publisher"; ValueData: "شركة 11 للبرمجيات"
Root: HKLM; Subkey: "SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\AccountingSystem11"; ValueType: string; ValueName: "URLInfoAbout"; ValueData: "https://www.system11.com"
Root: HKLM; Subkey: "SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\AccountingSystem11"; ValueType: string; ValueName: "URLUpdateInfo"; ValueData: "https://updates.system11.com"

; ربط ملفات البيانات
Root: HKCR; Subkey: ".as11"; ValueType: string; ValueName: ""; ValueData: "AccountingSystem11.DataFile"; Flags: uninsdeletevalue; Tasks: associatefiles
Root: HKCR; Subkey: "AccountingSystem11.DataFile"; ValueType: string; ValueName: ""; ValueData: "ملف بيانات نظام المحاسبة 11"; Flags: uninsdeletekey; Tasks: associatefiles
Root: HKCR; Subkey: "AccountingSystem11.DataFile\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\AccountingSystem11.exe,0"; Tasks: associatefiles
Root: HKCR; Subkey: "AccountingSystem11.DataFile\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\AccountingSystem11.exe"" ""%1"""; Tasks: associatefiles

; التشغيل التلقائي
Root: HKCU; Subkey: "SOFTWARE\Microsoft\Windows\CurrentVersion\Run"; ValueType: string; ValueName: "AccountingSystem11"; ValueData: """{app}\AccountingSystem11.exe"" /autostart"; Tasks: autostart

[Run]
; تشغيل البرنامج بعد التثبيت
Filename: "{app}\AccountingSystem11.exe"; Description: "{cm:LaunchProgram,نظام المحاسبة 11}"; Flags: nowait postinstall skipifsilent

; فتح دليل المستخدم
Filename: "{app}\Help\UserGuide.pdf"; Description: "فتح دليل المستخدم"; Flags: nowait postinstall skipifsilent unchecked shellexec

; زيارة الموقع
Filename: "https://www.system11.com/welcome"; Description: "زيارة موقع الشركة"; Flags: nowait postinstall skipifsilent unchecked shellexec

[UninstallRun]
; تنظيف البيانات عند إلغاء التثبيت
Filename: "{app}\AccountingSystem11.exe"; Parameters: "/uninstall"; Flags: waituntilterminated

[Code]
var
  DotNetPage: TInputOptionWizardPage;
  SqlServerPage: TInputOptionWizardPage;

procedure InitializeWizard;
begin
  // صفحة اختيار .NET Framework
  DotNetPage := CreateInputOptionPage(wpSelectTasks,
    'متطلبات النظام', 'اختيار إصدار .NET Framework',
    'يحتاج البرنامج إلى .NET Framework للعمل. اختر الإصدار المناسب:',
    True, False);
  DotNetPage.Add('.NET 6.0 (مُوصى به)');
  DotNetPage.Add('.NET Framework 4.8 (للأنظمة القديمة)');
  DotNetPage.Values[0] := True;

  // صفحة اختيار قاعدة البيانات
  SqlServerPage := CreateInputOptionPage(DotNetPage.ID,
    'إعداد قاعدة البيانات', 'اختيار نوع قاعدة البيانات',
    'اختر نوع قاعدة البيانات التي تريد استخدامها:',
    True, False);
  SqlServerPage.Add('SQL Server LocalDB (مُوصى به)');
  SqlServerPage.Add('SQL Server Express');
  SqlServerPage.Add('قاعدة بيانات محلية مبسطة');
  SqlServerPage.Values[0] := True;
end;

function NextButtonClick(CurPageID: Integer): Boolean;
begin
  Result := True;
  
  if CurPageID = wpReady then
  begin
    // التحقق من متطلبات النظام
    if not CheckDotNetInstalled() then
    begin
      if MsgBox('لم يتم العثور على .NET Framework. هل تريد تحميله وتثبيته؟',
                mbConfirmation, MB_YESNO) = IDYES then
      begin
        DownloadAndInstallDotNet();
      end;
    end;
  end;
end;

function CheckDotNetInstalled(): Boolean;
var
  ResultCode: Integer;
begin
  Result := Exec('dotnet', '--version', '', SW_HIDE, ewWaitUntilTerminated, ResultCode) and (ResultCode = 0);
end;

procedure DownloadAndInstallDotNet();
var
  ResultCode: Integer;
begin
  if MsgBox('سيتم فتح موقع Microsoft لتحميل .NET Framework. هل تريد المتابعة؟',
            mbInformation, MB_YESNO) = IDYES then
  begin
    ShellExec('open', 'https://dotnet.microsoft.com/download/dotnet/6.0', '', '', SW_SHOWNORMAL, ewNoWait, ResultCode);
  end;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  if CurStep = ssPostInstall then
  begin
    // إنشاء مجلدات البيانات
    CreateDir(ExpandConstant('{commonappdata}\AccountingSystem11'));
    CreateDir(ExpandConstant('{commonappdata}\AccountingSystem11\Database'));
    CreateDir(ExpandConstant('{commonappdata}\AccountingSystem11\Backups'));
    CreateDir(ExpandConstant('{commonappdata}\AccountingSystem11\Reports'));
    CreateDir(ExpandConstant('{commonappdata}\AccountingSystem11\Logs'));
    
    // تعيين الصلاحيات
    SetPermissions();
  end;
end;

procedure SetPermissions();
var
  ResultCode: Integer;
begin
  // إعطاء صلاحيات كاملة لمجلد البيانات
  Exec('icacls', ExpandConstant('"{commonappdata}\AccountingSystem11" /grant Users:(OI)(CI)F'), '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
end;

function ShouldSkipPage(PageID: Integer): Boolean;
begin
  Result := False;
  
  // تخطي صفحات معينة حسب الحاجة
  if (PageID = DotNetPage.ID) and CheckDotNetInstalled() then
    Result := True;
end;

procedure CurUninstallStepChanged(CurUninstallStep: TUninstallStep);
begin
  if CurUninstallStep = usPostUninstall then
  begin
    // سؤال المستخدم عن حذف البيانات
    if MsgBox('هل تريد حذف جميع بيانات البرنامج (قواعد البيانات والإعدادات)؟' + #13#10 +
              'تحذير: هذا الإجراء لا يمكن التراجع عنه!',
              mbConfirmation, MB_YESNO or MB_DEFBUTTON2) = IDYES then
    begin
      DelTree(ExpandConstant('{commonappdata}\AccountingSystem11'), True, True, True);
    end;
  end;
end;
