@echo off
title إنشاء ملف EXE - نظام المحاسبة 11
color 0B
echo.
echo ========================================================
echo        إنشاء ملف EXE - نظام المحاسبة 11
echo ========================================================
echo.

echo 🔨 جاري إنشاء ملف .exe...
echo.

REM إنشاء مجلد الإخراج
if not exist "Release" mkdir "Release"

echo [1] تنظيف المشروع...
dotnet clean AccountingSystem11.csproj --configuration Release >nul 2>&1
echo ✅ تم التنظيف

echo.
echo [2] استعادة الحزم...
dotnet restore AccountingSystem11.csproj >nul 2>&1
echo ✅ تم استعادة الحزم

echo.
echo [3] بناء المشروع...
dotnet build AccountingSystem11.csproj --configuration Release --no-restore >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ فشل البناء
    echo جرب إصلاح الأخطاء أولاً
    pause
    exit /b 1
)
echo ✅ تم البناء بنجاح

echo.
echo [4] نشر التطبيق كـ Self-Contained...
dotnet publish AccountingSystem11.csproj ^
    --configuration Release ^
    --runtime win-x64 ^
    --self-contained true ^
    --output "Release\AccountingSystem11" ^
    --verbosity minimal

if %errorlevel% equ 0 (
    echo.
    echo ========================================================
    echo 🎉 تم إنشاء ملف .exe بنجاح!
    echo ========================================================
    echo.
    echo 📁 مكان الملف: Release\AccountingSystem11\
    echo 📄 اسم الملف: AccountingSystem11.exe
    echo.
    echo 💾 حجم التطبيق: ~150 MB (يحتوي على جميع المتطلبات)
    echo.
    echo ✅ المميزات:
    echo    • لا يحتاج تثبيت .NET
    echo    • يعمل على أي جهاز Windows
    echo    • ملف واحد مستقل
    echo    • سهل التوزيع
    echo.
    
    REM نسخ ملفات إضافية
    if not exist "Release\AccountingSystem11\Data" mkdir "Release\AccountingSystem11\Data"
    
    echo 📋 نسخ ملفات التشغيل...
    copy "تشغيل_بسيط.bat" "Release\AccountingSystem11\" >nul 2>&1
    copy "حل_المشاكل.md" "Release\AccountingSystem11\" >nul 2>&1
    copy "README.md" "Release\AccountingSystem11\" >nul 2>&1
    
    REM إنشاء ملف تشغيل للـ exe
    echo @echo off > "Release\AccountingSystem11\تشغيل.bat"
    echo title نظام المحاسبة 11 >> "Release\AccountingSystem11\تشغيل.bat"
    echo echo 🚀 جاري تشغيل نظام المحاسبة 11... >> "Release\AccountingSystem11\تشغيل.bat"
    echo echo. >> "Release\AccountingSystem11\تشغيل.bat"
    echo echo 🔐 بيانات تسجيل الدخول: >> "Release\AccountingSystem11\تشغيل.bat"
    echo echo 👤 اسم المستخدم: admin >> "Release\AccountingSystem11\تشغيل.bat"
    echo echo 🔑 كلمة المرور: admin123 >> "Release\AccountingSystem11\تشغيل.bat"
    echo echo. >> "Release\AccountingSystem11\تشغيل.bat"
    echo AccountingSystem11.exe >> "Release\AccountingSystem11\تشغيل.bat"
    echo pause >> "Release\AccountingSystem11\تشغيل.bat"
    
    echo ✅ تم نسخ الملفات الإضافية
    echo.
    
    set /p choice="هل تريد فتح مجلد الملف؟ (y/n): "
    if /i "%choice%"=="y" (
        explorer "Release\AccountingSystem11"
    )
    
    echo.
    set /p choice2="هل تريد تشغيل البرنامج للاختبار؟ (y/n): "
    if /i "%choice2%"=="y" (
        cd "Release\AccountingSystem11"
        AccountingSystem11.exe
    )
    
) else (
    echo.
    echo ❌ فشل إنشاء ملف .exe
    echo.
    echo 🔧 الحلول المقترحة:
    echo 1. تأكد من تثبيت .NET 6.0 SDK
    echo 2. شغل الملف كـ Administrator
    echo 3. تأكد من وجود مساحة كافية (500 MB)
    echo 4. أغلق برامج مضاد الفيروسات مؤقتاً
)

echo.
pause
