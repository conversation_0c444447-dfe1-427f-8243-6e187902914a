<Window x:Class="AccountingSystem11.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تسجيل الدخول - نظام المحاسبة 11" 
        Height="600" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{materialDesign:MaterialDesignFont}">

    <Grid>
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#FF2196F3" Offset="0"/>
                <GradientStop Color="#FF1976D2" Offset="1"/>
            </LinearGradientBrush>
        </Grid.Background>

        <!-- بطاقة تسجيل الدخول -->
        <materialDesign:Card Margin="40" Padding="32" 
                           VerticalAlignment="Center"
                           materialDesign:ShadowAssist.ShadowDepth="Depth3">
            <StackPanel>
                <!-- شعار النظام -->
                <materialDesign:PackIcon Kind="AccountBalance" 
                                       Width="64" Height="64"
                                       HorizontalAlignment="Center"
                                       Foreground="{DynamicResource PrimaryHueMidBrush}"
                                       Margin="0,0,0,16"/>

                <!-- عنوان النظام -->
                <TextBlock Text="نظام المحاسبة 11" 
                         Style="{DynamicResource MaterialDesignHeadline4TextBlock}"
                         HorizontalAlignment="Center"
                         Margin="0,0,0,8"/>

                <TextBlock Text="تسجيل الدخول إلى حسابك" 
                         Style="{DynamicResource MaterialDesignBody1TextBlock}"
                         HorizontalAlignment="Center"
                         Opacity="0.7"
                         Margin="0,0,0,32"/>

                <!-- حقل اسم المستخدم -->
                <TextBox x:Name="UsernameTextBox"
                        materialDesign:HintAssist.Hint="اسم المستخدم"
                        materialDesign:HintAssist.IsFloating="True"
                        materialDesign:TextFieldAssist.HasClearButton="True"
                        materialDesign:TextFieldAssist.PrefixText="👤"
                        Style="{DynamicResource MaterialDesignOutlinedTextBox}"
                        Margin="0,0,0,16"
                        FontSize="14"
                        FlowDirection="RightToLeft"/>

                <!-- حقل كلمة المرور -->
                <PasswordBox x:Name="PasswordBox"
                           materialDesign:HintAssist.Hint="كلمة المرور"
                           materialDesign:HintAssist.IsFloating="True"
                           materialDesign:TextFieldAssist.PrefixText="🔑"
                           Style="{DynamicResource MaterialDesignOutlinedPasswordBox}"
                           Margin="0,0,0,16"
                           FontSize="14"
                           FlowDirection="RightToLeft"/>

                <!-- خيار تذكرني -->
                <CheckBox x:Name="RememberMeCheckBox"
                        Content="تذكرني"
                        Style="{DynamicResource MaterialDesignCheckBox}"
                        Margin="0,0,0,24"
                        FlowDirection="RightToLeft"/>

                <!-- زر تسجيل الدخول -->
                <Button x:Name="LoginButton"
                      Content="تسجيل الدخول"
                      Style="{DynamicResource MaterialDesignRaisedButton}"
                      materialDesign:ButtonAssist.CornerRadius="20"
                      Height="40"
                      FontSize="14"
                      FontWeight="Bold"
                      Click="LoginButton_Click"
                      Margin="0,0,0,16"/>

                <!-- رابط إنشاء حساب جديد -->
                <Button x:Name="CreateAccountButton"
                      Content="إنشاء حساب جديد"
                      Style="{DynamicResource MaterialDesignFlatButton}"
                      Foreground="{DynamicResource PrimaryHueMidBrush}"
                      FontSize="12"
                      Click="CreateAccountButton_Click"
                      Margin="0,0,0,8"/>

                <!-- رابط نسيت كلمة المرور -->
                <Button x:Name="ForgotPasswordButton"
                      Content="نسيت كلمة المرور؟"
                      Style="{DynamicResource MaterialDesignFlatButton}"
                      Foreground="{DynamicResource PrimaryHueMidBrush}"
                      FontSize="12"
                      Click="ForgotPasswordButton_Click"
                      Margin="0,0,0,16"/>

                <!-- معلومات الحساب الافتراضي -->
                <Border Background="{DynamicResource MaterialDesignSelection}"
                      CornerRadius="8"
                      Padding="12"
                      Margin="0,8,0,0">
                    <StackPanel>
                        <TextBlock Text="🔐 الحساب الافتراضي:"
                                 Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                 FontWeight="Bold"
                                 Margin="0,0,0,4"/>
                        <TextBlock Text="👤 اسم المستخدم: admin"
                                 Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                 Margin="0,0,0,2"/>
                        <TextBlock Text="🔑 كلمة المرور: admin123"
                                 Style="{DynamicResource MaterialDesignCaptionTextBlock}"/>
                    </StackPanel>
                </Border>

                <!-- رسالة الخطأ -->
                <TextBlock x:Name="ErrorMessageTextBlock"
                         Style="{DynamicResource MaterialDesignBody2TextBlock}"
                         Foreground="Red"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"
                         Visibility="Collapsed"/>

                <!-- شريط التحميل -->
                <ProgressBar x:Name="LoadingProgressBar"
                           Style="{DynamicResource MaterialDesignLinearProgressBar}"
                           IsIndeterminate="True"
                           Margin="0,16,0,0"
                           Visibility="Collapsed"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- معلومات النسخة -->
        <TextBlock Text="نظام المحاسبة 11 - الإصدار 1.0.0"
                 Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                 Foreground="White"
                 Opacity="0.7"
                 HorizontalAlignment="Center"
                 VerticalAlignment="Bottom"
                 Margin="0,0,0,16"/>
    </Grid>
</Window>
