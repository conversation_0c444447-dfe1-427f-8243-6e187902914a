using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AccountingSystem11.Models
{
    /// <summary>
    /// Supplier entity for managing vendors and suppliers
    /// </summary>
    public class Supplier : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; }

        [MaxLength(100)]
        public string CompanyName { get; set; }

        [MaxLength(15)]
        public string Phone { get; set; }

        [MaxLength(15)]
        public string Mobile { get; set; }

        [MaxLength(100)]
        public string Email { get; set; }

        [MaxLength(200)]
        public string Address { get; set; }

        [MaxLength(50)]
        public string City { get; set; }

        [MaxLength(50)]
        public string Governorate { get; set; }

        [MaxLength(10)]
        public string PostalCode { get; set; }

        /// <summary>
        /// Tax Registration Number
        /// </summary>
        [MaxLength(20)]
        public string TaxNumber { get; set; }

        /// <summary>
        /// Commercial Registration Number
        /// </summary>
        [MaxLength(20)]
        public string CommercialRegNumber { get; set; }

        /// <summary>
        /// Contact person name
        /// </summary>
        [MaxLength(100)]
        public string ContactPerson { get; set; }

        /// <summary>
        /// Contact person phone
        /// </summary>
        [MaxLength(15)]
        public string ContactPhone { get; set; }

        /// <summary>
        /// Payment terms in days
        /// </summary>
        public int PaymentTerms { get; set; } = 30;

        /// <summary>
        /// Credit limit
        /// </summary>
        public decimal CreditLimit { get; set; } = 0;

        /// <summary>
        /// Current balance
        /// </summary>
        public decimal Balance { get; set; } = 0;

        /// <summary>
        /// Notes about the supplier
        /// </summary>
        [MaxLength(500)]
        public string Notes { get; set; }

        /// <summary>
        /// Whether supplier is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
        public virtual ICollection<SupplierTransaction> Transactions { get; set; } = new List<SupplierTransaction>();
    }
}
