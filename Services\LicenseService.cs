using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Microsoft.Win32;
using System.Management;
using System.Linq;

namespace AccountingSystem11.Services
{
    public class LicenseService
    {
        private readonly string _licenseFilePath;
        private readonly string _registryPath = @"SOFTWARE\AccountingSystem11";
        private readonly string _encryptionKey = "AS11-2024-SECURE-KEY-FOR-LICENSING";

        public LicenseService()
        {
            _licenseFilePath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData),
                "AccountingSystem11", "license.dat");
            
            Directory.CreateDirectory(Path.GetDirectoryName(_licenseFilePath));
        }

        public LicenseInfo GetLicenseInfo()
        {
            try
            {
                // التحقق من الترخيص في الملف
                if (File.Exists(_licenseFilePath))
                {
                    var encryptedData = File.ReadAllText(_licenseFilePath);
                    var decryptedData = DecryptString(encryptedData);
                    var license = JsonSerializer.Deserialize<LicenseInfo>(decryptedData);
                    
                    if (ValidateLicense(license))
                    {
                        return license;
                    }
                }

                // التحقق من الترخيص في الريجستري
                var registryLicense = GetLicenseFromRegistry();
                if (registryLicense != null && ValidateLicense(registryLicense))
                {
                    return registryLicense;
                }

                // إرجاع ترخيص تجريبي
                return GetTrialLicense();
            }
            catch
            {
                return GetTrialLicense();
            }
        }

        public bool ActivateLicense(string licenseKey, string customerName, string customerEmail)
        {
            try
            {
                var license = GenerateLicenseFromKey(licenseKey, customerName, customerEmail);
                if (license == null) return false;

                // حفظ الترخيص في الملف
                var jsonData = JsonSerializer.Serialize(license, new JsonSerializerOptions { WriteIndented = true });
                var encryptedData = EncryptString(jsonData);
                File.WriteAllText(_licenseFilePath, encryptedData);

                // حفظ الترخيص في الريجستري
                SaveLicenseToRegistry(license);

                return true;
            }
            catch
            {
                return false;
            }
        }

        public bool IsLicenseValid()
        {
            var license = GetLicenseInfo();
            return ValidateLicense(license);
        }

        public int GetRemainingTrialDays()
        {
            var license = GetLicenseInfo();
            if (license.LicenseType != LicenseType.Trial) return -1;

            var remainingDays = (license.ExpiryDate - DateTime.Now).Days;
            return Math.Max(0, remainingDays);
        }

        private bool ValidateLicense(LicenseInfo license)
        {
            if (license == null) return false;
            if (DateTime.Now > license.ExpiryDate) return false;
            if (!ValidateHardwareFingerprint(license.HardwareFingerprint)) return false;
            if (!ValidateLicenseSignature(license)) return false;

            return true;
        }

        private LicenseInfo GenerateLicenseFromKey(string licenseKey, string customerName, string customerEmail)
        {
            // تحقق من صحة مفتاح الترخيص
            if (!IsValidLicenseKey(licenseKey)) return null;

            var licenseType = GetLicenseTypeFromKey(licenseKey);
            var expiryDate = GetExpiryDateFromKey(licenseKey, licenseType);

            var license = new LicenseInfo
            {
                LicenseKey = licenseKey,
                CustomerName = customerName,
                CustomerEmail = customerEmail,
                LicenseType = licenseType,
                IssueDate = DateTime.Now,
                ExpiryDate = expiryDate,
                HardwareFingerprint = GetHardwareFingerprint(),
                MaxUsers = GetMaxUsersFromKey(licenseKey),
                Features = GetFeaturesFromKey(licenseKey)
            };

            license.Signature = GenerateLicenseSignature(license);
            return license;
        }

        private LicenseInfo GetTrialLicense()
        {
            var trialStartDate = GetTrialStartDate();
            var trialDays = 30; // فترة تجريبية 30 يوم

            return new LicenseInfo
            {
                LicenseKey = "TRIAL-" + GetHardwareFingerprint().Substring(0, 8),
                CustomerName = "مستخدم تجريبي",
                CustomerEmail = "<EMAIL>",
                LicenseType = LicenseType.Trial,
                IssueDate = trialStartDate,
                ExpiryDate = trialStartDate.AddDays(trialDays),
                HardwareFingerprint = GetHardwareFingerprint(),
                MaxUsers = 1,
                Features = LicenseFeatures.Basic
            };
        }

        private DateTime GetTrialStartDate()
        {
            try
            {
                using var key = Registry.CurrentUser.CreateSubKey(_registryPath);
                var storedDate = key.GetValue("TrialStartDate") as string;
                
                if (DateTime.TryParse(storedDate, out var date))
                {
                    return date;
                }
                else
                {
                    var now = DateTime.Now;
                    key.SetValue("TrialStartDate", now.ToString());
                    return now;
                }
            }
            catch
            {
                return DateTime.Now;
            }
        }

        private string GetHardwareFingerprint()
        {
            try
            {
                var fingerprint = new StringBuilder();
                
                // معرف المعالج
                using (var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor"))
                {
                    foreach (var obj in searcher.Get())
                    {
                        fingerprint.Append(obj["ProcessorId"]?.ToString());
                        break;
                    }
                }

                // معرف اللوحة الأم
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard"))
                {
                    foreach (var obj in searcher.Get())
                    {
                        fingerprint.Append(obj["SerialNumber"]?.ToString());
                        break;
                    }
                }

                // معرف القرص الصلب
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_DiskDrive"))
                {
                    foreach (var obj in searcher.Get())
                    {
                        fingerprint.Append(obj["SerialNumber"]?.ToString());
                        break;
                    }
                }

                return ComputeHash(fingerprint.ToString());
            }
            catch
            {
                return ComputeHash(Environment.MachineName + Environment.UserName);
            }
        }

        private bool ValidateHardwareFingerprint(string storedFingerprint)
        {
            var currentFingerprint = GetHardwareFingerprint();
            return currentFingerprint.Equals(storedFingerprint, StringComparison.OrdinalIgnoreCase);
        }

        private string GenerateLicenseSignature(LicenseInfo license)
        {
            var data = $"{license.LicenseKey}{license.CustomerEmail}{license.ExpiryDate:yyyyMMdd}{license.HardwareFingerprint}";
            return ComputeHash(data + _encryptionKey);
        }

        private bool ValidateLicenseSignature(LicenseInfo license)
        {
            var expectedSignature = GenerateLicenseSignature(license);
            return expectedSignature.Equals(license.Signature, StringComparison.OrdinalIgnoreCase);
        }

        private string ComputeHash(string input)
        {
            using var sha256 = SHA256.Create();
            var bytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
            return Convert.ToBase64String(bytes);
        }

        private string EncryptString(string plainText)
        {
            var key = Encoding.UTF8.GetBytes(_encryptionKey.Substring(0, 32));
            var iv = Encoding.UTF8.GetBytes(_encryptionKey.Substring(0, 16));

            using var aes = Aes.Create();
            aes.Key = key;
            aes.IV = iv;

            using var encryptor = aes.CreateEncryptor();
            var plainBytes = Encoding.UTF8.GetBytes(plainText);
            var encryptedBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);
            
            return Convert.ToBase64String(encryptedBytes);
        }

        private string DecryptString(string cipherText)
        {
            var key = Encoding.UTF8.GetBytes(_encryptionKey.Substring(0, 32));
            var iv = Encoding.UTF8.GetBytes(_encryptionKey.Substring(0, 16));

            using var aes = Aes.Create();
            aes.Key = key;
            aes.IV = iv;

            using var decryptor = aes.CreateDecryptor();
            var cipherBytes = Convert.FromBase64String(cipherText);
            var decryptedBytes = decryptor.TransformFinalBlock(cipherBytes, 0, cipherBytes.Length);
            
            return Encoding.UTF8.GetString(decryptedBytes);
        }

        private bool IsValidLicenseKey(string licenseKey)
        {
            // تحقق من تنسيق مفتاح الترخيص
            if (string.IsNullOrWhiteSpace(licenseKey)) return false;
            if (licenseKey.Length != 29) return false; // XXXXX-XXXXX-XXXXX-XXXXX-XXXXX
            if (licenseKey.Count(c => c == '-') != 4) return false;

            var parts = licenseKey.Split('-');
            return parts.All(part => part.Length == 5 && part.All(char.IsLetterOrDigit));
        }

        private LicenseType GetLicenseTypeFromKey(string licenseKey)
        {
            var firstChar = licenseKey[0];
            return firstChar switch
            {
                'B' => LicenseType.Basic,
                'P' => LicenseType.Professional,
                'E' => LicenseType.Enterprise,
                _ => LicenseType.Basic
            };
        }

        private DateTime GetExpiryDateFromKey(string licenseKey, LicenseType licenseType)
        {
            return licenseType switch
            {
                LicenseType.Basic => DateTime.Now.AddYears(1),
                LicenseType.Professional => DateTime.Now.AddYears(1),
                LicenseType.Enterprise => DateTime.Now.AddYears(2),
                _ => DateTime.Now.AddDays(30)
            };
        }

        private int GetMaxUsersFromKey(string licenseKey)
        {
            var licenseType = GetLicenseTypeFromKey(licenseKey);
            return licenseType switch
            {
                LicenseType.Basic => 1,
                LicenseType.Professional => 5,
                LicenseType.Enterprise => 50,
                _ => 1
            };
        }

        private LicenseFeatures GetFeaturesFromKey(string licenseKey)
        {
            var licenseType = GetLicenseTypeFromKey(licenseKey);
            return licenseType switch
            {
                LicenseType.Basic => LicenseFeatures.Basic,
                LicenseType.Professional => LicenseFeatures.Basic | LicenseFeatures.Advanced | LicenseFeatures.Reports,
                LicenseType.Enterprise => LicenseFeatures.All,
                _ => LicenseFeatures.Basic
            };
        }

        private void SaveLicenseToRegistry(LicenseInfo license)
        {
            try
            {
                using var key = Registry.CurrentUser.CreateSubKey(_registryPath);
                var jsonData = JsonSerializer.Serialize(license);
                var encryptedData = EncryptString(jsonData);
                key.SetValue("LicenseData", encryptedData);
            }
            catch
            {
                // تجاهل أخطاء الريجستري
            }
        }

        private LicenseInfo GetLicenseFromRegistry()
        {
            try
            {
                using var key = Registry.CurrentUser.OpenSubKey(_registryPath);
                var encryptedData = key?.GetValue("LicenseData") as string;
                
                if (string.IsNullOrEmpty(encryptedData)) return null;
                
                var decryptedData = DecryptString(encryptedData);
                return JsonSerializer.Deserialize<LicenseInfo>(decryptedData);
            }
            catch
            {
                return null;
            }
        }
    }

    public class LicenseInfo
    {
        public string LicenseKey { get; set; }
        public string CustomerName { get; set; }
        public string CustomerEmail { get; set; }
        public LicenseType LicenseType { get; set; }
        public DateTime IssueDate { get; set; }
        public DateTime ExpiryDate { get; set; }
        public string HardwareFingerprint { get; set; }
        public int MaxUsers { get; set; }
        public LicenseFeatures Features { get; set; }
        public string Signature { get; set; }
    }

    public enum LicenseType
    {
        Trial,
        Basic,
        Professional,
        Enterprise
    }

    [Flags]
    public enum LicenseFeatures
    {
        Basic = 1,
        Advanced = 2,
        Reports = 4,
        EInvoice = 8,
        MultiUser = 16,
        CloudSync = 32,
        API = 64,
        All = Basic | Advanced | Reports | EInvoice | MultiUser | CloudSync | API
    }
}
