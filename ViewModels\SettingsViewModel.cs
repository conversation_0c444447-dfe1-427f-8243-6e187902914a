using AccountingSystem11.Services;
using System.Threading.Tasks;
using System.Windows.Input;

namespace AccountingSystem11.ViewModels
{
    /// <summary>
    /// Settings view model (placeholder)
    /// </summary>
    public class SettingsViewModel : BaseViewModel
    {
        private readonly IUserService _userService;
        private readonly ITaxService _taxService;

        public SettingsViewModel(IUserService userService, ITaxService taxService)
        {
            _userService = userService;
            _taxService = taxService;

            LoadSettingsCommand = new AsyncRelayCommand(LoadSettingsAsync);
        }

        public ICommand LoadSettingsCommand { get; }

        private async Task LoadSettingsAsync()
        {
            await ExecuteAsync(async () =>
            {
                // Load settings data
                await Task.Delay(1000); // Placeholder
            }, "جاري تحميل الإعدادات...");
        }
    }
}
