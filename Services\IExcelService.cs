using AccountingSystem11.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AccountingSystem11.Services
{
    /// <summary>
    /// Interface for Excel integration operations
    /// </summary>
    public interface IExcelService
    {
        /// <summary>
        /// Import invoices from Excel file
        /// </summary>
        Task<ImportResult<Invoice>> ImportInvoicesFromExcelAsync(string filePath);

        /// <summary>
        /// Export invoices to Excel file
        /// </summary>
        Task<bool> ExportInvoicesToExcelAsync(IEnumerable<Invoice> invoices, string filePath);

        /// <summary>
        /// Import products from Excel file
        /// </summary>
        Task<ImportResult<Product>> ImportProductsFromExcelAsync(string filePath);

        /// <summary>
        /// Export products to Excel file
        /// </summary>
        Task<bool> ExportProductsToExcelAsync(IEnumerable<Product> products, string filePath);

        /// <summary>
        /// Import customers from Excel file
        /// </summary>
        Task<ImportResult<Customer>> ImportCustomersFromExcelAsync(string filePath);

        /// <summary>
        /// Export customers to Excel file
        /// </summary>
        Task<bool> ExportCustomersToExcelAsync(IEnumerable<Customer> customers, string filePath);

        /// <summary>
        /// Generate Excel template for invoices
        /// </summary>
        Task<bool> GenerateInvoiceTemplateAsync(string filePath);

        /// <summary>
        /// Generate Excel template for products
        /// </summary>
        Task<bool> GenerateProductTemplateAsync(string filePath);

        /// <summary>
        /// Generate Excel template for customers
        /// </summary>
        Task<bool> GenerateCustomerTemplateAsync(string filePath);

        /// <summary>
        /// Export sales report to Excel
        /// </summary>
        Task<bool> ExportSalesReportToExcelAsync(SalesReportData reportData, string filePath);

        /// <summary>
        /// Export tax report to Excel
        /// </summary>
        Task<bool> ExportTaxReportToExcelAsync(TaxReport taxReport, string filePath);

        /// <summary>
        /// Export inventory report to Excel
        /// </summary>
        Task<bool> ExportInventoryReportToExcelAsync(InventoryReportData reportData, string filePath);

        /// <summary>
        /// Validate Excel file format
        /// </summary>
        Task<ValidationResult> ValidateExcelFileAsync(string filePath, ExcelFileType fileType);
    }

    /// <summary>
    /// Import result with success/error information
    /// </summary>
    public class ImportResult<T>
    {
        public bool IsSuccess { get; set; }
        public List<T> SuccessfulItems { get; set; } = new List<T>();
        public List<ImportError> Errors { get; set; } = new List<ImportError>();
        public int TotalRows { get; set; }
        public int SuccessfulRows { get; set; }
        public int ErrorRows { get; set; }
        public string Message { get; set; }
    }

    /// <summary>
    /// Import error information
    /// </summary>
    public class ImportError
    {
        public int RowNumber { get; set; }
        public string ColumnName { get; set; }
        public string ErrorMessage { get; set; }
        public string Value { get; set; }
    }

    /// <summary>
    /// Validation result for Excel files
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public string Message { get; set; }
    }

    /// <summary>
    /// Excel file types
    /// </summary>
    public enum ExcelFileType
    {
        Invoice = 1,
        Product = 2,
        Customer = 3,
        Supplier = 4,
        InventoryTransaction = 5
    }

    /// <summary>
    /// Sales report data for Excel export
    /// </summary>
    public class SalesReportData
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalTax { get; set; }
        public int InvoiceCount { get; set; }
        public List<SalesReportItem> Items { get; set; } = new List<SalesReportItem>();
        public List<SalesReportSummary> Summary { get; set; } = new List<SalesReportSummary>();
    }

    /// <summary>
    /// Sales report item
    /// </summary>
    public class SalesReportItem
    {
        public string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public string CustomerName { get; set; }
        public decimal Amount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public string PaymentStatus { get; set; }
    }

    /// <summary>
    /// Sales report summary
    /// </summary>
    public class SalesReportSummary
    {
        public string Category { get; set; }
        public decimal Amount { get; set; }
        public int Count { get; set; }
        public decimal Percentage { get; set; }
    }

    /// <summary>
    /// Inventory report data for Excel export
    /// </summary>
    public class InventoryReportData
    {
        public DateTime ReportDate { get; set; }
        public decimal TotalStockValue { get; set; }
        public int TotalProducts { get; set; }
        public int LowStockProducts { get; set; }
        public int OutOfStockProducts { get; set; }
        public List<InventoryReportItem> Items { get; set; } = new List<InventoryReportItem>();
    }

    /// <summary>
    /// Inventory report item
    /// </summary>
    public class InventoryReportItem
    {
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public string Category { get; set; }
        public decimal StockQuantity { get; set; }
        public decimal UnitCost { get; set; }
        public decimal TotalValue { get; set; }
        public decimal MinStockLevel { get; set; }
        public string StockStatus { get; set; }
    }
}
