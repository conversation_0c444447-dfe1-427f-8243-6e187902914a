<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة 11 - إصدار الويب</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 95%;
            max-width: 1200px;
            height: 90vh;
            display: flex;
        }

        .sidebar {
            background: #2c3e50;
            width: 250px;
            padding: 20px 0;
            color: white;
        }

        .logo {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid #34495e;
            margin-bottom: 20px;
        }

        .logo h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .logo p {
            font-size: 12px;
            opacity: 0.7;
        }

        .menu-item {
            padding: 15px 25px;
            cursor: pointer;
            transition: all 0.3s;
            border-right: 3px solid transparent;
        }

        .menu-item:hover, .menu-item.active {
            background: #34495e;
            border-right-color: #3498db;
        }

        .menu-item i {
            margin-left: 10px;
            width: 20px;
        }

        .main-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .header {
            background: #3498db;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            border-top: 4px solid;
        }

        .stat-card.customers { border-top-color: #e74c3c; }
        .stat-card.products { border-top-color: #f39c12; }
        .stat-card.invoices { border-top-color: #27ae60; }
        .stat-card.reports { border-top-color: #9b59b6; }

        .stat-number {
            font-size: 36px;
            font-weight: bold;
            margin: 10px 0;
        }

        .customers .stat-number { color: #e74c3c; }
        .products .stat-number { color: #f39c12; }
        .invoices .stat-number { color: #27ae60; }
        .reports .stat-number { color: #9b59b6; }

        .info-section {
            background: #ecf0f1;
            padding: 25px;
            border-radius: 15px;
            margin-top: 20px;
        }

        .info-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 20px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .info-item {
            background: white;
            padding: 15px;
            border-radius: 10px;
            border-right: 4px solid #3498db;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            margin: 5px;
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn.success { background: #27ae60; }
        .btn.success:hover { background: #229954; }

        .btn.danger { background: #e74c3c; }
        .btn.danger:hover { background: #c0392b; }

        .hidden {
            display: none;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .table th, .table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #ecf0f1;
        }

        .table th {
            background: #34495e;
            color: white;
            font-weight: bold;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
                height: auto;
                min-height: 100vh;
            }
            
            .sidebar {
                width: 100%;
                order: 2;
            }
            
            .main-content {
                order: 1;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="logo">
                <h1>🏢 نظام المحاسبة 11</h1>
                <p>إصدار الويب - بدون .NET</p>
            </div>
            
            <div class="menu-item active" onclick="showSection('dashboard')">
                <i>🏠</i> الرئيسية
            </div>
            <div class="menu-item" onclick="showSection('customers')">
                <i>👥</i> العملاء
            </div>
            <div class="menu-item" onclick="showSection('products')">
                <i>📦</i> المنتجات
            </div>
            <div class="menu-item" onclick="showSection('invoices')">
                <i>🧾</i> الفواتير
            </div>
            <div class="menu-item" onclick="showSection('reports')">
                <i>📊</i> التقارير
            </div>
            <div class="menu-item" onclick="showSection('settings')">
                <i>⚙️</i> الإعدادات
            </div>
            <div class="menu-item" onclick="logout()">
                <i>🚪</i> خروج
            </div>
        </div>

        <div class="main-content">
            <!-- لوحة التحكم -->
            <div id="dashboard" class="section">
                <div class="header">
                    <h1>🎉 مرحباً بك في نظام المحاسبة 11</h1>
                    <p>إصدار الويب - يعمل في أي متصفح بدون تثبيت</p>
                </div>

                <div class="stats-grid">
                    <div class="stat-card customers">
                        <h3>👥 العملاء</h3>
                        <div class="stat-number" id="customersCount">5</div>
                        <p>إجمالي العملاء المسجلين</p>
                    </div>
                    
                    <div class="stat-card products">
                        <h3>📦 المنتجات</h3>
                        <div class="stat-number" id="productsCount">25</div>
                        <p>إجمالي المنتجات المتاحة</p>
                    </div>
                    
                    <div class="stat-card invoices">
                        <h3>🧾 الفواتير</h3>
                        <div class="stat-number" id="invoicesCount">12</div>
                        <p>إجمالي الفواتير المُصدرة</p>
                    </div>
                    
                    <div class="stat-card reports">
                        <h3>💰 المبيعات</h3>
                        <div class="stat-number">₪ 45,230</div>
                        <p>إجمالي المبيعات هذا الشهر</p>
                    </div>
                </div>

                <div class="info-section">
                    <h3>📋 معلومات النظام</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <h4>✅ المميزات</h4>
                            <ul>
                                <li>لا يحتاج .NET Framework</li>
                                <li>يعمل في أي متصفح</li>
                                <li>واجهة عربية متجاوبة</li>
                                <li>حفظ محلي في المتصفح</li>
                                <li>سريع وخفيف</li>
                            </ul>
                        </div>
                        
                        <div class="info-item">
                            <h4>🔐 بيانات تسجيل الدخول</h4>
                            <p><strong>اسم المستخدم:</strong> admin</p>
                            <p><strong>كلمة المرور:</strong> admin123</p>
                            <p><strong>الصلاحية:</strong> مدير النظام</p>
                        </div>
                        
                        <div class="info-item">
                            <h4>📊 إحصائيات سريعة</h4>
                            <p><strong>العملاء النشطين:</strong> <span id="activeCustomers">5</span></p>
                            <p><strong>المنتجات المتاحة:</strong> <span id="availableProducts">25</span></p>
                            <p><strong>الفواتير المعلقة:</strong> <span id="pendingInvoices">3</span></p>
                        </div>
                        
                        <div class="info-item">
                            <h4>🕒 معلومات النظام</h4>
                            <p><strong>تاريخ اليوم:</strong> <span id="currentDate"></span></p>
                            <p><strong>الوقت:</strong> <span id="currentTime"></span></p>
                            <p><strong>المتصفح:</strong> <span id="browserInfo"></span></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- صفحة العملاء -->
            <div id="customers" class="section hidden">
                <div class="header">
                    <h1>👥 إدارة العملاء</h1>
                    <p>إضافة وإدارة بيانات العملاء</p>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <button class="btn success" onclick="showAddCustomerForm()">➕ إضافة عميل جديد</button>
                    <button class="btn" onclick="refreshCustomers()">🔄 تحديث القائمة</button>
                </div>

                <table class="table" id="customersTable">
                    <thead>
                        <tr>
                            <th>الرقم</th>
                            <th>الاسم</th>
                            <th>الشركة</th>
                            <th>الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>المدينة</th>
                            <th>العمليات</th>
                        </tr>
                    </thead>
                    <tbody id="customersTableBody">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </tbody>
                </table>
            </div>

            <!-- صفحات أخرى -->
            <div id="products" class="section hidden">
                <div class="header">
                    <h1>📦 إدارة المنتجات</h1>
                    <p>قريباً... صفحة إدارة المنتجات</p>
                </div>
            </div>

            <div id="invoices" class="section hidden">
                <div class="header">
                    <h1>🧾 إدارة الفواتير</h1>
                    <p>قريباً... صفحة إدارة الفواتير</p>
                </div>
            </div>

            <div id="reports" class="section hidden">
                <div class="header">
                    <h1>📊 التقارير</h1>
                    <p>قريباً... صفحة التقارير</p>
                </div>
            </div>

            <div id="settings" class="section hidden">
                <div class="header">
                    <h1>⚙️ الإعدادات</h1>
                    <p>قريباً... صفحة الإعدادات</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // البيانات المحلية (تُحفظ في localStorage)
        let customers = JSON.parse(localStorage.getItem('customers')) || [
            {id: 1, name: 'عميل تجريبي', company: 'شركة تجريبية', phone: '01012345678', email: '<EMAIL>', city: 'القاهرة'},
            {id: 2, name: 'أحمد محمد', company: 'شركة النور', phone: '01098765432', email: '<EMAIL>', city: 'الإسكندرية'},
            {id: 3, name: 'فاطمة علي', company: 'مؤسسة الأمل', phone: '01055555555', email: '<EMAIL>', city: 'الجيزة'},
            {id: 4, name: 'محمد حسن', company: 'شركة المستقبل', phone: '01077777777', email: '<EMAIL>', city: 'المنصورة'},
            {id: 5, name: 'سارة أحمد', company: 'مكتب الإبداع', phone: '01088888888', email: '<EMAIL>', city: 'أسوان'}
        ];

        // حفظ البيانات في localStorage
        function saveData() {
            localStorage.setItem('customers', JSON.stringify(customers));
        }

        // عرض قسم معين
        function showSection(sectionName) {
            // إخفاء جميع الأقسام
            document.querySelectorAll('.section').forEach(section => {
                section.classList.add('hidden');
            });
            
            // إزالة الفئة النشطة من جميع عناصر القائمة
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // عرض القسم المطلوب
            document.getElementById(sectionName).classList.remove('hidden');
            
            // إضافة الفئة النشطة لعنصر القائمة
            event.target.classList.add('active');
            
            // تحديث البيانات حسب القسم
            if (sectionName === 'customers') {
                loadCustomers();
            } else if (sectionName === 'dashboard') {
                updateDashboard();
            }
        }

        // تحديث لوحة التحكم
        function updateDashboard() {
            document.getElementById('customersCount').textContent = customers.length;
            document.getElementById('activeCustomers').textContent = customers.length;
            
            // تحديث التاريخ والوقت
            const now = new Date();
            document.getElementById('currentDate').textContent = now.toLocaleDateString('ar-EG');
            document.getElementById('currentTime').textContent = now.toLocaleTimeString('ar-EG');
            document.getElementById('browserInfo').textContent = navigator.userAgent.split(' ')[0];
        }

        // تحميل العملاء
        function loadCustomers() {
            const tbody = document.getElementById('customersTableBody');
            tbody.innerHTML = '';
            
            customers.forEach(customer => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${customer.id}</td>
                    <td>${customer.name}</td>
                    <td>${customer.company}</td>
                    <td>${customer.phone}</td>
                    <td>${customer.email}</td>
                    <td>${customer.city}</td>
                    <td>
                        <button class="btn" onclick="editCustomer(${customer.id})">✏️ تعديل</button>
                        <button class="btn danger" onclick="deleteCustomer(${customer.id})">🗑️ حذف</button>
                    </td>
                `;
            });
        }

        // إضافة عميل جديد
        function showAddCustomerForm() {
            const name = prompt('اسم العميل:');
            if (!name) return;
            
            const company = prompt('اسم الشركة:') || '';
            const phone = prompt('رقم الهاتف:') || '';
            const email = prompt('البريد الإلكتروني:') || '';
            const city = prompt('المدينة:') || '';
            
            const newCustomer = {
                id: Math.max(...customers.map(c => c.id), 0) + 1,
                name: name,
                company: company,
                phone: phone,
                email: email,
                city: city
            };
            
            customers.push(newCustomer);
            saveData();
            loadCustomers();
            updateDashboard();
            
            alert('تم إضافة العميل بنجاح!');
        }

        // تعديل عميل
        function editCustomer(id) {
            const customer = customers.find(c => c.id === id);
            if (!customer) return;
            
            const name = prompt('اسم العميل:', customer.name);
            if (name === null) return;
            
            const company = prompt('اسم الشركة:', customer.company);
            const phone = prompt('رقم الهاتف:', customer.phone);
            const email = prompt('البريد الإلكتروني:', customer.email);
            const city = prompt('المدينة:', customer.city);
            
            customer.name = name || customer.name;
            customer.company = company || customer.company;
            customer.phone = phone || customer.phone;
            customer.email = email || customer.email;
            customer.city = city || customer.city;
            
            saveData();
            loadCustomers();
            
            alert('تم تحديث بيانات العميل بنجاح!');
        }

        // حذف عميل
        function deleteCustomer(id) {
            if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
                customers = customers.filter(c => c.id !== id);
                saveData();
                loadCustomers();
                updateDashboard();
                
                alert('تم حذف العميل بنجاح!');
            }
        }

        // تحديث قائمة العملاء
        function refreshCustomers() {
            loadCustomers();
            alert('تم تحديث القائمة!');
        }

        // تسجيل الخروج
        function logout() {
            if (confirm('هل تريد الخروج من البرنامج؟')) {
                alert('تم تسجيل الخروج بنجاح!');
                // يمكن إعادة توجيه لصفحة تسجيل الدخول
            }
        }

        // تهيئة البرنامج
        document.addEventListener('DOMContentLoaded', function() {
            updateDashboard();
            
            // تحديث الوقت كل ثانية
            setInterval(() => {
                const now = new Date();
                document.getElementById('currentTime').textContent = now.toLocaleTimeString('ar-EG');
            }, 1000);
        });
    </script>
</body>
</html>
