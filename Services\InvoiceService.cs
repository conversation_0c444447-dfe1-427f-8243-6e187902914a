using AccountingSystem11.Data;
using AccountingSystem11.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AccountingSystem11.Services
{
    /// <summary>
    /// Invoice service implementation
    /// </summary>
    public class InvoiceService : IInvoiceService
    {
        private readonly AccountingDbContext _context;

        public InvoiceService(AccountingDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Invoice>> GetAllInvoicesAsync()
        {
            return await _context.Invoices
                .Include(i => i.Customer)
                .Include(i => i.Items)
                .Include(i => i.Payments)
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<Invoice> GetInvoiceByIdAsync(int id)
        {
            return await _context.Invoices
                .Include(i => i.Customer)
                .Include(i => i.Items)
                    .ThenInclude(ii => ii.Product)
                .Include(i => i.Payments)
                .FirstOrDefaultAsync(i => i.Id == id);
        }

        public async Task<Invoice> GetInvoiceByNumberAsync(string invoiceNumber)
        {
            if (string.IsNullOrWhiteSpace(invoiceNumber))
                return null;

            return await _context.Invoices
                .Include(i => i.Customer)
                .Include(i => i.Items)
                    .ThenInclude(ii => ii.Product)
                .Include(i => i.Payments)
                .FirstOrDefaultAsync(i => i.InvoiceNumber == invoiceNumber);
        }

        public async Task<Invoice> CreateInvoiceAsync(Invoice invoice)
        {
            if (invoice == null)
                throw new ArgumentNullException(nameof(invoice));

            // Validate unique invoice number
            var existingNumber = await InvoiceNumberExistsAsync(invoice.InvoiceNumber);
            if (existingNumber)
                throw new InvalidOperationException("رقم الفاتورة موجود بالفعل");

            // Calculate totals
            invoice.CalculateTotals();

            invoice.CreatedAt = DateTime.Now;
            _context.Invoices.Add(invoice);
            await _context.SaveChangesAsync();

            return invoice;
        }

        public async Task<Invoice> UpdateInvoiceAsync(Invoice invoice)
        {
            if (invoice == null)
                throw new ArgumentNullException(nameof(invoice));

            var existingInvoice = await _context.Invoices
                .Include(i => i.Items)
                .FirstOrDefaultAsync(i => i.Id == invoice.Id);

            if (existingInvoice == null)
                throw new InvalidOperationException("الفاتورة غير موجودة");

            // Validate unique invoice number
            var existingNumber = await InvoiceNumberExistsAsync(invoice.InvoiceNumber, invoice.Id);
            if (existingNumber)
                throw new InvalidOperationException("رقم الفاتورة موجود بالفعل");

            // Update properties
            existingInvoice.InvoiceNumber = invoice.InvoiceNumber;
            existingInvoice.InvoiceDate = invoice.InvoiceDate;
            existingInvoice.DueDate = invoice.DueDate;
            existingInvoice.CustomerId = invoice.CustomerId;
            existingInvoice.InvoiceType = invoice.InvoiceType;
            existingInvoice.Status = invoice.Status;
            existingInvoice.PaymentStatus = invoice.PaymentStatus;
            existingInvoice.DiscountPercentage = invoice.DiscountPercentage;
            existingInvoice.VatRate = invoice.VatRate;
            existingInvoice.AdditionalTaxAmount = invoice.AdditionalTaxAmount;
            existingInvoice.PaymentMethod = invoice.PaymentMethod;
            existingInvoice.Currency = invoice.Currency;
            existingInvoice.ExchangeRate = invoice.ExchangeRate;
            existingInvoice.Notes = invoice.Notes;
            existingInvoice.ReferenceNumber = invoice.ReferenceNumber;
            existingInvoice.PurchaseOrderNumber = invoice.PurchaseOrderNumber;

            // Update items
            existingInvoice.Items.Clear();
            foreach (var item in invoice.Items)
            {
                item.InvoiceId = existingInvoice.Id;
                item.CalculateLineTotals();
                existingInvoice.Items.Add(item);
            }

            // Recalculate totals
            existingInvoice.CalculateTotals();
            existingInvoice.Update();

            await _context.SaveChangesAsync();
            return existingInvoice;
        }

        public async Task<bool> DeleteInvoiceAsync(int id)
        {
            var invoice = await _context.Invoices.FindAsync(id);
            if (invoice == null)
                return false;

            // Check if invoice can be deleted
            if (invoice.Status == InvoiceStatus.Approved)
                throw new InvalidOperationException("لا يمكن حذف فاتورة معتمدة");

            invoice.Delete();
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> CancelInvoiceAsync(int id, string reason)
        {
            var invoice = await _context.Invoices.FindAsync(id);
            if (invoice == null)
                return false;

            invoice.Status = InvoiceStatus.Cancelled;
            invoice.Notes = $"{invoice.Notes}\nسبب الإلغاء: {reason}";
            invoice.Update();

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> InvoiceExistsAsync(int id)
        {
            return await _context.Invoices.AnyAsync(i => i.Id == id);
        }

        public async Task<bool> InvoiceNumberExistsAsync(string invoiceNumber, int? excludeId = null)
        {
            if (string.IsNullOrWhiteSpace(invoiceNumber))
                return false;

            var query = _context.Invoices.Where(i => i.InvoiceNumber == invoiceNumber);
            
            if (excludeId.HasValue)
                query = query.Where(i => i.Id != excludeId.Value);

            return await query.AnyAsync();
        }

        public async Task<IEnumerable<Invoice>> GetInvoicesByCustomerAsync(int customerId)
        {
            return await _context.Invoices
                .Include(i => i.Customer)
                .Include(i => i.Items)
                .Where(i => i.CustomerId == customerId)
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> GetInvoicesByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Invoices
                .Include(i => i.Customer)
                .Include(i => i.Items)
                .Where(i => i.InvoiceDate >= startDate && i.InvoiceDate <= endDate)
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> GetInvoicesByStatusAsync(InvoiceStatus status)
        {
            return await _context.Invoices
                .Include(i => i.Customer)
                .Include(i => i.Items)
                .Where(i => i.Status == status)
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> GetInvoicesByPaymentStatusAsync(PaymentStatus paymentStatus)
        {
            return await _context.Invoices
                .Include(i => i.Customer)
                .Include(i => i.Items)
                .Where(i => i.PaymentStatus == paymentStatus)
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> GetOverdueInvoicesAsync()
        {
            var today = DateTime.Today;
            
            return await _context.Invoices
                .Include(i => i.Customer)
                .Include(i => i.Items)
                .Where(i => i.DueDate < today && 
                           i.PaymentStatus != PaymentStatus.Paid &&
                           i.Status == InvoiceStatus.Approved)
                .OrderBy(i => i.DueDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Invoice>> SearchInvoicesAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllInvoicesAsync();

            searchTerm = searchTerm.Trim().ToLower();

            return await _context.Invoices
                .Include(i => i.Customer)
                .Include(i => i.Items)
                .Where(i => i.InvoiceNumber.ToLower().Contains(searchTerm) ||
                           i.Customer.Name.ToLower().Contains(searchTerm) ||
                           i.ReferenceNumber.ToLower().Contains(searchTerm) ||
                           i.PurchaseOrderNumber.ToLower().Contains(searchTerm))
                .OrderByDescending(i => i.InvoiceDate)
                .ToListAsync();
        }

        public async Task<string> GenerateNextInvoiceNumberAsync(InvoiceType invoiceType)
        {
            var prefix = invoiceType switch
            {
                InvoiceType.Sales => "SAL",
                InvoiceType.Purchase => "PUR",
                InvoiceType.SalesReturn => "SRT",
                InvoiceType.PurchaseReturn => "PRT",
                InvoiceType.Quotation => "QUO",
                InvoiceType.ProformaInvoice => "PRO",
                _ => "INV"
            };

            var lastInvoice = await _context.Invoices
                .Where(i => i.InvoiceType == invoiceType)
                .OrderByDescending(i => i.Id)
                .FirstOrDefaultAsync();

            var nextNumber = (lastInvoice?.Id ?? 0) + 1;
            var year = DateTime.Now.Year;
            
            return $"{prefix}{year}{nextNumber:D6}";
        }

        public async Task<bool> AddPaymentAsync(int invoiceId, InvoicePayment payment)
        {
            var invoice = await _context.Invoices.FindAsync(invoiceId);
            if (invoice == null)
                return false;

            payment.InvoiceId = invoiceId;
            payment.CreatedAt = DateTime.Now;
            
            _context.InvoicePayments.Add(payment);
            
            // Update invoice paid amount
            invoice.PaidAmount += payment.Amount;
            invoice.CalculateTotals();
            invoice.Update();

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RemovePaymentAsync(int paymentId)
        {
            var payment = await _context.InvoicePayments
                .Include(p => p.Invoice)
                .FirstOrDefaultAsync(p => p.Id == paymentId);

            if (payment == null)
                return false;

            // Update invoice paid amount
            payment.Invoice.PaidAmount -= payment.Amount;
            payment.Invoice.CalculateTotals();
            payment.Invoice.Update();

            _context.InvoicePayments.Remove(payment);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<decimal> GetTotalSalesAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _context.Invoices
                .Where(i => i.InvoiceType == InvoiceType.Sales && 
                           i.Status == InvoiceStatus.Approved);

            if (startDate.HasValue)
                query = query.Where(i => i.InvoiceDate >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(i => i.InvoiceDate <= endDate.Value);

            return await query.SumAsync(i => i.TotalAmount);
        }

        public async Task<decimal> GetTotalTaxAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _context.Invoices
                .Where(i => i.InvoiceType == InvoiceType.Sales && 
                           i.Status == InvoiceStatus.Approved);

            if (startDate.HasValue)
                query = query.Where(i => i.InvoiceDate >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(i => i.InvoiceDate <= endDate.Value);

            return await query.SumAsync(i => i.TotalTaxAmount);
        }

        public async Task<int> GetInvoiceCountAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _context.Invoices
                .Where(i => i.InvoiceType == InvoiceType.Sales && 
                           i.Status == InvoiceStatus.Approved);

            if (startDate.HasValue)
                query = query.Where(i => i.InvoiceDate >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(i => i.InvoiceDate <= endDate.Value);

            return await query.CountAsync();
        }

        public async Task<IEnumerable<Invoice>> GetRecentInvoicesAsync(int count = 10)
        {
            return await _context.Invoices
                .Include(i => i.Customer)
                .OrderByDescending(i => i.CreatedAt)
                .Take(count)
                .ToListAsync();
        }
    }
}
