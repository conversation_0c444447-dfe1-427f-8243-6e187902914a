{"version": 3, "targets": {"net6.0-windows7.0": {}}, "libraries": {}, "projectFileDependencyGroups": {"net6.0-windows7.0": ["BCrypt.Net-Next >= 4.0.3", "EPPlus >= 7.0.4", "LiveCharts.Wpf >= 0.9.7", "MaterialDesignColors >= 2.1.4", "MaterialDesignThemes >= 4.9.0", "Microsoft.EntityFrameworkCore >= 7.0.14", "Microsoft.EntityFrameworkCore.Design >= 7.0.14", "Microsoft.EntityFrameworkCore.SqlServer >= 7.0.14", "Microsoft.EntityFrameworkCore.Tools >= 7.0.14", "Newtonsoft.Json >= 13.0.3", "QRCoder >= 1.4.3", "System.Data.SqlClient >= 4.8.5"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\اتن\\AccountingSystem11.csproj", "projectName": "AccountingSystem11", "projectPath": "D:\\اتن\\AccountingSystem11.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\اتن\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "EPPlus": {"target": "Package", "version": "[7.0.4, )"}, "LiveCharts.Wpf": {"target": "Package", "version": "[0.9.7, )"}, "MaterialDesignColors": {"target": "Package", "version": "[2.1.4, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.14, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[7.0.14, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.14, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[7.0.14, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "QRCoder": {"target": "Package", "version": "[1.4.3, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "logs": [{"code": "Undefined", "level": "Error", "message": "Failed to download package 'Microsoft.EntityFrameworkCore.SqlServer.7.0.14' from 'https://api.nuget.org/v3-flatcontainer/microsoft.entityframeworkcore.sqlserver/7.0.14/microsoft.entityframeworkcore.sqlserver.7.0.14.nupkg'.\r\nNo such host is known. (api.nuget.org:443)\r\n  No such host is known."}, {"code": "Undefined", "level": "Error", "message": "Failed to download package 'System.Data.SqlClient.4.8.5' from 'https://api.nuget.org/v3-flatcontainer/system.data.sqlclient/4.8.5/system.data.sqlclient.4.8.5.nupkg'.\r\nNo such host is known. (api.nuget.org:443)\r\n  No such host is known."}, {"code": "Undefined", "level": "Error", "message": "Failed to download package 'MaterialDesignThemes.4.9.0' from 'https://api.nuget.org/v3-flatcontainer/materialdesignthemes/4.9.0/materialdesignthemes.4.9.0.nupkg'.\r\nNo such host is known. (api.nuget.org:443)\r\n  No such host is known."}, {"code": "Undefined", "level": "Error", "message": "Failed to download package 'EPPlus.7.0.4' from 'https://api.nuget.org/v3-flatcontainer/epplus/7.0.4/epplus.7.0.4.nupkg'.\r\nNo such host is known. (api.nuget.org:443)\r\n  No such host is known."}, {"code": "Undefined", "level": "Error", "message": "Failed to download package 'Microsoft.EntityFrameworkCore.Tools.7.0.14' from 'https://api.nuget.org/v3-flatcontainer/microsoft.entityframeworkcore.tools/7.0.14/microsoft.entityframeworkcore.tools.7.0.14.nupkg'.\r\nNo such host is known. (api.nuget.org:443)\r\n  No such host is known."}, {"code": "Undefined", "level": "Error", "message": "The feed 'nuget.org [https://api.nuget.org/v3/index.json]' lists package 'Microsoft.EntityFrameworkCore.Tools.7.0.14' but multiple attempts to download the nupkg have failed. The feed is either invalid or required packages were removed while the current operation was in progress. Verify the package exists on the feed and try again."}, {"code": "Undefined", "level": "Error", "message": "The feed 'nuget.org [https://api.nuget.org/v3/index.json]' lists package 'EPPlus.7.0.4' but multiple attempts to download the nupkg have failed. The feed is either invalid or required packages were removed while the current operation was in progress. Verify the package exists on the feed and try again."}, {"code": "Undefined", "level": "Error", "message": "The feed 'nuget.org [https://api.nuget.org/v3/index.json]' lists package 'MaterialDesignThemes.4.9.0' but multiple attempts to download the nupkg have failed. The feed is either invalid or required packages were removed while the current operation was in progress. Verify the package exists on the feed and try again."}, {"code": "Undefined", "level": "Error", "message": "The feed 'nuget.org [https://api.nuget.org/v3/index.json]' lists package 'System.Data.SqlClient.4.8.5' but multiple attempts to download the nupkg have failed. The feed is either invalid or required packages were removed while the current operation was in progress. Verify the package exists on the feed and try again."}, {"code": "Undefined", "level": "Error", "message": "The feed 'nuget.org [https://api.nuget.org/v3/index.json]' lists package 'Microsoft.EntityFrameworkCore.SqlServer.7.0.14' but multiple attempts to download the nupkg have failed. The feed is either invalid or required packages were removed while the current operation was in progress. Verify the package exists on the feed and try again."}]}