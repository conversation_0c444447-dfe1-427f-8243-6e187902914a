#pragma once
#include <string>
#include <vector>
#include <memory>
#include <sqlite3.h>

struct Customer {
    int id;
    std::wstring name;
    std::wstring company;
    std::wstring phone;
    std::wstring mobile;
    std::wstring email;
    std::wstring address;
    std::wstring city;
    std::wstring taxNumber;
    double creditLimit;
    double balance;
    bool isActive;
};

struct Product {
    int id;
    std::wstring code;
    std::wstring name;
    std::wstring description;
    int categoryId;
    std::wstring unit;
    double purchasePrice;
    double sellingPrice;
    double stockQuantity;
    double minStockLevel;
    bool isTaxable;
    double taxRate;
    bool isActive;
};

struct Invoice {
    int id;
    std::wstring invoiceNumber;
    int customerId;
    std::wstring invoiceDate;
    std::wstring dueDate;
    double subtotal;
    double taxAmount;
    double discountAmount;
    double totalAmount;
    std::wstring status;
    std::wstring notes;
};

class Database {
private:
    sqlite3* db;
    std::wstring dbPath;

    bool ExecuteSQL(const std::string& sql);
    std::string WStringToString(const std::wstring& wstr);
    std::wstring StringToWString(const std::string& str);

public:
    Database();
    ~Database();

    bool Initialize();
    bool CreateTables();
    bool SeedInitialData();

    // Customer operations
    std::vector<Customer> GetCustomers();
    bool AddCustomer(const Customer& customer);
    bool UpdateCustomer(const Customer& customer);
    bool DeleteCustomer(int id);
    int GetCustomerCount();

    // Product operations
    std::vector<Product> GetProducts();
    bool AddProduct(const Product& product);
    bool UpdateProduct(const Product& product);
    bool DeleteProduct(int id);
    int GetProductCount();

    // Invoice operations
    std::vector<Invoice> GetInvoices();
    bool AddInvoice(const Invoice& invoice);
    bool UpdateInvoice(const Invoice& invoice);
    bool DeleteInvoice(int id);
    int GetInvoiceCount();

    // Statistics
    double GetTotalSales();
    int GetLowStockCount();

    // Utility
    bool BackupDatabase(const std::wstring& backupPath);
    bool RestoreDatabase(const std::wstring& backupPath);
};
