using System;
using System.Diagnostics;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using AccountingSystem11.Services;

namespace AccountingSystem11.Views
{
    public partial class LicenseActivationWindow : Window
    {
        private readonly LicenseService _licenseService;
        private LicenseInfo _currentLicense;

        public bool LicenseActivated { get; private set; }

        public LicenseActivationWindow()
        {
            InitializeComponent();
            _licenseService = new LicenseService();
            LoadCurrentLicenseInfo();
        }

        private void LoadCurrentLicenseInfo()
        {
            try
            {
                _currentLicense = _licenseService.GetLicenseInfo();
                UpdateLicenseDisplay();
            }
            catch (Exception ex)
            {
                ShowStatus($"خطأ في تحميل معلومات الترخيص: {ex.Message}", false);
            }
        }

        private void UpdateLicenseDisplay()
        {
            if (_currentLicense == null) return;

            // تحديث نوع الترخيص
            LicenseTypeText.Text = GetLicenseTypeDisplayName(_currentLicense.LicenseType);

            // تحديث تاريخ الانتهاء
            if (_currentLicense.LicenseType == LicenseType.Trial)
            {
                var remainingDays = _licenseService.GetRemainingTrialDays();
                ExpiryDateText.Text = $"تنتهي في: {remainingDays} يوم";
                ExpiryDateText.Foreground = remainingDays <= 7 ? Brushes.Red : Brushes.Orange;
            }
            else
            {
                ExpiryDateText.Text = $"تنتهي في: {_currentLicense.ExpiryDate:dd/MM/yyyy}";
                ExpiryDateText.Foreground = _currentLicense.ExpiryDate <= DateTime.Now.AddDays(30) ? Brushes.Orange : Brushes.Green;
            }

            // تحديث المميزات
            FeaturesText.Text = $"المميزات: {GetFeaturesDisplayName(_currentLicense.Features)}";

            // تحديث العنوان
            if (_currentLicense.LicenseType != LicenseType.Trial)
            {
                CurrentLicenseTitle.Text = "✅ الترخيص المفعل:";
                CustomerNameTextBox.Text = _currentLicense.CustomerName;
                CustomerEmailTextBox.Text = _currentLicense.CustomerEmail;
                LicenseKeyTextBox.Text = _currentLicense.LicenseKey;
            }
        }

        private string GetLicenseTypeDisplayName(LicenseType licenseType)
        {
            return licenseType switch
            {
                LicenseType.Trial => "نسخة تجريبية",
                LicenseType.Basic => "الخطة الأساسية",
                LicenseType.Professional => "الخطة المتقدمة",
                LicenseType.Enterprise => "خطة المؤسسات",
                _ => "غير محدد"
            };
        }

        private string GetFeaturesDisplayName(LicenseFeatures features)
        {
            if (features.HasFlag(LicenseFeatures.All))
                return "جميع المميزات";
            
            if (features.HasFlag(LicenseFeatures.Advanced))
                return "متقدمة";
            
            return "أساسية";
        }

        private void LicenseKeyTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            // تنسيق مفتاح الترخيص تلقائياً
            var textBox = sender as System.Windows.Controls.TextBox;
            var text = textBox.Text.Replace("-", "").ToUpper();
            
            if (text.Length > 25) text = text.Substring(0, 25);
            
            var formatted = "";
            for (int i = 0; i < text.Length; i++)
            {
                if (i > 0 && i % 5 == 0) formatted += "-";
                formatted += text[i];
            }
            
            if (formatted != textBox.Text)
            {
                var cursorPosition = textBox.SelectionStart;
                textBox.Text = formatted;
                textBox.SelectionStart = Math.Min(cursorPosition, formatted.Length);
            }

            // تحديث حالة زر التفعيل
            ActivateButton.IsEnabled = IsValidLicenseKeyFormat(formatted) && 
                                     !string.IsNullOrWhiteSpace(CustomerNameTextBox.Text) &&
                                     !string.IsNullOrWhiteSpace(CustomerEmailTextBox.Text);
        }

        private bool IsValidLicenseKeyFormat(string licenseKey)
        {
            var pattern = @"^[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}$";
            return Regex.IsMatch(licenseKey, pattern);
        }

        private async void ActivateButton_Click(object sender, RoutedEventArgs e)
        {
            await ActivateLicense();
        }

        private async Task ActivateLicense()
        {
            try
            {
                ShowLoading(true);
                ShowStatus("جاري تفعيل الترخيص...", true);

                var licenseKey = LicenseKeyTextBox.Text.Trim();
                var customerName = CustomerNameTextBox.Text.Trim();
                var customerEmail = CustomerEmailTextBox.Text.Trim();

                // التحقق من صحة البيانات
                if (!IsValidLicenseKeyFormat(licenseKey))
                {
                    ShowStatus("تنسيق مفتاح الترخيص غير صحيح", false);
                    return;
                }

                if (string.IsNullOrWhiteSpace(customerName))
                {
                    ShowStatus("يرجى إدخال اسم العميل", false);
                    return;
                }

                if (string.IsNullOrWhiteSpace(customerEmail) || !IsValidEmail(customerEmail))
                {
                    ShowStatus("يرجى إدخال بريد إلكتروني صحيح", false);
                    return;
                }

                // محاولة التفعيل
                await Task.Delay(2000); // محاكاة التحقق من الخادم

                var success = _licenseService.ActivateLicense(licenseKey, customerName, customerEmail);

                if (success)
                {
                    ShowStatus("✅ تم تفعيل الترخيص بنجاح!", true);
                    LicenseActivated = true;
                    
                    // تحديث معلومات الترخيص
                    LoadCurrentLicenseInfo();
                    
                    await Task.Delay(2000);
                    
                    MessageBox.Show(
                        "تم تفعيل الترخيص بنجاح!\n\nشكراً لك لشراء نظام المحاسبة 11.\nستتمكن الآن من الوصول لجميع المميزات المتاحة في خطتك.",
                        "تفعيل ناجح",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                    
                    this.DialogResult = true;
                    this.Close();
                }
                else
                {
                    ShowStatus("❌ فشل في تفعيل الترخيص. تحقق من صحة المفتاح.", false);
                }
            }
            catch (Exception ex)
            {
                ShowStatus($"خطأ في التفعيل: {ex.Message}", false);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private void BuyLicenseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح موقع الشراء
                Process.Start(new ProcessStartInfo
                {
                    FileName = "https://www.system11.com/buy",
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                ShowStatus($"خطأ في فتح الموقع: {ex.Message}", false);
            }
        }

        private void ContinueTrialButton_Click(object sender, RoutedEventArgs e)
        {
            var remainingDays = _licenseService.GetRemainingTrialDays();
            
            if (remainingDays <= 0)
            {
                MessageBox.Show(
                    "انتهت فترة التجربة المجانية.\n\nيرجى شراء ترخيص للمتابعة.",
                    "انتهاء التجربة",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show(
                $"لديك {remainingDays} يوم متبقي في النسخة التجريبية.\n\nهل تريد المتابعة بالنسخة التجريبية؟",
                "متابعة التجربة",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                this.DialogResult = false;
                this.Close();
            }
        }

        private void ShowStatus(string message, bool isSuccess)
        {
            StatusMessageTextBlock.Text = message;
            StatusMessageTextBlock.Foreground = isSuccess ? Brushes.Green : Brushes.Red;
            StatusMessageTextBlock.Visibility = Visibility.Visible;
        }

        private void ShowLoading(bool show)
        {
            LoadingProgressBar.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
            ActivateButton.IsEnabled = !show;
            BuyLicenseButton.IsEnabled = !show;
            ContinueTrialButton.IsEnabled = !show;
        }
    }
}
