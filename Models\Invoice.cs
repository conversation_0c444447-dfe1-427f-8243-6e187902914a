using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace AccountingSystem11.Models
{
    /// <summary>
    /// Invoice entity supporting Egyptian tax system
    /// </summary>
    public class Invoice : BaseEntity
    {
        [Required]
        [MaxLength(20)]
        public string InvoiceNumber { get; set; }

        [Required]
        public DateTime InvoiceDate { get; set; } = DateTime.Now;

        /// <summary>
        /// Due date for payment
        /// </summary>
        public DateTime? DueDate { get; set; }

        /// <summary>
        /// Customer information
        /// </summary>
        [Required]
        public int CustomerId { get; set; }
        public virtual Customer Customer { get; set; }

        /// <summary>
        /// Invoice type: Sales, Purchase, Return, etc.
        /// </summary>
        public InvoiceType InvoiceType { get; set; } = InvoiceType.Sales;

        /// <summary>
        /// Invoice status
        /// </summary>
        public InvoiceStatus Status { get; set; } = InvoiceStatus.Draft;

        /// <summary>
        /// Payment status
        /// </summary>
        public PaymentStatus PaymentStatus { get; set; } = PaymentStatus.Unpaid;

        /// <summary>
        /// Subtotal before taxes and discounts
        /// </summary>
        public decimal Subtotal { get; set; } = 0;

        /// <summary>
        /// Total discount amount
        /// </summary>
        public decimal DiscountAmount { get; set; } = 0;

        /// <summary>
        /// Discount percentage
        /// </summary>
        public decimal DiscountPercentage { get; set; } = 0;

        /// <summary>
        /// Total amount after discount but before tax
        /// </summary>
        public decimal AmountAfterDiscount { get; set; } = 0;

        /// <summary>
        /// VAT amount (ضريبة القيمة المضافة)
        /// </summary>
        public decimal VatAmount { get; set; } = 0;

        /// <summary>
        /// VAT rate applied
        /// </summary>
        public decimal VatRate { get; set; } = 14; // Default Egyptian VAT rate

        /// <summary>
        /// Additional tax amount (if any)
        /// </summary>
        public decimal AdditionalTaxAmount { get; set; } = 0;

        /// <summary>
        /// Total tax amount
        /// </summary>
        public decimal TotalTaxAmount { get; set; } = 0;

        /// <summary>
        /// Final total amount
        /// </summary>
        public decimal TotalAmount { get; set; } = 0;

        /// <summary>
        /// Amount paid
        /// </summary>
        public decimal PaidAmount { get; set; } = 0;

        /// <summary>
        /// Remaining amount
        /// </summary>
        public decimal RemainingAmount { get; set; } = 0;

        /// <summary>
        /// Payment method
        /// </summary>
        public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.Cash;

        /// <summary>
        /// Currency code (default EGP)
        /// </summary>
        [MaxLength(3)]
        public string Currency { get; set; } = "EGP";

        /// <summary>
        /// Exchange rate (if not EGP)
        /// </summary>
        public decimal ExchangeRate { get; set; } = 1;

        /// <summary>
        /// Notes or comments
        /// </summary>
        [MaxLength(500)]
        public string Notes { get; set; }

        /// <summary>
        /// Internal reference number
        /// </summary>
        [MaxLength(50)]
        public string ReferenceNumber { get; set; }

        /// <summary>
        /// Purchase order number (for sales invoices)
        /// </summary>
        [MaxLength(50)]
        public string PurchaseOrderNumber { get; set; }

        /// <summary>
        /// Egyptian E-Invoice UUID
        /// </summary>
        [MaxLength(100)]
        public string EInvoiceUuid { get; set; }

        /// <summary>
        /// E-Invoice submission status
        /// </summary>
        public EInvoiceStatus EInvoiceStatus { get; set; } = EInvoiceStatus.NotSubmitted;

        /// <summary>
        /// E-Invoice submission date
        /// </summary>
        public DateTime? EInvoiceSubmissionDate { get; set; }

        /// <summary>
        /// E-Invoice response from tax authority
        /// </summary>
        [MaxLength(1000)]
        public string EInvoiceResponse { get; set; }

        // Navigation properties
        public virtual ICollection<InvoiceItem> Items { get; set; } = new List<InvoiceItem>();
        public virtual ICollection<InvoicePayment> Payments { get; set; } = new List<InvoicePayment>();

        /// <summary>
        /// Calculate invoice totals
        /// </summary>
        public void CalculateTotals()
        {
            Subtotal = Items.Sum(i => i.TotalAmount);
            
            // Calculate discount
            if (DiscountPercentage > 0)
            {
                DiscountAmount = Subtotal * (DiscountPercentage / 100);
            }
            
            AmountAfterDiscount = Subtotal - DiscountAmount;
            
            // Calculate VAT
            VatAmount = AmountAfterDiscount * (VatRate / 100);
            
            // Calculate total tax
            TotalTaxAmount = VatAmount + AdditionalTaxAmount;
            
            // Calculate final total
            TotalAmount = AmountAfterDiscount + TotalTaxAmount;
            
            // Calculate remaining amount
            RemainingAmount = TotalAmount - PaidAmount;
            
            // Update payment status
            if (PaidAmount >= TotalAmount)
            {
                PaymentStatus = PaymentStatus.Paid;
            }
            else if (PaidAmount > 0)
            {
                PaymentStatus = PaymentStatus.PartiallyPaid;
            }
            else
            {
                PaymentStatus = PaymentStatus.Unpaid;
            }
        }
    }

    public enum InvoiceType
    {
        Sales = 1,
        Purchase = 2,
        SalesReturn = 3,
        PurchaseReturn = 4,
        Quotation = 5,
        ProformaInvoice = 6
    }

    public enum InvoiceStatus
    {
        Draft = 1,
        Pending = 2,
        Approved = 3,
        Sent = 4,
        Cancelled = 5,
        Void = 6
    }

    public enum PaymentStatus
    {
        Unpaid = 1,
        PartiallyPaid = 2,
        Paid = 3,
        Overdue = 4,
        Refunded = 5
    }

    public enum PaymentMethod
    {
        Cash = 1,
        BankTransfer = 2,
        Check = 3,
        CreditCard = 4,
        DebitCard = 5,
        MobileWallet = 6,
        Other = 7
    }

    public enum EInvoiceStatus
    {
        NotSubmitted = 1,
        Submitted = 2,
        Accepted = 3,
        Rejected = 4,
        Cancelled = 5
    }
}
