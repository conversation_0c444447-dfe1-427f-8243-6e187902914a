using AccountingSystem11.Services;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;

namespace AccountingSystem11.ViewModels
{
    /// <summary>
    /// Dashboard view model للعمل مع قاعدة بيانات Access
    /// </summary>
    public class AccessDashboardViewModel : BaseViewModel
    {
        private readonly AccessDataService _dataService;

        public AccessDashboardViewModel(AccessDataService dataService)
        {
            _dataService = dataService;

            // Initialize commands
            RefreshCommand = new AsyncRelayCommand(LoadDataAsync);

            // Initialize collections
            RecentCustomers = new ObservableCollection<string>();
            RecentProducts = new ObservableCollection<string>();
            SystemAlerts = new ObservableCollection<string>();

            // Set default date range (current month)
            var now = DateTime.Now;
            StartDate = new DateTime(now.Year, now.Month, 1);
            EndDate = now;
        }

        #region Properties

        private DateTime _startDate;
        public DateTime StartDate
        {
            get => _startDate;
            set => SetProperty(ref _startDate, value);
        }

        private DateTime _endDate;
        public DateTime EndDate
        {
            get => _endDate;
            set => SetProperty(ref _endDate, value);
        }

        // Summary Statistics
        private decimal _totalSales;
        public decimal TotalSales
        {
            get => _totalSales;
            set => SetProperty(ref _totalSales, value);
        }

        private decimal _totalProfit;
        public decimal TotalProfit
        {
            get => _totalProfit;
            set => SetProperty(ref _totalProfit, value);
        }

        private int _totalInvoices;
        public int TotalInvoices
        {
            get => _totalInvoices;
            set => SetProperty(ref _totalInvoices, value);
        }

        private int _totalCustomers;
        public int TotalCustomers
        {
            get => _totalCustomers;
            set => SetProperty(ref _totalCustomers, value);
        }

        private int _totalProducts;
        public int TotalProducts
        {
            get => _totalProducts;
            set => SetProperty(ref _totalProducts, value);
        }

        private decimal _inventoryValue;
        public decimal InventoryValue
        {
            get => _inventoryValue;
            set => SetProperty(ref _inventoryValue, value);
        }

        private int _lowStockProductsCount;
        public int LowStockProductsCount
        {
            get => _lowStockProductsCount;
            set => SetProperty(ref _lowStockProductsCount, value);
        }

        private decimal _salesGrowth = 15.5m; // قيمة افتراضية
        public decimal SalesGrowth
        {
            get => _salesGrowth;
            set => SetProperty(ref _salesGrowth, value);
        }

        private decimal _profitGrowth = 12.3m; // قيمة افتراضية
        public decimal ProfitGrowth
        {
            get => _profitGrowth;
            set => SetProperty(ref _profitGrowth, value);
        }

        // Collections
        public ObservableCollection<string> RecentCustomers { get; }
        public ObservableCollection<string> RecentProducts { get; }
        public ObservableCollection<string> SystemAlerts { get; }

        // Chart Properties
        private string _salesChartTitle = "مبيعات الشهر الحالي";
        public string SalesChartTitle
        {
            get => _salesChartTitle;
            set => SetProperty(ref _salesChartTitle, value);
        }

        // Quick Stats
        private int _pendingInvoicesCount = 0;
        public int PendingInvoicesCount
        {
            get => _pendingInvoicesCount;
            set => SetProperty(ref _pendingInvoicesCount, value);
        }

        private int _overdueInvoicesCount = 0;
        public int OverdueInvoicesCount
        {
            get => _overdueInvoicesCount;
            set => SetProperty(ref _overdueInvoicesCount, value);
        }

        private decimal _totalReceivables = 0;
        public decimal TotalReceivables
        {
            get => _totalReceivables;
            set => SetProperty(ref _totalReceivables, value);
        }

        #endregion

        #region Commands

        public ICommand RefreshCommand { get; }

        #endregion

        #region Public Methods

        public async Task LoadDataAsync()
        {
            await ExecuteAsync(async () =>
            {
                // تحميل الإحصائيات من قاعدة البيانات
                var stats = await _dataService.GetDashboardStatsAsync();

                if (stats != null)
                {
                    // تحديث الإحصائيات الأساسية
                    TotalCustomers = stats.TotalCustomers;
                    TotalProducts = stats.TotalProducts;
                    LowStockProductsCount = stats.LowStockProducts;
                    InventoryValue = stats.InventoryValue;
                    TotalSales = stats.TotalSales;
                    TotalProfit = stats.TotalProfit;
                    TotalInvoices = stats.TotalInvoices;
                }

                // تحميل العملاء الحديثين
                await LoadRecentCustomersAsync();

                // تحميل المنتجات الحديثة
                await LoadRecentProductsAsync();

                // تحميل التنبيهات
                await LoadSystemAlertsAsync();

            }, "جاري تحميل بيانات لوحة التحكم...");
        }

        #endregion

        #region Private Methods

        private async Task LoadRecentCustomersAsync()
        {
            try
            {
                var customers = await _dataService.GetAllCustomersAsync();
                RecentCustomers.Clear();

                var recentCustomers = customers
                    .OrderByDescending(c => c.CreatedAt)
                    .Take(5);

                foreach (var customer in recentCustomers)
                {
                    RecentCustomers.Add(customer.Name);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل العملاء الحديثين: {ex.Message}");
            }
        }

        private async Task LoadRecentProductsAsync()
        {
            try
            {
                var products = await _dataService.GetAllProductsAsync();
                RecentProducts.Clear();

                var recentProducts = products
                    .OrderByDescending(p => p.CreatedAt)
                    .Take(5);

                foreach (var product in recentProducts)
                {
                    RecentProducts.Add(product.Name);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل المنتجات الحديثة: {ex.Message}");
            }
        }

        private async Task LoadSystemAlertsAsync()
        {
            try
            {
                SystemAlerts.Clear();

                // تنبيهات المخزون المنخفض
                if (LowStockProductsCount > 0)
                {
                    SystemAlerts.Add($"⚠️ {LowStockProductsCount} منتج يحتاج إعادة تموين");
                }

                // تنبيهات عامة
                SystemAlerts.Add("✅ النظام يعمل بشكل طبيعي");
                SystemAlerts.Add("📊 قاعدة البيانات المحلية متصلة");

                if (TotalCustomers == 0)
                {
                    SystemAlerts.Add("💡 ابدأ بإضافة عملائك الأوائل");
                }

                if (TotalProducts == 0)
                {
                    SystemAlerts.Add("💡 أضف منتجاتك لبدء العمل");
                }

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل التنبيهات: {ex.Message}");
            }
        }

        #endregion
    }
}
