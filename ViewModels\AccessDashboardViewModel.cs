using AccountingSystem11.Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Input;

namespace AccountingSystem11.ViewModels
{
    /// <summary>
    /// Dashboard ViewModel for Access database
    /// </summary>
    public class AccessDashboardViewModel : BaseViewModel
    {
        private readonly AccessDataService _accessDataService;

        // Properties
        private int _customerCount;
        public int CustomerCount
        {
            get => _customerCount;
            set => SetProperty(ref _customerCount, value);
        }

        private int _productCount;
        public int ProductCount
        {
            get => _productCount;
            set => SetProperty(ref _productCount, value);
        }

        private int _invoiceCount;
        public int InvoiceCount
        {
            get => _invoiceCount;
            set => SetProperty(ref _invoiceCount, value);
        }

        private decimal _totalSales;
        public decimal TotalSales
        {
            get => _totalSales;
            set => SetProperty(ref _totalSales, value);
        }

        private int _lowStockCount;
        public int LowStockCount
        {
            get => _lowStockCount;
            set => SetProperty(ref _lowStockCount, value);
        }

        private string _welcomeMessage;
        public string WelcomeMessage
        {
            get => _welcomeMessage;
            set => SetProperty(ref _welcomeMessage, value);
        }

        private string _systemInfo;
        public string SystemInfo
        {
            get => _systemInfo;
            set => SetProperty(ref _systemInfo, value);
        }

        // Commands
        public ICommand RefreshDataCommand { get; }
        public ICommand ViewCustomersCommand { get; }
        public ICommand ViewProductsCommand { get; }
        public ICommand ViewInvoicesCommand { get; }

        public AccessDashboardViewModel(AccessDataService accessDataService)
        {
            _accessDataService = accessDataService;

            // Initialize commands
            RefreshDataCommand = new RelayCommand(async () => await RefreshDataAsync());
            ViewCustomersCommand = new RelayCommand(ViewCustomers);
            ViewProductsCommand = new RelayCommand(ViewProducts);
            ViewInvoicesCommand = new RelayCommand(ViewInvoices);

            // Set initial messages
            WelcomeMessage = $"مرحباً بك في نظام المحاسبة 11 - {DateTime.Now:yyyy/MM/dd}";
            SystemInfo = "قاعدة البيانات: Microsoft Access | الإصدار: 1.0 | الحالة: متصل";

            // Load initial data
            _ = Task.Run(async () => await RefreshDataAsync());
        }

        private async Task RefreshDataAsync()
        {
            try
            {
                IsLoading = true;

                var stats = await _accessDataService.GetDashboardStatsAsync();

                CustomerCount = (int)stats["CustomerCount"];
                ProductCount = (int)stats["ProductCount"];
                InvoiceCount = (int)stats["InvoiceCount"];
                TotalSales = (decimal)stats["TotalSales"];
                LowStockCount = (int)stats["LowStockCount"];

                // Update system info
                SystemInfo = $"قاعدة البيانات: Microsoft Access | آخر تحديث: {DateTime.Now:HH:mm:ss} | الحالة: متصل";
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحديث البيانات: {ex.Message}");
                SystemInfo = "قاعدة البيانات: Microsoft Access | الحالة: خطأ في الاتصال";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void ViewCustomers()
        {
            try
            {
                // Navigate to customers view
                ShowInfo("سيتم فتح صفحة العملاء قريباً");
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في فتح صفحة العملاء: {ex.Message}");
            }
        }

        private void ViewProducts()
        {
            try
            {
                // Navigate to products view
                ShowInfo("سيتم فتح صفحة المنتجات قريباً");
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في فتح صفحة المنتجات: {ex.Message}");
            }
        }

        private void ViewInvoices()
        {
            try
            {
                // Navigate to invoices view
                ShowInfo("سيتم فتح صفحة الفواتير قريباً");
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في فتح صفحة الفواتير: {ex.Message}");
            }
        }
    }
}
