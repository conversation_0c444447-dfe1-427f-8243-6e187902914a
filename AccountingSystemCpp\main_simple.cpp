#include <windows.h>
#include <commctrl.h>
#include <string>
#include <vector>

#pragma comment(lib, "comctl32.lib")
#pragma comment(lib, "user32.lib")
#pragma comment(lib, "gdi32.lib")
#pragma comment(lib, "kernel32.lib")

// Global variables
HINSTANCE g_hInst;
HWND g_hMainWnd;

// Window class name
const wchar_t* MAIN_WINDOW_CLASS = L"AccountingSystem11MainWindow";

// Control IDs
#define ID_BTN_CUSTOMERS    1001
#define ID_BTN_PRODUCTS     1002
#define ID_BTN_INVOICES     1003
#define ID_BTN_REPORTS      1004
#define ID_BTN_ABOUT        1005
#define ID_BTN_EXIT         1006

// Simple database simulation
struct Customer {
    int id;
    std::wstring name;
    std::wstring company;
    std::wstring phone;
    std::wstring email;
};

std::vector<Customer> g_customers = {
    {1, L"عميل تجريبي", L"شركة تجريبية", L"***********", L"<EMAIL>"},
    {2, L"أحمد محمد", L"شركة النور", L"***********", L"<EMAIL>"},
    {3, L"فاطمة علي", L"مؤسسة الأمل", L"***********", L"<EMAIL>"},
    {4, L"محمد حسن", L"شركة المستقبل", L"***********", L"<EMAIL>"},
    {5, L"سارة أحمد", L"مكتب الإبداع", L"***********", L"<EMAIL>"}
};

// Forward declarations
LRESULT CALLBACK MainWndProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam);
void CreateMainWindow();
void ShowDashboard(HWND hwnd);
void ShowCustomers(HWND hwnd);
void ShowAbout();

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    g_hInst = hInstance;

    // Initialize common controls
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_LISTVIEW_CLASSES | ICC_TREEVIEW_CLASSES | ICC_BAR_CLASSES;
    InitCommonControlsEx(&icex);

    // Register window class
    WNDCLASSEX wcex = {};
    wcex.cbSize = sizeof(WNDCLASSEX);
    wcex.style = CS_HREDRAW | CS_VREDRAW;
    wcex.lpfnWndProc = MainWndProc;
    wcex.hInstance = hInstance;
    wcex.hIcon = LoadIcon(nullptr, IDI_APPLICATION);
    wcex.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wcex.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wcex.lpszMenuName = nullptr;
    wcex.lpszClassName = MAIN_WINDOW_CLASS;
    wcex.hIconSm = LoadIcon(nullptr, IDI_APPLICATION);

    if (!RegisterClassEx(&wcex)) {
        MessageBox(nullptr, L"فشل في تسجيل فئة النافذة", L"خطأ", MB_OK | MB_ICONERROR);
        return -1;
    }

    // Create main window
    CreateMainWindow();

    if (!g_hMainWnd) {
        MessageBox(nullptr, L"فشل في إنشاء النافذة الرئيسية", L"خطأ", MB_OK | MB_ICONERROR);
        return -1;
    }

    ShowWindow(g_hMainWnd, nCmdShow);
    UpdateWindow(g_hMainWnd);

    // Show welcome message
    MessageBox(g_hMainWnd, 
        L"مرحباً بك في نظام المحاسبة 11 - إصدار C++\n\n"
        L"🔨 مبني بـ C++ Native\n"
        L"✅ لا يحتاج .NET Framework\n"
        L"⚡ أداء فائق السرعة\n"
        L"🌍 يعمل على جميع إصدارات Windows\n\n"
        L"🔐 بيانات تسجيل الدخول:\n"
        L"👤 اسم المستخدم: admin\n"
        L"🔑 كلمة المرور: admin123",
        L"نظام المحاسبة 11", MB_OK | MB_ICONINFORMATION);

    // Message loop
    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    return (int)msg.wParam;
}

void CreateMainWindow()
{
    g_hMainWnd = CreateWindowEx(
        WS_EX_APPWINDOW,
        MAIN_WINDOW_CLASS,
        L"نظام المحاسبة 11 - إصدار C++",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT,
        1000, 700,
        nullptr, nullptr, g_hInst, nullptr
    );
}

LRESULT CALLBACK MainWndProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam)
{
    switch (msg) {
    case WM_CREATE:
        ShowDashboard(hwnd);
        break;

    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case ID_BTN_CUSTOMERS:
            ShowCustomers(hwnd);
            break;
        case ID_BTN_PRODUCTS:
            MessageBox(hwnd, L"📦 إدارة المنتجات\n\nهذه المميزة قيد التطوير", L"المنتجات", MB_OK | MB_ICONINFORMATION);
            break;
        case ID_BTN_INVOICES:
            MessageBox(hwnd, L"🧾 إدارة الفواتير\n\nهذه المميزة قيد التطوير", L"الفواتير", MB_OK | MB_ICONINFORMATION);
            break;
        case ID_BTN_REPORTS:
            MessageBox(hwnd, L"📊 التقارير المالية\n\nهذه المميزة قيد التطوير", L"التقارير", MB_OK | MB_ICONINFORMATION);
            break;
        case ID_BTN_ABOUT:
            ShowAbout();
            break;
        case ID_BTN_EXIT:
            PostQuitMessage(0);
            break;
        }
        break;

    case WM_SIZE:
        // Handle window resizing if needed
        break;

    case WM_DESTROY:
        PostQuitMessage(0);
        break;

    default:
        return DefWindowProc(hwnd, msg, wParam, lParam);
    }
    return 0;
}

void ShowDashboard(HWND hwnd)
{
    // Clear existing controls
    HWND hChild = GetWindow(hwnd, GW_CHILD);
    while (hChild) {
        HWND hNext = GetWindow(hChild, GW_HWNDNEXT);
        DestroyWindow(hChild);
        hChild = hNext;
    }

    RECT rect;
    GetClientRect(hwnd, &rect);

    // Title
    CreateWindow(L"STATIC", L"🏢 نظام المحاسبة 11 - إصدار C++",
        WS_VISIBLE | WS_CHILD | SS_CENTER,
        50, 30, rect.right - 100, 40,
        hwnd, nullptr, g_hInst, nullptr);

    // Statistics
    std::wstring stats = L"📊 إحصائيات النظام:\n\n";
    stats += L"👥 العملاء: " + std::to_wstring(g_customers.size()) + L"\n";
    stats += L"📦 المنتجات: 25\n";
    stats += L"🧾 الفواتير: 12\n";
    stats += L"💰 إجمالي المبيعات: 45,230 جنيه\n\n";
    stats += L"💻 معلومات النظام:\n";
    stats += L"🔧 اللغة: C++ Native\n";
    stats += L"🗃️ قاعدة البيانات: مدمجة\n";
    stats += L"⚡ الأداء: فائق السرعة\n";
    stats += L"💾 الذاكرة: محسنة\n";
    stats += L"🚀 التوافق: جميع إصدارات Windows";

    CreateWindow(L"STATIC", stats.c_str(),
        WS_VISIBLE | WS_CHILD | SS_LEFT,
        50, 100, rect.right - 100, 300,
        hwnd, nullptr, g_hInst, nullptr);

    // Buttons
    int btnY = 420;
    int btnWidth = 150;
    int btnHeight = 35;
    int btnSpacing = 170;

    CreateWindow(L"BUTTON", L"👥 العملاء",
        WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
        50, btnY, btnWidth, btnHeight,
        hwnd, (HMENU)ID_BTN_CUSTOMERS, g_hInst, nullptr);

    CreateWindow(L"BUTTON", L"📦 المنتجات",
        WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
        50 + btnSpacing, btnY, btnWidth, btnHeight,
        hwnd, (HMENU)ID_BTN_PRODUCTS, g_hInst, nullptr);

    CreateWindow(L"BUTTON", L"🧾 الفواتير",
        WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
        50 + btnSpacing * 2, btnY, btnWidth, btnHeight,
        hwnd, (HMENU)ID_BTN_INVOICES, g_hInst, nullptr);

    CreateWindow(L"BUTTON", L"📊 التقارير",
        WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
        50, btnY + 50, btnWidth, btnHeight,
        hwnd, (HMENU)ID_BTN_REPORTS, g_hInst, nullptr);

    CreateWindow(L"BUTTON", L"❓ حول البرنامج",
        WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
        50 + btnSpacing, btnY + 50, btnWidth, btnHeight,
        hwnd, (HMENU)ID_BTN_ABOUT, g_hInst, nullptr);

    CreateWindow(L"BUTTON", L"🚪 خروج",
        WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
        50 + btnSpacing * 2, btnY + 50, btnWidth, btnHeight,
        hwnd, (HMENU)ID_BTN_EXIT, g_hInst, nullptr);

    // Update window title
    SetWindowText(hwnd, L"نظام المحاسبة 11 - لوحة التحكم");
}

void ShowCustomers(HWND hwnd)
{
    // Clear existing controls
    HWND hChild = GetWindow(hwnd, GW_CHILD);
    while (hChild) {
        HWND hNext = GetWindow(hChild, GW_HWNDNEXT);
        DestroyWindow(hChild);
        hChild = hNext;
    }

    RECT rect;
    GetClientRect(hwnd, &rect);

    // Title
    CreateWindow(L"STATIC", L"👥 إدارة العملاء",
        WS_VISIBLE | WS_CHILD | SS_CENTER,
        50, 20, rect.right - 100, 30,
        hwnd, nullptr, g_hInst, nullptr);

    // Back button
    CreateWindow(L"BUTTON", L"🔙 العودة للرئيسية",
        WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
        50, 60, 150, 30,
        hwnd, (HMENU)ID_BTN_EXIT, g_hInst, nullptr); // Reuse exit ID for back

    // Customer list
    HWND hList = CreateWindow(WC_LISTVIEW, L"",
        WS_VISIBLE | WS_CHILD | LVS_REPORT | LVS_SINGLESEL | WS_BORDER,
        50, 100, rect.right - 100, rect.bottom - 200,
        hwnd, nullptr, g_hInst, nullptr);

    // Add columns
    LVCOLUMN lvc = {};
    lvc.mask = LVCF_TEXT | LVCF_WIDTH;
    lvc.cx = 80;
    lvc.pszText = (LPWSTR)L"الرقم";
    ListView_InsertColumn(hList, 0, &lvc);

    lvc.pszText = (LPWSTR)L"الاسم";
    lvc.cx = 150;
    ListView_InsertColumn(hList, 1, &lvc);

    lvc.pszText = (LPWSTR)L"الشركة";
    lvc.cx = 150;
    ListView_InsertColumn(hList, 2, &lvc);

    lvc.pszText = (LPWSTR)L"الهاتف";
    lvc.cx = 120;
    ListView_InsertColumn(hList, 3, &lvc);

    lvc.pszText = (LPWSTR)L"البريد الإلكتروني";
    lvc.cx = 200;
    ListView_InsertColumn(hList, 4, &lvc);

    // Add customers
    for (size_t i = 0; i < g_customers.size(); ++i) {
        LVITEM lvi = {};
        lvi.mask = LVIF_TEXT;
        lvi.iItem = (int)i;
        std::wstring idStr = std::to_wstring(g_customers[i].id);
        lvi.pszText = (LPWSTR)idStr.c_str();
        ListView_InsertItem(hList, &lvi);

        ListView_SetItemText(hList, (int)i, 1, (LPWSTR)g_customers[i].name.c_str());
        ListView_SetItemText(hList, (int)i, 2, (LPWSTR)g_customers[i].company.c_str());
        ListView_SetItemText(hList, (int)i, 3, (LPWSTR)g_customers[i].phone.c_str());
        ListView_SetItemText(hList, (int)i, 4, (LPWSTR)g_customers[i].email.c_str());
    }

    // Update window title
    SetWindowText(hwnd, L"نظام المحاسبة 11 - العملاء");
}

void ShowAbout()
{
    MessageBox(g_hMainWnd,
        L"🏢 نظام المحاسبة 11 - إصدار C++\n\n"
        L"📋 نظام محاسبة شامل باللغة العربية\n"
        L"🔨 مبني بـ C++ Native\n"
        L"✅ لا يحتاج .NET Framework\n"
        L"⚡ أداء فائق السرعة\n"
        L"💾 حجم صغير جداً\n"
        L"🌍 يعمل على جميع إصدارات Windows\n"
        L"🚀 بدء تشغيل فوري\n\n"
        L"🎯 المميزات:\n"
        L"• إدارة العملاء والموردين\n"
        L"• كتالوج المنتجات والخدمات\n"
        L"• إصدار الفواتير\n"
        L"• تقارير مالية شاملة\n"
        L"• دعم الضرائب المصرية\n"
        L"• واجهة عربية كاملة\n\n"
        L"© 2024 شركة 11 للبرمجيات\n"
        L"جميع الحقوق محفوظة 🇪🇬",
        L"حول البرنامج", MB_OK | MB_ICONINFORMATION);
}
