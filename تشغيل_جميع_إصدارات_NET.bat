@echo off
title نظام المحاسبة 11 - يدعم جميع إصدارات .NET
color 0A
echo.
echo ========================================================
echo    نظام المحاسبة 11 - يدعم جميع إصدارات .NET
echo ========================================================
echo.

echo 🔄 النظام الأصلي مع دعم شامل لجميع إصدارات .NET!
echo ✅ .NET Framework 4.8
echo ✅ .NET 6.0
echo ✅ .NET 7.0  
echo ✅ .NET 8.0
echo 🗃️ قاعدة بيانات SQL Server متقدمة
echo 🎨 واجهة WPF حديثة
echo.

echo [1] التحقق من إصدارات .NET المتاحة...

REM التحقق من .NET Framework 4.8
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" /v Release >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ .NET Framework 4.8 متوفر
    set "NET48_AVAILABLE=true"
) else (
    echo ⚠️  .NET Framework 4.8 غير متوفر
    set "NET48_AVAILABLE=false"
)

REM التحقق من .NET Core/.NET 5+
dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ .NET Core/.NET متوفر
    for /f "tokens=*" %%i in ('dotnet --version') do echo    الإصدار: %%i
    set "DOTNET_AVAILABLE=true"
) else (
    echo ⚠️  .NET Core/.NET غير متوفر
    set "DOTNET_AVAILABLE=false"
)

echo.
echo [2] اختيار إصدار .NET للبناء...

if "%DOTNET_AVAILABLE%"=="true" (
    echo 🎯 سيتم استخدام .NET الحديث (مُوصى به)
    set "TARGET_FRAMEWORK=net8.0-windows"
    set "BUILD_COMMAND=dotnet build -c Release -f net8.0-windows"
) else if "%NET48_AVAILABLE%"=="true" (
    echo 🎯 سيتم استخدام .NET Framework 4.8
    set "TARGET_FRAMEWORK=net48"
    set "BUILD_COMMAND=dotnet build -c Release -f net48"
) else (
    echo ❌ لا يوجد إصدار .NET متوفر
    echo.
    echo 📥 يرجى تثبيت أحد التالي:
    echo 1. .NET 8.0 (الأحدث): https://dotnet.microsoft.com/download/dotnet/8.0
    echo 2. .NET 6.0 (LTS): https://dotnet.microsoft.com/download/dotnet/6.0
    echo 3. .NET Framework 4.8: https://dotnet.microsoft.com/download/dotnet-framework/net48
    echo.
    pause
    exit /b 1
)

echo.
echo [3] التحقق من SQL Server...
sqlcmd -S "(localdb)\MSSQLLocalDB" -Q "SELECT @@VERSION" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ LocalDB متوفر
    set "DB_AVAILABLE=true"
) else (
    sqlcmd -S ".\SQLEXPRESS" -Q "SELECT @@VERSION" >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ SQL Server Express متوفر
        set "DB_AVAILABLE=true"
    ) else (
        echo ⚠️  SQL Server غير متوفر - سيتم استخدام قاعدة بيانات مؤقتة
        set "DB_AVAILABLE=false"
    )
)

echo.
echo [4] بناء المشروع...
echo 🔨 الهدف: %TARGET_FRAMEWORK%
echo 🔨 الأمر: %BUILD_COMMAND%

%BUILD_COMMAND%

if %errorlevel% equ 0 (
    echo ✅ تم بناء المشروع بنجاح!
) else (
    echo ❌ فشل بناء المشروع
    echo.
    echo 🔧 الحلول المحتملة:
    echo 1. تأكد من تثبيت .NET SDK (وليس فقط Runtime)
    echo 2. شغل Command Prompt كـ Administrator
    echo 3. تأكد من وجود ملف AccountingSystem11.csproj
    echo 4. جرب: dotnet restore
    echo.
    pause
    exit /b 1
)

echo.
echo [5] تشغيل البرنامج...

if "%TARGET_FRAMEWORK%"=="net8.0-windows" (
    set "EXE_PATH=bin\Release\net8.0-windows\AccountingSystem11.exe"
) else if "%TARGET_FRAMEWORK%"=="net7.0-windows" (
    set "EXE_PATH=bin\Release\net7.0-windows\AccountingSystem11.exe"
) else if "%TARGET_FRAMEWORK%"=="net6.0-windows" (
    set "EXE_PATH=bin\Release\net6.0-windows\AccountingSystem11.exe"
) else (
    set "EXE_PATH=bin\Release\net48\AccountingSystem11.exe"
)

if exist "%EXE_PATH%" (
    echo ✅ تم العثور على الملف التنفيذي: %EXE_PATH%
    echo.
    echo 🚀 جاري تشغيل نظام المحاسبة 11...
    echo.
    echo 🔐 بيانات تسجيل الدخول:
    echo 👤 اسم المستخدم: admin
    echo 🔑 كلمة المرور: admin123
    echo.
    echo 🎯 المميزات المتاحة:
    echo    • إدارة العملاء والموردين
    echo    • كتالوج المنتجات
    echo    • إصدار الفواتير
    echo    • تقارير مالية شاملة
    echo    • دعم الضرائب المصرية
    echo    • الفاتورة الإلكترونية
    echo    • تصدير Excel
    echo    • واجهة عربية كاملة
    echo.
    
    start "" "%EXE_PATH%"
    
    echo ✅ تم تشغيل البرنامج
    echo 💡 إذا لم تظهر النافذة، تحقق من Task Manager
    
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    echo المسار المتوقع: %EXE_PATH%
    echo.
    echo 🔍 البحث عن ملفات .exe أخرى...
    for /r "bin" %%f in (*.exe) do (
        echo وُجد: %%f
        set "FOUND_EXE=%%f"
    )
    
    if defined FOUND_EXE (
        echo.
        echo 🚀 جاري تشغيل: %FOUND_EXE%
        start "" "%FOUND_EXE%"
    ) else (
        echo ❌ لم يتم العثور على أي ملف .exe
        echo يرجى التحقق من نجاح عملية البناء
    )
)

echo.
echo 📊 معلومات النظام:
echo ┌─────────────────────────────────────────────────────────┐
echo │ نظام المحاسبة 11 - إصدار متعدد .NET                    │
echo ├─────────────────────────────────────────────────────────┤
echo │ الهدف: %TARGET_FRAMEWORK%                               │
echo │ قاعدة البيانات: SQL Server                             │
echo │ الواجهة: WPF مع Material Design                        │
echo │ اللغة: العربية مع دعم Unicode                          │
echo │ المميزات: شاملة ومتقدمة                               │
echo └─────────────────────────────────────────────────────────┘
echo.

echo 💡 نصائح للاستخدام الأمثل:
echo    • استخدم .NET 8.0 للحصول على أفضل أداء
echo    • تأكد من تشغيل SQL Server للحصول على جميع المميزات
echo    • قم بعمل نسخة احتياطية من قاعدة البيانات دورياً
echo    • استخدم الفاتورة الإلكترونية للامتثال للقوانين المصرية
echo.

echo 🎉 مقارنة الإصدارات:
echo ┌─────────────────┬──────────┬──────────┬──────────┬──────────┐
echo │ المعيار         │ NET 4.8  │ NET 6.0  │ NET 7.0  │ NET 8.0  │
echo ├─────────────────┼──────────┼──────────┼──────────┼──────────┤
echo │ الأداء          │ ⭐⭐⭐     │ ⭐⭐⭐⭐   │ ⭐⭐⭐⭐   │ ⭐⭐⭐⭐⭐  │
echo │ الأمان          │ ⭐⭐⭐     │ ⭐⭐⭐⭐⭐  │ ⭐⭐⭐⭐⭐  │ ⭐⭐⭐⭐⭐  │
echo │ المميزات        │ ⭐⭐⭐⭐   │ ⭐⭐⭐⭐⭐  │ ⭐⭐⭐⭐⭐  │ ⭐⭐⭐⭐⭐  │
echo │ التوافق         │ ⭐⭐⭐⭐⭐  │ ⭐⭐⭐⭐   │ ⭐⭐⭐⭐   │ ⭐⭐⭐⭐   │
echo │ الدعم           │ ⭐⭐⭐     │ ⭐⭐⭐⭐⭐  │ ⭐⭐⭐     │ ⭐⭐⭐⭐⭐  │
echo └─────────────────┴──────────┴──────────┴──────────┴──────────┘
echo.

pause
