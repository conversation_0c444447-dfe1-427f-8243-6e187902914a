using AccountingSystem11.Data;
using AccountingSystem11.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AccountingSystem11.Services
{
    /// <summary>
    /// Customer service implementation
    /// </summary>
    public class CustomerService : ICustomerService
    {
        private readonly AccountingDbContext _context;

        public CustomerService(AccountingDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Customer>> GetAllCustomersAsync()
        {
            return await _context.Customers
                .Where(c => c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<Customer> GetCustomerByIdAsync(int id)
        {
            return await _context.Customers
                .Include(c => c.Invoices)
                .Include(c => c.Transactions)
                .FirstOrDefaultAsync(c => c.Id == id);
        }

        public async Task<Customer> GetCustomerByTaxNumberAsync(string taxNumber)
        {
            if (string.IsNullOrWhiteSpace(taxNumber))
                return null;

            return await _context.Customers
                .FirstOrDefaultAsync(c => c.TaxNumber == taxNumber);
        }

        public async Task<Customer> CreateCustomerAsync(Customer customer)
        {
            if (customer == null)
                throw new ArgumentNullException(nameof(customer));

            // Validate unique constraints
            if (!string.IsNullOrWhiteSpace(customer.TaxNumber))
            {
                var existingTaxNumber = await TaxNumberExistsAsync(customer.TaxNumber);
                if (existingTaxNumber)
                    throw new InvalidOperationException("الرقم الضريبي موجود بالفعل");
            }

            if (!string.IsNullOrWhiteSpace(customer.Email))
            {
                var existingEmail = await EmailExistsAsync(customer.Email);
                if (existingEmail)
                    throw new InvalidOperationException("البريد الإلكتروني موجود بالفعل");
            }

            customer.CreatedAt = DateTime.Now;
            _context.Customers.Add(customer);
            await _context.SaveChangesAsync();

            return customer;
        }

        public async Task<Customer> UpdateCustomerAsync(Customer customer)
        {
            if (customer == null)
                throw new ArgumentNullException(nameof(customer));

            var existingCustomer = await _context.Customers.FindAsync(customer.Id);
            if (existingCustomer == null)
                throw new InvalidOperationException("العميل غير موجود");

            // Validate unique constraints
            if (!string.IsNullOrWhiteSpace(customer.TaxNumber))
            {
                var existingTaxNumber = await TaxNumberExistsAsync(customer.TaxNumber, customer.Id);
                if (existingTaxNumber)
                    throw new InvalidOperationException("الرقم الضريبي موجود بالفعل");
            }

            if (!string.IsNullOrWhiteSpace(customer.Email))
            {
                var existingEmail = await EmailExistsAsync(customer.Email, customer.Id);
                if (existingEmail)
                    throw new InvalidOperationException("البريد الإلكتروني موجود بالفعل");
            }

            // Update properties
            existingCustomer.Name = customer.Name;
            existingCustomer.CompanyName = customer.CompanyName;
            existingCustomer.Phone = customer.Phone;
            existingCustomer.Mobile = customer.Mobile;
            existingCustomer.Email = customer.Email;
            existingCustomer.Address = customer.Address;
            existingCustomer.City = customer.City;
            existingCustomer.Governorate = customer.Governorate;
            existingCustomer.PostalCode = customer.PostalCode;
            existingCustomer.TaxNumber = customer.TaxNumber;
            existingCustomer.CommercialRegNumber = customer.CommercialRegNumber;
            existingCustomer.NationalId = customer.NationalId;
            existingCustomer.CustomerType = customer.CustomerType;
            existingCustomer.CreditLimit = customer.CreditLimit;
            existingCustomer.PaymentTerms = customer.PaymentTerms;
            existingCustomer.DefaultDiscount = customer.DefaultDiscount;
            existingCustomer.Notes = customer.Notes;
            existingCustomer.IsActive = customer.IsActive;
            existingCustomer.Update();

            await _context.SaveChangesAsync();
            return existingCustomer;
        }

        public async Task<bool> DeleteCustomerAsync(int id)
        {
            var customer = await _context.Customers.FindAsync(id);
            if (customer == null)
                return false;

            // Check if customer has invoices
            var hasInvoices = await _context.Invoices.AnyAsync(i => i.CustomerId == id);
            if (hasInvoices)
                throw new InvalidOperationException("لا يمكن حذف العميل لوجود فواتير مرتبطة به");

            customer.Delete();
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> CustomerExistsAsync(int id)
        {
            return await _context.Customers.AnyAsync(c => c.Id == id);
        }

        public async Task<bool> TaxNumberExistsAsync(string taxNumber, int? excludeId = null)
        {
            if (string.IsNullOrWhiteSpace(taxNumber))
                return false;

            var query = _context.Customers.Where(c => c.TaxNumber == taxNumber);
            
            if (excludeId.HasValue)
                query = query.Where(c => c.Id != excludeId.Value);

            return await query.AnyAsync();
        }

        public async Task<bool> EmailExistsAsync(string email, int? excludeId = null)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            var query = _context.Customers.Where(c => c.Email == email);
            
            if (excludeId.HasValue)
                query = query.Where(c => c.Id != excludeId.Value);

            return await query.AnyAsync();
        }

        public async Task<IEnumerable<Customer>> SearchCustomersAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllCustomersAsync();

            searchTerm = searchTerm.Trim().ToLower();

            return await _context.Customers
                .Where(c => c.IsActive && 
                           (c.Name.ToLower().Contains(searchTerm) ||
                            c.CompanyName.ToLower().Contains(searchTerm) ||
                            c.Phone.Contains(searchTerm) ||
                            c.Mobile.Contains(searchTerm) ||
                            c.Email.ToLower().Contains(searchTerm) ||
                            c.TaxNumber.Contains(searchTerm)))
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<decimal> GetCustomerBalanceAsync(int customerId)
        {
            var customer = await _context.Customers.FindAsync(customerId);
            return customer?.Balance ?? 0;
        }

        public async Task<bool> UpdateCustomerBalanceAsync(int customerId, decimal amount)
        {
            var customer = await _context.Customers.FindAsync(customerId);
            if (customer == null)
                return false;

            customer.Balance += amount;
            customer.Update();
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<Customer>> GetCustomersWithOverdueInvoicesAsync()
        {
            var today = DateTime.Today;
            
            return await _context.Customers
                .Where(c => c.IsActive && 
                           c.Invoices.Any(i => i.DueDate < today && 
                                              i.PaymentStatus != PaymentStatus.Paid))
                .Include(c => c.Invoices)
                .ToListAsync();
        }

        public async Task<IEnumerable<Customer>> GetTopCustomersByRevenueAsync(int count = 10)
        {
            return await _context.Customers
                .Where(c => c.IsActive)
                .Select(c => new 
                {
                    Customer = c,
                    TotalRevenue = c.Invoices
                        .Where(i => i.InvoiceType == InvoiceType.Sales && 
                                   i.Status == InvoiceStatus.Approved)
                        .Sum(i => i.TotalAmount)
                })
                .OrderByDescending(x => x.TotalRevenue)
                .Take(count)
                .Select(x => x.Customer)
                .ToListAsync();
        }
    }
}
