using AccountingSystem11.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AccountingSystem11.Services
{
    /// <summary>
    /// Interface for user management operations
    /// </summary>
    public interface IUserService
    {
        Task<User> AuthenticateAsync(string username, string password);
        Task<User> GetUserByIdAsync(int id);
        Task<User> GetUserByUsernameAsync(string username);
        Task<User> CreateUserAsync(User user, string password);
        Task<User> UpdateUserAsync(User user);
        Task<bool> DeleteUserAsync(int id);
        Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword);
        Task<bool> ResetPasswordAsync(int userId, string newPassword);
        Task<bool> LockUserAsync(int userId, int lockDurationMinutes = 30);
        Task<bool> UnlockUserAsync(int userId);
        Task<IEnumerable<User>> GetAllUsersAsync();
        Task<IEnumerable<User>> GetActiveUsersAsync();
        Task<bool> UserExistsAsync(string username);
        Task<bool> EmailExistsAsync(string email, int? excludeId = null);
        Task<bool> HasPermissionAsync(int userId, Permission permission);
        Task<bool> GrantPermissionAsync(int userId, Permission permission);
        Task<bool> RevokePermissionAsync(int userId, Permission permission);
        Task<IEnumerable<Permission>> GetUserPermissionsAsync(int userId);
        Task<UserSession> CreateSessionAsync(int userId, string ipAddress, string userAgent);
        Task<bool> EndSessionAsync(string sessionToken);
        Task<bool> IsSessionValidAsync(string sessionToken);
        Task<User> GetUserBySessionTokenAsync(string sessionToken);
    }
}
