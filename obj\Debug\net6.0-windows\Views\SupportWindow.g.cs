﻿#pragma checksum "..\..\..\..\Views\SupportWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DC0D2A28E0ACEE3725FC1B2E00D686CA765C209B"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingSystem11.Views {
    
    
    /// <summary>
    /// SupportWindow
    /// </summary>
    public partial class SupportWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 35 "..\..\..\..\Views\SupportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox SupportOptionsListBox;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\Views\SupportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel MainContentPanel;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\Views\SupportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel FAQPanel;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\Views\SupportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel TicketPanel;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\..\Views\SupportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TicketSubjectTextBox;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\Views\SupportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TicketPriorityComboBox;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\..\Views\SupportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TicketDescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\..\Views\SupportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SendTicketButton;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\Views\SupportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel SystemInfoPanel;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\..\Views\SupportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SystemInfoText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingSystem11;V1.0.0.0;component/views/supportwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\SupportWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SupportOptionsListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 38 "..\..\..\..\Views\SupportWindow.xaml"
            this.SupportOptionsListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SupportOptionsListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.MainContentPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 3:
            this.FAQPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 4:
            this.TicketPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 5:
            this.TicketSubjectTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.TicketPriorityComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.TicketDescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.SendTicketButton = ((System.Windows.Controls.Button)(target));
            
            #line 220 "..\..\..\..\Views\SupportWindow.xaml"
            this.SendTicketButton.Click += new System.Windows.RoutedEventHandler(this.SendTicketButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.SystemInfoPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 10:
            this.SystemInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            
            #line 240 "..\..\..\..\Views\SupportWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopySystemInfoButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

