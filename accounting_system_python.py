#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام المحاسبة 11 - إصدار Python
لا يحتاج .NET - يعمل مع Python فقط
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import sqlite3
import os
from datetime import datetime
import hashlib

class AccountingSystem:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام المحاسبة 11 - إصدار Python")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f0f8ff')
        
        # إنشاء قاعدة البيانات
        self.init_database()
        
        # إنشاء الواجهة
        self.create_interface()
        
    def init_database(self):
        """إنشاء قاعدة بيانات SQLite"""
        self.db_path = "accounting_system.db"
        self.conn = sqlite3.connect(self.db_path)
        self.cursor = self.conn.cursor()
        
        # إنشاء الجداول
        self.create_tables()
        self.insert_initial_data()
        
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        
        # جدول المستخدمين
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                email TEXT,
                role TEXT DEFAULT 'user',
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول العملاء
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                company_name TEXT,
                phone TEXT,
                mobile TEXT,
                email TEXT,
                address TEXT,
                city TEXT,
                tax_number TEXT,
                customer_type TEXT DEFAULT 'individual',
                credit_limit REAL DEFAULT 0,
                balance REAL DEFAULT 0,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول فئات المنتجات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS product_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                code TEXT UNIQUE,
                description TEXT,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المنتجات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                category_id INTEGER,
                unit TEXT DEFAULT 'قطعة',
                purchase_price REAL DEFAULT 0,
                selling_price REAL DEFAULT 0,
                stock_quantity REAL DEFAULT 0,
                min_stock_level REAL DEFAULT 0,
                is_taxable INTEGER DEFAULT 1,
                tax_rate REAL DEFAULT 14.0,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES product_categories (id)
            )
        ''')
        
        # جدول الفواتير
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER,
                invoice_date DATE NOT NULL,
                due_date DATE,
                subtotal REAL DEFAULT 0,
                tax_amount REAL DEFAULT 0,
                discount_amount REAL DEFAULT 0,
                total_amount REAL DEFAULT 0,
                status TEXT DEFAULT 'draft',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        ''')
        
        self.conn.commit()
        
    def insert_initial_data(self):
        """إدخال البيانات الأولية"""
        
        # التحقق من وجود مستخدم مدير
        self.cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
        if self.cursor.fetchone()[0] == 0:
            # إنشاء مستخدم مدير
            password_hash = hashlib.sha256("admin123".encode()).hexdigest()
            self.cursor.execute('''
                INSERT INTO users (username, password_hash, full_name, email, role)
                VALUES ('admin', ?, 'مدير النظام', '<EMAIL>', 'admin')
            ''', (password_hash,))
            
        # إدخال فئات المنتجات الأساسية
        categories = [
            ('عام', 'GEN', 'فئة عامة للمنتجات'),
            ('ملابس', 'CLO', 'الملابس والأزياء'),
            ('أغذية', 'FOD', 'المواد الغذائية'),
            ('إلكترونيات', 'ELE', 'الأجهزة الإلكترونية'),
            ('أدوية', 'MED', 'الأدوية والمستحضرات الطبية')
        ]
        
        for name, code, desc in categories:
            self.cursor.execute('''
                INSERT OR IGNORE INTO product_categories (name, code, description)
                VALUES (?, ?, ?)
            ''', (name, code, desc))
            
        # إدخال عميل تجريبي
        self.cursor.execute("SELECT COUNT(*) FROM customers")
        if self.cursor.fetchone()[0] == 0:
            self.cursor.execute('''
                INSERT INTO customers (name, company_name, phone, mobile, email, address, city, customer_type)
                VALUES ('عميل تجريبي', 'شركة تجريبية', '02-12345678', '01012345678', 
                        '<EMAIL>', '123 شارع التجربة', 'القاهرة', 'company')
            ''')
            
        self.conn.commit()
        
    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        
        # الشريط العلوي
        header_frame = tk.Frame(self.root, bg='#4682b4', height=80)
        header_frame.pack(fill='x', padx=10, pady=5)
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(header_frame, text="🏢 نظام المحاسبة 11 - إصدار Python", 
                              font=('Arial', 20, 'bold'), fg='white', bg='#4682b4')
        title_label.pack(expand=True)
        
        subtitle_label = tk.Label(header_frame, text="لا يحتاج .NET - يعمل مع Python فقط", 
                                 font=('Arial', 12), fg='lightgray', bg='#4682b4')
        subtitle_label.pack()
        
        # الشريط الجانبي
        sidebar_frame = tk.Frame(self.root, bg='#f8f8f8', width=200)
        sidebar_frame.pack(side='left', fill='y', padx=5, pady=5)
        sidebar_frame.pack_propagate(False)
        
        # أزرار القائمة
        menu_buttons = [
            ("🏠 الرئيسية", self.show_dashboard),
            ("👥 العملاء", self.show_customers),
            ("📦 المنتجات", self.show_products),
            ("🧾 الفواتير", self.show_invoices),
            ("📊 التقارير", self.show_reports),
            ("⚙️ الإعدادات", self.show_settings),
            ("🚪 خروج", self.logout)
        ]
        
        for text, command in menu_buttons:
            btn = tk.Button(sidebar_frame, text=text, command=command,
                           font=('Arial', 12), width=18, pady=10,
                           bg='white', relief='flat', cursor='hand2')
            btn.pack(pady=2, padx=5, fill='x')
            
        # المنطقة الرئيسية
        self.main_frame = tk.Frame(self.root, bg='white')
        self.main_frame.pack(side='right', fill='both', expand=True, padx=5, pady=5)
        
        # عرض لوحة التحكم افتراضياً
        self.show_dashboard()
        
    def clear_main_frame(self):
        """مسح المحتوى الرئيسي"""
        for widget in self.main_frame.winfo_children():
            widget.destroy()
            
    def show_dashboard(self):
        """عرض لوحة التحكم"""
        self.clear_main_frame()
        
        # عنوان الصفحة
        title = tk.Label(self.main_frame, text="📊 لوحة التحكم", 
                        font=('Arial', 18, 'bold'), bg='white')
        title.pack(pady=20)
        
        # إحصائيات سريعة
        stats_frame = tk.Frame(self.main_frame, bg='white')
        stats_frame.pack(pady=20)
        
        # عدد العملاء
        self.cursor.execute("SELECT COUNT(*) FROM customers WHERE is_active = 1")
        customers_count = self.cursor.fetchone()[0]
        
        # عدد المنتجات
        self.cursor.execute("SELECT COUNT(*) FROM products WHERE is_active = 1")
        products_count = self.cursor.fetchone()[0]
        
        # عدد الفواتير
        self.cursor.execute("SELECT COUNT(*) FROM invoices")
        invoices_count = self.cursor.fetchone()[0]
        
        stats = [
            ("👥 العملاء", customers_count, '#4CAF50'),
            ("📦 المنتجات", products_count, '#2196F3'),
            ("🧾 الفواتير", invoices_count, '#FF9800')
        ]
        
        for i, (label, count, color) in enumerate(stats):
            stat_frame = tk.Frame(stats_frame, bg=color, width=200, height=100)
            stat_frame.grid(row=0, column=i, padx=20, pady=10)
            stat_frame.pack_propagate(False)
            
            tk.Label(stat_frame, text=label, font=('Arial', 14, 'bold'), 
                    fg='white', bg=color).pack(pady=10)
            tk.Label(stat_frame, text=str(count), font=('Arial', 24, 'bold'), 
                    fg='white', bg=color).pack()
                    
        # معلومات النظام
        info_frame = tk.Frame(self.main_frame, bg='#f0f0f0', relief='ridge', bd=2)
        info_frame.pack(pady=30, padx=50, fill='x')
        
        info_text = f"""
🎉 مرحباً بك في نظام المحاسبة 11 - إصدار Python

✅ المميزات:
• لا يحتاج .NET Framework
• يعمل مع Python فقط
• قاعدة بيانات SQLite محلية
• واجهة عربية بسيطة
• سريع وخفيف

📊 إحصائيات النظام:
• العملاء: {customers_count}
• المنتجات: {products_count}
• الفواتير: {invoices_count}

🔐 بيانات تسجيل الدخول:
• اسم المستخدم: admin
• كلمة المرور: admin123

📁 قاعدة البيانات: {self.db_path}
🕒 آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        tk.Label(info_frame, text=info_text, font=('Arial', 11), 
                justify='right', bg='#f0f0f0', fg='#333').pack(pady=20, padx=20)
        
    def show_customers(self):
        """عرض صفحة العملاء"""
        self.clear_main_frame()
        
        title = tk.Label(self.main_frame, text="👥 إدارة العملاء", 
                        font=('Arial', 18, 'bold'), bg='white')
        title.pack(pady=20)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(self.main_frame, bg='white')
        buttons_frame.pack(pady=10)
        
        tk.Button(buttons_frame, text="➕ إضافة عميل جديد", 
                 command=self.add_customer, bg='#4CAF50', fg='white',
                 font=('Arial', 12), padx=20, pady=5).pack(side='left', padx=5)
        
        tk.Button(buttons_frame, text="🔄 تحديث القائمة", 
                 command=self.show_customers, bg='#2196F3', fg='white',
                 font=('Arial', 12), padx=20, pady=5).pack(side='left', padx=5)
        
        # جدول العملاء
        tree_frame = tk.Frame(self.main_frame, bg='white')
        tree_frame.pack(pady=20, padx=20, fill='both', expand=True)
        
        columns = ('ID', 'الاسم', 'الشركة', 'الهاتف', 'البريد الإلكتروني', 'المدينة')
        tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)
            
        # إضافة البيانات
        self.cursor.execute('''
            SELECT id, name, company_name, phone, email, city 
            FROM customers WHERE is_active = 1 ORDER BY name
        ''')
        
        for row in self.cursor.fetchall():
            tree.insert('', 'end', values=row)
            
        tree.pack(side='left', fill='both', expand=True)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(tree_frame, orient='vertical', command=tree.yview)
        scrollbar.pack(side='right', fill='y')
        tree.configure(yscrollcommand=scrollbar.set)
        
    def add_customer(self):
        """إضافة عميل جديد"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة عميل جديد")
        dialog.geometry("400x500")
        dialog.configure(bg='white')
        
        # حقول الإدخال
        fields = [
            ("الاسم:", "name"),
            ("اسم الشركة:", "company_name"),
            ("الهاتف:", "phone"),
            ("الموبايل:", "mobile"),
            ("البريد الإلكتروني:", "email"),
            ("العنوان:", "address"),
            ("المدينة:", "city"),
            ("الرقم الضريبي:", "tax_number")
        ]
        
        entries = {}
        
        for i, (label, field) in enumerate(fields):
            tk.Label(dialog, text=label, font=('Arial', 12), bg='white').grid(
                row=i, column=0, sticky='e', padx=10, pady=5)
            
            entry = tk.Entry(dialog, font=('Arial', 12), width=25)
            entry.grid(row=i, column=1, padx=10, pady=5)
            entries[field] = entry
            
        # نوع العميل
        tk.Label(dialog, text="نوع العميل:", font=('Arial', 12), bg='white').grid(
            row=len(fields), column=0, sticky='e', padx=10, pady=5)
        
        customer_type = ttk.Combobox(dialog, values=['individual', 'company'], 
                                   font=('Arial', 12), width=22)
        customer_type.set('individual')
        customer_type.grid(row=len(fields), column=1, padx=10, pady=5)
        
        def save_customer():
            try:
                data = {field: entry.get() for field, entry in entries.items()}
                data['customer_type'] = customer_type.get()
                
                if not data['name']:
                    messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
                    return
                    
                self.cursor.execute('''
                    INSERT INTO customers (name, company_name, phone, mobile, email, 
                                         address, city, tax_number, customer_type)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (data['name'], data['company_name'], data['phone'], 
                     data['mobile'], data['email'], data['address'], 
                     data['city'], data['tax_number'], data['customer_type']))
                
                self.conn.commit()
                messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")
                dialog.destroy()
                self.show_customers()  # تحديث القائمة
                
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
        
        # أزرار الحفظ والإلغاء
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.grid(row=len(fields)+2, column=0, columnspan=2, pady=20)
        
        tk.Button(buttons_frame, text="💾 حفظ", command=save_customer,
                 bg='#4CAF50', fg='white', font=('Arial', 12), padx=20).pack(side='left', padx=10)
        
        tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                 bg='#f44336', fg='white', font=('Arial', 12), padx=20).pack(side='left', padx=10)
        
    def show_products(self):
        """عرض صفحة المنتجات"""
        self.clear_main_frame()
        
        title = tk.Label(self.main_frame, text="📦 إدارة المنتجات", 
                        font=('Arial', 18, 'bold'), bg='white')
        title.pack(pady=20)
        
        info_label = tk.Label(self.main_frame, text="قريباً... صفحة إدارة المنتجات", 
                             font=('Arial', 14), bg='white', fg='gray')
        info_label.pack(pady=50)
        
    def show_invoices(self):
        """عرض صفحة الفواتير"""
        self.clear_main_frame()
        
        title = tk.Label(self.main_frame, text="🧾 إدارة الفواتير", 
                        font=('Arial', 18, 'bold'), bg='white')
        title.pack(pady=20)
        
        info_label = tk.Label(self.main_frame, text="قريباً... صفحة إدارة الفواتير", 
                             font=('Arial', 14), bg='white', fg='gray')
        info_label.pack(pady=50)
        
    def show_reports(self):
        """عرض صفحة التقارير"""
        self.clear_main_frame()
        
        title = tk.Label(self.main_frame, text="📊 التقارير", 
                        font=('Arial', 18, 'bold'), bg='white')
        title.pack(pady=20)
        
        info_label = tk.Label(self.main_frame, text="قريباً... صفحة التقارير", 
                             font=('Arial', 14), bg='white', fg='gray')
        info_label.pack(pady=50)
        
    def show_settings(self):
        """عرض صفحة الإعدادات"""
        self.clear_main_frame()
        
        title = tk.Label(self.main_frame, text="⚙️ الإعدادات", 
                        font=('Arial', 18, 'bold'), bg='white')
        title.pack(pady=20)
        
        info_label = tk.Label(self.main_frame, text="قريباً... صفحة الإعدادات", 
                             font=('Arial', 14), bg='white', fg='gray')
        info_label.pack(pady=50)
        
    def logout(self):
        """تسجيل الخروج"""
        if messagebox.askyesno("تأكيد", "هل تريد الخروج من البرنامج؟"):
            self.conn.close()
            self.root.quit()
            
    def run(self):
        """تشغيل البرنامج"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        app = AccountingSystem()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل البرنامج: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
