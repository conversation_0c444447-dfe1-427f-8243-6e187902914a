using AccountingSystem11.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AccountingSystem11.Services
{
    /// <summary>
    /// Interface for invoice service operations
    /// </summary>
    public interface IInvoiceService
    {
        Task<IEnumerable<Invoice>> GetAllInvoicesAsync();
        Task<Invoice> GetInvoiceByIdAsync(int id);
        Task<Invoice> GetInvoiceByNumberAsync(string invoiceNumber);
        Task<Invoice> CreateInvoiceAsync(Invoice invoice);
        Task<Invoice> UpdateInvoiceAsync(Invoice invoice);
        Task<bool> DeleteInvoiceAsync(int id);
        Task<bool> CancelInvoiceAsync(int id, string reason);
        Task<bool> InvoiceExistsAsync(int id);
        Task<bool> InvoiceNumberExistsAsync(string invoiceNumber, int? excludeId = null);
        Task<IEnumerable<Invoice>> GetInvoicesByCustomerAsync(int customerId);
        Task<IEnumerable<Invoice>> GetInvoicesByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<Invoice>> GetInvoicesByStatusAsync(InvoiceStatus status);
        Task<IEnumerable<Invoice>> GetInvoicesByPaymentStatusAsync(PaymentStatus paymentStatus);
        Task<IEnumerable<Invoice>> GetOverdueInvoicesAsync();
        Task<IEnumerable<Invoice>> SearchInvoicesAsync(string searchTerm);
        Task<string> GenerateNextInvoiceNumberAsync(InvoiceType invoiceType);
        Task<bool> AddPaymentAsync(int invoiceId, InvoicePayment payment);
        Task<bool> RemovePaymentAsync(int paymentId);
        Task<decimal> GetTotalSalesAsync(DateTime? startDate = null, DateTime? endDate = null);
        Task<decimal> GetTotalTaxAsync(DateTime? startDate = null, DateTime? endDate = null);
        Task<int> GetInvoiceCountAsync(DateTime? startDate = null, DateTime? endDate = null);
        Task<IEnumerable<Invoice>> GetRecentInvoicesAsync(int count = 10);
    }
}
