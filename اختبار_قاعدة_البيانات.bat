@echo off
title اختبار قاعدة البيانات - نظام المحاسبة 11
color 0A
echo.
echo ========================================================
echo        اختبار قاعدة البيانات - نظام المحاسبة 11
echo ========================================================
echo.
echo هذا الملف سيختبر قاعدة البيانات للتأكد من عملها
echo.

echo [1] التحقق من وجود مجلد البيانات...
if exist "Data" (
    echo ✅ مجلد البيانات موجود
) else (
    echo ❌ مجلد البيانات غير موجود
    echo 📁 جاري إنشاء مجلد البيانات...
    mkdir "Data"
    echo ✅ تم إنشاء مجلد البيانات
)

echo.
echo [2] التحقق من ملف قاعدة البيانات...
if exist "Data\AccountingSystem11.accdb" (
    echo ✅ ملف قاعدة البيانات موجود
    
    REM عرض معلومات الملف
    for %%A in ("Data\AccountingSystem11.accdb") do (
        echo 📊 حجم الملف: %%~zA بايت
        echo 📅 تاريخ التعديل: %%~tA
    )
) else (
    echo ⚠️  ملف قاعدة البيانات غير موجود
    echo سيتم إنشاؤه عند تشغيل البرنامج لأول مرة
)

echo.
echo [3] اختبار الاتصال بقاعدة البيانات...

REM إنشاء ملف VBS لاختبار الاتصال مع Access 2016+
echo On Error Resume Next > test_access_2016.vbs
echo. >> test_access_2016.vbs
echo ' اختبار Provider الجديد أولاً >> test_access_2016.vbs
echo Set conn = CreateObject("ADODB.Connection") >> test_access_2016.vbs
echo conn.Open "Provider=Microsoft.ACE.OLEDB.16.0;Data Source=test_2016.accdb;" >> test_access_2016.vbs
echo. >> test_access_2016.vbs
echo If Err.Number = 0 Then >> test_access_2016.vbs
echo     WScript.Echo "SUCCESS_16" >> test_access_2016.vbs
echo     conn.Close >> test_access_2016.vbs
echo Else >> test_access_2016.vbs
echo     ' جرب Provider القديم >> test_access_2016.vbs
echo     Err.Clear >> test_access_2016.vbs
echo     Set conn2 = CreateObject("ADODB.Connection") >> test_access_2016.vbs
echo     conn2.Open "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=test_2016.accdb;" >> test_access_2016.vbs
echo     If Err.Number = 0 Then >> test_access_2016.vbs
echo         WScript.Echo "SUCCESS_12" >> test_access_2016.vbs
echo         conn2.Close >> test_access_2016.vbs
echo     Else >> test_access_2016.vbs
echo         WScript.Echo "FAILED" >> test_access_2016.vbs
echo     End If >> test_access_2016.vbs
echo End If >> test_access_2016.vbs

REM تشغيل الاختبار
for /f %%i in ('cscript //nologo test_access_2016.vbs 2^>nul') do set TEST_RESULT=%%i

if "%TEST_RESULT%"=="SUCCESS_16" (
    echo ✅ اختبار الاتصال نجح - Access 2016+ Provider
    echo 🚀 قاعدة البيانات جاهزة للاستخدام الأمثل
) else if "%TEST_RESULT%"=="SUCCESS_12" (
    echo ✅ اختبار الاتصال نجح - Access 2010+ Provider
    echo ⚠️  يُنصح بترقية Access Database Engine للحصول على أداء أفضل
) else (
    echo ❌ فشل اختبار الاتصال
    echo.
    echo 🔧 الحلول المقترحة:
    echo    1. ثبت Microsoft 365 Access Runtime
    echo    2. ثبت Access Database Engine 2016
    echo    3. أعد تشغيل الجهاز بعد التثبيت
    echo    4. شغل هذا الملف كـ Administrator
)

REM حذف ملفات الاختبار
del test_access_2016.vbs >nul 2>&1
del test_2016.accdb >nul 2>&1

echo.
echo [4] التحقق من .NET Runtime...
dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ .NET Runtime مثبت
    dotnet --version
) else (
    echo ❌ .NET Runtime غير مثبت
    echo 📥 يرجى تثبيت .NET 6.0 Desktop Runtime
)

echo.
echo [5] اختبار إنشاء جدول تجريبي...
if "%TEST_RESULT%"=="SUCCESS_16" (
    echo جاري اختبار إنشاء جدول...
    
    REM إنشاء ملف VBS لاختبار إنشاء جدول
    echo On Error Resume Next > test_table.vbs
    echo Set conn = CreateObject("ADODB.Connection") >> test_table.vbs
    echo conn.Open "Provider=Microsoft.ACE.OLEDB.16.0;Data Source=test_table.accdb;" >> test_table.vbs
    echo Set cmd = CreateObject("ADODB.Command") >> test_table.vbs
    echo cmd.ActiveConnection = conn >> test_table.vbs
    echo cmd.CommandText = "CREATE TABLE TestTable (Id COUNTER PRIMARY KEY, Name TEXT(50))" >> test_table.vbs
    echo cmd.Execute >> test_table.vbs
    echo If Err.Number = 0 Then >> test_table.vbs
    echo     WScript.Echo "TABLE_SUCCESS" >> test_table.vbs
    echo Else >> test_table.vbs
    echo     WScript.Echo "TABLE_FAILED" >> test_table.vbs
    echo End If >> test_table.vbs
    echo conn.Close >> test_table.vbs
    
    for /f %%i in ('cscript //nologo test_table.vbs 2^>nul') do set TABLE_RESULT=%%i
    
    if "%TABLE_RESULT%"=="TABLE_SUCCESS" (
        echo ✅ اختبار إنشاء الجدول نجح
        echo 🎉 قاعدة البيانات تعمل بشكل مثالي
    ) else (
        echo ❌ فشل اختبار إنشاء الجدول
        echo قد تحتاج لصلاحيات أعلى أو مساحة أكبر
    )
    
    REM حذف ملفات الاختبار
    del test_table.vbs >nul 2>&1
    del test_table.accdb >nul 2>&1
)

echo.
echo ========================================================
echo 📋 ملخص نتائج الاختبار
echo ========================================================

if "%TEST_RESULT%"=="SUCCESS_16" (
    echo ✅ قاعدة البيانات: جاهزة (Access 2016+)
) else if "%TEST_RESULT%"=="SUCCESS_12" (
    echo ⚠️  قاعدة البيانات: تعمل (Access 2010+)
) else (
    echo ❌ قاعدة البيانات: تحتاج إعداد
)

dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ .NET Runtime: مثبت
) else (
    echo ❌ .NET Runtime: غير مثبت
)

if exist "Data" (
    echo ✅ مجلد البيانات: موجود
) else (
    echo ❌ مجلد البيانات: غير موجود
)

echo.
if "%TEST_RESULT%"=="SUCCESS_16" (
    echo 🎉 النظام جاهز للتشغيل!
    echo.
    set /p choice="هل تريد تشغيل البرنامج الآن؟ (y/n): "
    if /i "%choice%"=="y" (
        echo.
        echo 🚀 جاري تشغيل البرنامج...
        call تشغيل_محلي.bat
    )
) else (
    echo ⚠️  يرجى إصلاح المشاكل أولاً
    echo.
    echo 🔧 للمساعدة في الإعداد، شغل:
    echo    📁 تثبيت_المتطلبات.bat
)

echo.
pause
