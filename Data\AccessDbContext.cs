using System;
using System.Data;
using System.Data.OleDb;
using System.IO;
using System.Threading.Tasks;
using AccountingSystem11.Models;

namespace AccountingSystem11.Data
{
    /// <summary>
    /// Access database context for local database operations
    /// </summary>
    public class AccessDbContext : IDisposable
    {
        private readonly string _connectionString;
        private readonly string _databasePath;
        private OleDbConnection _connection;

        public AccessDbContext()
        {
            // إنشاء مجلد البيانات إذا لم يكن موجوداً
            var dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
            if (!Directory.Exists(dataFolder))
            {
                Directory.CreateDirectory(dataFolder);
            }

            _databasePath = Path.Combine(dataFolder, "AccountingSystem11.accdb");

            // استخدام Provider محدث لـ Access 2016+
            _connectionString = $"Provider=Microsoft.ACE.OLEDB.16.0;Data Source={_databasePath};Persist Security Info=False;";

            // إذا فشل، جرب Provider الأقدم
            if (!TestConnection(_connectionString))
            {
                _connectionString = $"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={_databasePath};Persist Security Info=False;";
            }
        }

        public async Task<bool> InitializeDatabaseAsync()
        {
            try
            {
                // إنشاء قاعدة البيانات إذا لم تكن موجودة
                if (!File.Exists(_databasePath))
                {
                    await CreateDatabaseAsync();
                }

                // إنشاء الجداول إذا لم تكن موجودة
                await CreateTablesAsync();

                // إدخال البيانات الأولية
                await SeedInitialDataAsync();

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة قاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        private bool TestConnection(string connectionString)
        {
            try
            {
                using var testConnection = new OleDbConnection(connectionString);
                testConnection.Open();
                testConnection.Close();
                return true;
            }
            catch
            {
                return false;
            }
        }

        private async Task CreateDatabaseAsync()
        {
            try
            {
                // إنشاء قاعدة بيانات Access باستخدام COM Interop
                await CreateDatabaseWithInteropAsync();
            }
            catch (Exception ex)
            {
                // إذا فشل إنشاء قاعدة البيانات بـ Interop، ننشئها يدوياً
                await CreateDatabaseManuallyAsync();
            }
        }

        private async Task CreateDatabaseWithInteropAsync()
        {
            try
            {
                // استخدام Microsoft Access Interop لإنشاء قاعدة البيانات
                var accessApp = new Microsoft.Office.Interop.Access.Application();
                accessApp.NewCurrentDatabase(_databasePath);
                accessApp.CloseCurrentDatabase();
                accessApp.Quit();

                // تحرير الذاكرة
                System.Runtime.InteropServices.Marshal.ReleaseComObject(accessApp);

                await Task.Delay(500); // انتظار للتأكد من إنشاء الملف
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"فشل إنشاء قاعدة البيانات بـ Interop: {ex.Message}");
                throw;
            }
        }

        private async Task CreateDatabaseManuallyAsync()
        {
            try
            {
                // إنشاء ملف قاعدة بيانات Access فارغ باستخدام SQL
                using var connection = new OleDbConnection(_connectionString.Replace(_databasePath, ":memory:"));

                // إنشاء ملف فارغ أولاً
                await File.WriteAllBytesAsync(_databasePath, GetEmptyAccess2016DatabaseBytes());

                await Task.Delay(100);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"فشل إنشاء قاعدة البيانات يدوياً: {ex.Message}");

                // كحل أخير، إنشاء ملف فارغ بسيط
                await CreateMinimalDatabaseAsync();
            }
        }

        private byte[] GetEmptyAccess2016DatabaseBytes()
        {
            // بيانات ملف Access 2016 فارغ (header مبسط)
            var header = new byte[4096];

            // Access 2016 signature
            header[0] = 0x00;
            header[1] = 0x01;
            header[2] = 0x00;
            header[3] = 0x00;

            // Version info for Access 2016
            var versionBytes = System.Text.Encoding.ASCII.GetBytes("Standard ACE DB");
            Array.Copy(versionBytes, 0, header, 4, Math.Min(versionBytes.Length, 20));

            return header;
        }

        private async Task CreateMinimalDatabaseAsync()
        {
            // إنشاء ملف فارغ كحد أدنى
            await File.WriteAllTextAsync(_databasePath, "");

            // محاولة إنشاء قاعدة بيانات باستخدام SQL CREATE
            try
            {
                var tempConnectionString = _connectionString.Replace("Data Source=" + _databasePath, "Data Source=" + Path.GetTempFileName() + ".accdb");
                using var tempConnection = new OleDbConnection(tempConnectionString);
                await tempConnection.OpenAsync();
                tempConnection.Close();
            }
            catch
            {
                // تجاهل الأخطاء - سيتم إنشاء الجداول لاحقاً
            }
        }

        private async Task CreateTablesAsync()
        {
            using var connection = new OleDbConnection(_connectionString);
            await connection.OpenAsync();

            // إنشاء جدول العملاء مع تحسينات Access 2016+
            await ExecuteNonQueryAsync(connection, @"
                CREATE TABLE Customers (
                    Id COUNTER CONSTRAINT PrimaryKey PRIMARY KEY,
                    Name TEXT(100) NOT NULL,
                    CompanyName TEXT(100),
                    Phone TEXT(15),
                    Mobile TEXT(15),
                    Email TEXT(100),
                    Address TEXT(200),
                    City TEXT(50),
                    Governorate TEXT(50),
                    PostalCode TEXT(10),
                    TaxNumber TEXT(20),
                    CommercialRegNumber TEXT(20),
                    NationalId TEXT(14),
                    CustomerType LONG DEFAULT 1,
                    CreditLimit CURRENCY DEFAULT 0,
                    Balance CURRENCY DEFAULT 0,
                    PaymentTerms LONG DEFAULT 30,
                    DefaultDiscount CURRENCY DEFAULT 0,
                    Notes MEMO,
                    IsActive YESNO DEFAULT TRUE,
                    CreatedAt DATETIME DEFAULT Date(),
                    UpdatedAt DATETIME,
                    CreatedBy TEXT(100),
                    UpdatedBy TEXT(100),
                    IsDeleted YESNO DEFAULT FALSE,
                    DeletedAt DATETIME,
                    DeletedBy TEXT(100)
                )");

            // إنشاء جدول فئات المنتجات
            await ExecuteNonQueryAsync(connection, @"
                CREATE TABLE ProductCategories (
                    Id COUNTER CONSTRAINT PK_ProductCategories PRIMARY KEY,
                    Name TEXT(100) NOT NULL,
                    Description MEMO,
                    ParentCategoryId LONG,
                    Code TEXT(20),
                    IsActive YESNO DEFAULT TRUE,
                    DisplayOrder LONG DEFAULT 0,
                    CreatedAt DATETIME DEFAULT Date(),
                    UpdatedAt DATETIME,
                    CreatedBy TEXT(100),
                    UpdatedBy TEXT(100),
                    IsDeleted YESNO DEFAULT FALSE,
                    DeletedAt DATETIME,
                    DeletedBy TEXT(100)
                )");

            // إنشاء جدول الموردين
            await ExecuteNonQueryAsync(connection, @"
                CREATE TABLE Suppliers (
                    Id COUNTER CONSTRAINT PK_Suppliers PRIMARY KEY,
                    Name TEXT(100) NOT NULL,
                    CompanyName TEXT(100),
                    Phone TEXT(15),
                    Mobile TEXT(15),
                    Email TEXT(100),
                    Address TEXT(200),
                    City TEXT(50),
                    Governorate TEXT(50),
                    PostalCode TEXT(10),
                    TaxNumber TEXT(20),
                    CommercialRegNumber TEXT(20),
                    ContactPerson TEXT(100),
                    ContactPhone TEXT(15),
                    PaymentTerms LONG DEFAULT 30,
                    CreditLimit CURRENCY DEFAULT 0,
                    Balance CURRENCY DEFAULT 0,
                    Notes MEMO,
                    IsActive YESNO DEFAULT TRUE,
                    CreatedAt DATETIME DEFAULT Date(),
                    UpdatedAt DATETIME,
                    CreatedBy TEXT(100),
                    UpdatedBy TEXT(100),
                    IsDeleted YESNO DEFAULT FALSE,
                    DeletedAt DATETIME,
                    DeletedBy TEXT(100)
                )");

            // إنشاء جدول المنتجات
            await ExecuteNonQueryAsync(connection, @"
                CREATE TABLE Products (
                    Id COUNTER CONSTRAINT PK_Products PRIMARY KEY,
                    Code TEXT(50) NOT NULL,
                    Name TEXT(200) NOT NULL,
                    Description MEMO,
                    CategoryId LONG,
                    Unit TEXT(20) DEFAULT 'قطعة',
                    PurchasePrice CURRENCY DEFAULT 0,
                    SellingPrice CURRENCY DEFAULT 0,
                    MinSellingPrice CURRENCY DEFAULT 0,
                    WholesalePrice CURRENCY DEFAULT 0,
                    StockQuantity DOUBLE DEFAULT 0,
                    MinStockLevel DOUBLE DEFAULT 0,
                    MaxStockLevel DOUBLE DEFAULT 0,
                    ReorderPoint DOUBLE DEFAULT 0,
                    Barcode TEXT(50),
                    ImagePath TEXT(200),
                    TaxRate DOUBLE,
                    IsTaxable YESNO DEFAULT TRUE,
                    IsService YESNO DEFAULT FALSE,
                    IsActive YESNO DEFAULT TRUE,
                    Location TEXT(50),
                    SupplierId LONG,
                    Notes MEMO,
                    CreatedAt DATETIME DEFAULT Date(),
                    UpdatedAt DATETIME,
                    CreatedBy TEXT(100),
                    UpdatedBy TEXT(100),
                    IsDeleted YESNO DEFAULT FALSE,
                    DeletedAt DATETIME,
                    DeletedBy TEXT(100)
                )");

            // إنشاء جدول المستخدمين
            await ExecuteNonQueryAsync(connection, @"
                CREATE TABLE Users (
                    Id COUNTER CONSTRAINT PK_Users PRIMARY KEY,
                    Username TEXT(50) NOT NULL,
                    FullName TEXT(100) NOT NULL,
                    Email TEXT(100) NOT NULL,
                    PasswordHash TEXT(255) NOT NULL,
                    Phone TEXT(15),
                    Mobile TEXT(15),
                    Role LONG DEFAULT 5,
                    IsActive YESNO DEFAULT TRUE,
                    MustChangePassword YESNO DEFAULT FALSE,
                    LastLoginDate DATETIME,
                    LastLoginIp TEXT(45),
                    FailedLoginAttempts LONG DEFAULT 0,
                    LockedUntil DATETIME,
                    PasswordResetToken TEXT(100),
                    PasswordResetTokenExpiry DATETIME,
                    Preferences MEMO,
                    Notes MEMO,
                    CreatedAt DATETIME DEFAULT Date(),
                    UpdatedAt DATETIME,
                    CreatedBy TEXT(100),
                    UpdatedBy TEXT(100),
                    IsDeleted YESNO DEFAULT FALSE,
                    DeletedAt DATETIME,
                    DeletedBy TEXT(100)
                )");

            // إنشاء الفهارس مع معالجة القيم الفارغة
            await ExecuteNonQueryAsync(connection, "CREATE INDEX IX_Customers_TaxNumber ON Customers (TaxNumber) WITH IGNORE NULL");
            await ExecuteNonQueryAsync(connection, "CREATE INDEX IX_Customers_Email ON Customers (Email) WITH IGNORE NULL");
            await ExecuteNonQueryAsync(connection, "CREATE UNIQUE INDEX IX_Products_Code ON Products (Code)");
            await ExecuteNonQueryAsync(connection, "CREATE INDEX IX_Products_Barcode ON Products (Barcode) WITH IGNORE NULL");
            await ExecuteNonQueryAsync(connection, "CREATE UNIQUE INDEX IX_Users_Username ON Users (Username)");
            await ExecuteNonQueryAsync(connection, "CREATE UNIQUE INDEX IX_Users_Email ON Users (Email)");

            // فهارس إضافية للأداء
            await ExecuteNonQueryAsync(connection, "CREATE INDEX IX_Products_CategoryId ON Products (CategoryId)");
            await ExecuteNonQueryAsync(connection, "CREATE INDEX IX_Products_SupplierId ON Products (SupplierId)");
            await ExecuteNonQueryAsync(connection, "CREATE INDEX IX_ProductCategories_ParentId ON ProductCategories (ParentCategoryId)");
        }

        private async Task SeedInitialDataAsync()
        {
            using var connection = new OleDbConnection(_connectionString);
            await connection.OpenAsync();

            // إدخال مستخدم المدير الافتراضي
            var adminExists = await ExecuteScalarAsync(connection, "SELECT COUNT(*) FROM Users WHERE Username = 'admin'");
            if (Convert.ToInt32(adminExists) == 0)
            {
                var passwordHash = BCrypt.Net.BCrypt.HashPassword("admin123");
                await ExecuteNonQueryAsync(connection,
                    "INSERT INTO Users (Username, FullName, Email, PasswordHash, Role, IsActive, CreatedBy, CreatedAt) " +
                    $"VALUES ('admin', 'مدير النظام', '<EMAIL>', '{passwordHash}', 1, TRUE, 'System', #{DateTime.Now:yyyy-MM-dd HH:mm:ss}#)");
            }

            // إدخال فئات المنتجات الافتراضية
            var categoriesExist = await ExecuteScalarAsync(connection, "SELECT COUNT(*) FROM ProductCategories");
            if (Convert.ToInt32(categoriesExist) == 0)
            {
                var categories = new[]
                {
                    ("عام", "GEN", "فئة عامة للمنتجات"),
                    ("ملابس", "CLO", "الملابس والأزياء"),
                    ("أغذية ومشروبات", "FOO", "المواد الغذائية والمشروبات"),
                    ("إلكترونيات", "ELE", "الأجهزة الإلكترونية والكهربائية"),
                    ("أدوية", "MED", "الأدوية والمستحضرات الطبية")
                };

                foreach (var (name, code, description) in categories)
                {
                    await ExecuteNonQueryAsync(connection,
                        "INSERT INTO ProductCategories (Name, Code, Description, CreatedBy, CreatedAt) " +
                        $"VALUES ('{name}', '{code}', '{description}', 'System', #{DateTime.Now:yyyy-MM-dd HH:mm:ss}#)");
                }
            }

            // إدخال عميل تجريبي
            var customersExist = await ExecuteScalarAsync(connection, "SELECT COUNT(*) FROM Customers");
            if (Convert.ToInt32(customersExist) == 0)
            {
                await ExecuteNonQueryAsync(connection,
                    "INSERT INTO Customers (Name, CompanyName, Phone, Mobile, Email, Address, City, Governorate, CustomerType, CreditLimit, PaymentTerms, IsActive, CreatedBy, CreatedAt) " +
                    $"VALUES ('عميل تجريبي', 'شركة تجريبية', '02-12345678', '01012345678', '<EMAIL>', '123 شارع التجربة، المعادي', 'القاهرة', 'القاهرة', 2, 50000, 30, TRUE, 'System', #{DateTime.Now:yyyy-MM-dd HH:mm:ss}#)");
            }
        }

        private async Task<int> ExecuteNonQueryAsync(OleDbConnection connection, string sql)
        {
            try
            {
                using var command = new OleDbCommand(sql, connection);
                return await command.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                // تجاهل الأخطاء إذا كان الجدول موجود بالفعل
                if (!ex.Message.Contains("already exists"))
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تنفيذ SQL: {ex.Message}");
                }
                return 0;
            }
        }

        private async Task<object> ExecuteScalarAsync(OleDbConnection connection, string sql)
        {
            try
            {
                using var command = new OleDbCommand(sql, connection);
                return await command.ExecuteScalarAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنفيذ SQL Scalar: {ex.Message}");
                return 0;
            }
        }

        public OleDbConnection GetConnection()
        {
            if (_connection == null || _connection.State != ConnectionState.Open)
            {
                _connection = new OleDbConnection(_connectionString);
                _connection.Open();
            }
            return _connection;
        }

        public void Dispose()
        {
            _connection?.Close();
            _connection?.Dispose();
        }
    }
}
