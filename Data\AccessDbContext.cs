using System;
using System.Data;
using System.Data.OleDb;
using System.IO;
using System.Threading.Tasks;

namespace AccountingSystem11.Data
{
    /// <summary>
    /// Access Database Context for local data storage
    /// </summary>
    public class AccessDbContext : IDisposable
    {
        private readonly string _connectionString;
        private OleDbConnection _connection;

        public AccessDbContext()
        {
            var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "AccountingSystem.accdb");
            _connectionString = $"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={dbPath};";
        }

        public async Task<bool> InitializeAsync()
        {
            try
            {
                await EnsureDatabaseExistsAsync();
                await CreateTablesAsync();
                await SeedInitialDataAsync();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Database initialization error: {ex.Message}");
                return false;
            }
        }

        private async Task EnsureDatabaseExistsAsync()
        {
            var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "AccountingSystem.accdb");
            var dataDir = Path.GetDirectoryName(dbPath);

            if (!Directory.Exists(dataDir))
            {
                Directory.CreateDirectory(dataDir);
            }

            if (!File.Exists(dbPath))
            {
                // Create empty Access database
                var catalog = new ADOX.Catalog();
                catalog.Create(_connectionString);
                System.Runtime.InteropServices.Marshal.ReleaseComObject(catalog);
            }
        }

        private async Task CreateTablesAsync()
        {
            using var connection = new OleDbConnection(_connectionString);
            await connection.OpenAsync();

            var tables = new[]
            {
                @"CREATE TABLE IF NOT EXISTS Users (
                    ID AUTOINCREMENT PRIMARY KEY,
                    Username TEXT(50) NOT NULL,
                    PasswordHash TEXT(255) NOT NULL,
                    FullName TEXT(100) NOT NULL,
                    Email TEXT(100),
                    Role TEXT(20) DEFAULT 'User',
                    IsActive YESNO DEFAULT True,
                    CreatedAt DATETIME DEFAULT Now(),
                    CreatedBy TEXT(50)
                )",

                @"CREATE TABLE IF NOT EXISTS Customers (
                    ID AUTOINCREMENT PRIMARY KEY,
                    Name TEXT(100) NOT NULL,
                    CompanyName TEXT(100),
                    Phone TEXT(20),
                    Mobile TEXT(20),
                    Email TEXT(100),
                    Address MEMO,
                    City TEXT(50),
                    Governorate TEXT(50),
                    TaxNumber TEXT(50),
                    CustomerType TEXT(20) DEFAULT 'Individual',
                    CreditLimit CURRENCY DEFAULT 0,
                    Balance CURRENCY DEFAULT 0,
                    PaymentTerms INTEGER DEFAULT 0,
                    IsActive YESNO DEFAULT True,
                    CreatedAt DATETIME DEFAULT Now(),
                    CreatedBy TEXT(50)
                )",

                @"CREATE TABLE IF NOT EXISTS ProductCategories (
                    ID AUTOINCREMENT PRIMARY KEY,
                    Name TEXT(100) NOT NULL,
                    Code TEXT(20),
                    Description MEMO,
                    IsActive YESNO DEFAULT True,
                    CreatedAt DATETIME DEFAULT Now(),
                    CreatedBy TEXT(50)
                )",

                @"CREATE TABLE IF NOT EXISTS Products (
                    ID AUTOINCREMENT PRIMARY KEY,
                    Code TEXT(50) NOT NULL,
                    Name TEXT(100) NOT NULL,
                    Description MEMO,
                    CategoryID INTEGER,
                    Unit TEXT(20) DEFAULT 'قطعة',
                    PurchasePrice CURRENCY DEFAULT 0,
                    SellingPrice CURRENCY DEFAULT 0,
                    MinSellingPrice CURRENCY DEFAULT 0,
                    StockQuantity DOUBLE DEFAULT 0,
                    MinStockLevel DOUBLE DEFAULT 0,
                    IsTaxable YESNO DEFAULT True,
                    TaxRate DOUBLE DEFAULT 14,
                    IsActive YESNO DEFAULT True,
                    CreatedAt DATETIME DEFAULT Now(),
                    CreatedBy TEXT(50)
                )",

                @"CREATE TABLE IF NOT EXISTS Invoices (
                    ID AUTOINCREMENT PRIMARY KEY,
                    InvoiceNumber TEXT(50) NOT NULL,
                    CustomerID INTEGER,
                    InvoiceDate DATETIME NOT NULL,
                    DueDate DATETIME,
                    Subtotal CURRENCY DEFAULT 0,
                    TaxAmount CURRENCY DEFAULT 0,
                    DiscountAmount CURRENCY DEFAULT 0,
                    TotalAmount CURRENCY DEFAULT 0,
                    Status TEXT(20) DEFAULT 'Draft',
                    Notes MEMO,
                    CreatedAt DATETIME DEFAULT Now(),
                    CreatedBy TEXT(50)
                )"
            };

            foreach (var tableScript in tables)
            {
                try
                {
                    using var command = new OleDbCommand(tableScript, connection);
                    await command.ExecuteNonQueryAsync();
                }
                catch (Exception ex)
                {
                    // Table might already exist
                    System.Diagnostics.Debug.WriteLine($"Table creation warning: {ex.Message}");
                }
            }
        }

        private async Task SeedInitialDataAsync()
        {
            using var connection = new OleDbConnection(_connectionString);
            await connection.OpenAsync();

            // Check if admin user exists
            var checkUserCmd = new OleDbCommand("SELECT COUNT(*) FROM Users WHERE Username = 'admin'", connection);
            var userCount = (int)await checkUserCmd.ExecuteScalarAsync();

            if (userCount == 0)
            {
                // Create admin user
                var createUserCmd = new OleDbCommand(
                    "INSERT INTO Users (Username, PasswordHash, FullName, Email, Role) VALUES (?, ?, ?, ?, ?)",
                    connection);
                
                createUserCmd.Parameters.AddWithValue("@username", "admin");
                createUserCmd.Parameters.AddWithValue("@password", BCrypt.Net.BCrypt.HashPassword("admin123"));
                createUserCmd.Parameters.AddWithValue("@fullname", "مدير النظام");
                createUserCmd.Parameters.AddWithValue("@email", "<EMAIL>");
                createUserCmd.Parameters.AddWithValue("@role", "SystemAdmin");

                await createUserCmd.ExecuteNonQueryAsync();
            }

            // Seed categories if not exist
            var checkCatCmd = new OleDbCommand("SELECT COUNT(*) FROM ProductCategories", connection);
            var catCount = (int)await checkCatCmd.ExecuteScalarAsync();

            if (catCount == 0)
            {
                var categories = new[]
                {
                    ("عام", "GEN", "فئة عامة للمنتجات"),
                    ("ملابس", "CLO", "الملابس والأزياء"),
                    ("أغذية", "FOD", "المواد الغذائية"),
                    ("إلكترونيات", "ELE", "الأجهزة الإلكترونية")
                };

                foreach (var (name, code, desc) in categories)
                {
                    var cmd = new OleDbCommand(
                        "INSERT INTO ProductCategories (Name, Code, Description) VALUES (?, ?, ?)",
                        connection);
                    cmd.Parameters.AddWithValue("@name", name);
                    cmd.Parameters.AddWithValue("@code", code);
                    cmd.Parameters.AddWithValue("@desc", desc);
                    await cmd.ExecuteNonQueryAsync();
                }
            }
        }

        public async Task<DataTable> ExecuteQueryAsync(string query, params OleDbParameter[] parameters)
        {
            using var connection = new OleDbConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new OleDbCommand(query, connection);
            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }

            using var adapter = new OleDbDataAdapter(command);
            var dataTable = new DataTable();
            adapter.Fill(dataTable);

            return dataTable;
        }

        public async Task<int> ExecuteNonQueryAsync(string query, params OleDbParameter[] parameters)
        {
            using var connection = new OleDbConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new OleDbCommand(query, connection);
            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }

            return await command.ExecuteNonQueryAsync();
        }

        public void Dispose()
        {
            _connection?.Dispose();
        }
    }
}
