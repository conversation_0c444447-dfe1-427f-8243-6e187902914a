using System;
using System.ComponentModel.DataAnnotations;

namespace AccountingSystem11.Models
{
    /// <summary>
    /// Invoice payment tracking
    /// </summary>
    public class InvoicePayment : BaseEntity
    {
        [Required]
        public int InvoiceId { get; set; }
        public virtual Invoice Invoice { get; set; }

        [Required]
        public DateTime PaymentDate { get; set; } = DateTime.Now;

        [Required]
        [MaxLength(20)]
        public string PaymentNumber { get; set; }

        /// <summary>
        /// Payment amount
        /// </summary>
        [Required]
        public decimal Amount { get; set; }

        /// <summary>
        /// Payment method
        /// </summary>
        public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.Cash;

        /// <summary>
        /// Reference number (check number, transfer reference, etc.)
        /// </summary>
        [MaxLength(50)]
        public string ReferenceNumber { get; set; }

        /// <summary>
        /// Bank name (if applicable)
        /// </summary>
        [MaxLength(100)]
        public string BankName { get; set; }

        /// <summary>
        /// Payment notes
        /// </summary>
        [MaxLength(500)]
        public string Notes { get; set; }

        /// <summary>
        /// Whether payment is confirmed
        /// </summary>
        public bool IsConfirmed { get; set; } = true;

        /// <summary>
        /// Exchange rate (if payment in different currency)
        /// </summary>
        public decimal ExchangeRate { get; set; } = 1;

        /// <summary>
        /// Currency of payment
        /// </summary>
        [MaxLength(3)]
        public string Currency { get; set; } = "EGP";
    }
}
