using AccountingSystem11.Services;
using System;
using System.Threading.Tasks;
using System.Windows.Input;

namespace AccountingSystem11.ViewModels
{
    /// <summary>
    /// Inventory view model (placeholder)
    /// </summary>
    public class InventoryViewModel : BaseViewModel
    {
        private readonly IInventoryService _inventoryService;

        public InventoryViewModel(IInventoryService inventoryService)
        {
            _inventoryService = inventoryService;

            LoadDataCommand = new AsyncRelayCommand(LoadDataAsync);
        }

        public ICommand LoadDataCommand { get; }

        private async Task LoadDataAsync()
        {
            try
            {
                // Load inventory data
                await Task.Delay(1000); // Placeholder
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل المخزون: {ex.Message}");
            }
        }
    }
}
