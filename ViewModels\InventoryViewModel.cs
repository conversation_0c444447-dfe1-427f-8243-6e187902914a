using AccountingSystem11.Services;
using System.Threading.Tasks;
using System.Windows.Input;

namespace AccountingSystem11.ViewModels
{
    /// <summary>
    /// Inventory view model (placeholder)
    /// </summary>
    public class InventoryViewModel : BaseViewModel
    {
        private readonly IInventoryService _inventoryService;

        public InventoryViewModel(IInventoryService inventoryService)
        {
            _inventoryService = inventoryService;

            LoadDataCommand = new AsyncRelayCommand(LoadDataAsync);
        }

        public ICommand LoadDataCommand { get; }

        private async Task LoadDataAsync()
        {
            await ExecuteAsync(async () =>
            {
                // Load inventory data
                await Task.Delay(1000); // Placeholder
            }, "جاري تحميل بيانات المخزون...");
        }
    }
}
