using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Win32;

namespace AccountingSystem11.Services
{
    public class AnalyticsService
    {
        private readonly string _analyticsEndpoint = "https://analytics.system11.com/track";
        private readonly string _sessionId;
        private readonly string _userId;
        private readonly HttpClient _httpClient;
        private readonly Queue<AnalyticsEvent> _eventQueue;
        private readonly object _queueLock = new object();

        public AnalyticsService()
        {
            _sessionId = Guid.NewGuid().ToString();
            _userId = GetOrCreateUserId();
            _httpClient = new HttpClient();
            _eventQueue = new Queue<AnalyticsEvent>();
            
            // إرسال حدث بدء الجلسة
            TrackEvent("app_start", new Dictionary<string, object>
            {
                ["version"] = GetAppVersion(),
                ["os"] = Environment.OSVersion.ToString(),
                ["dotnet_version"] = Environment.Version.ToString()
            });
        }

        public void TrackEvent(string eventName, Dictionary<string, object> properties = null)
        {
            try
            {
                var analyticsEvent = new AnalyticsEvent
                {
                    EventName = eventName,
                    UserId = _userId,
                    SessionId = _sessionId,
                    Timestamp = DateTime.UtcNow,
                    Properties = properties ?? new Dictionary<string, object>()
                };

                // إضافة خصائص النظام
                analyticsEvent.Properties["app_version"] = GetAppVersion();
                analyticsEvent.Properties["license_type"] = GetLicenseType();
                analyticsEvent.Properties["user_type"] = GetUserType();

                lock (_queueLock)
                {
                    _eventQueue.Enqueue(analyticsEvent);
                }

                // إرسال الأحداث إذا وصل العدد للحد الأقصى
                if (_eventQueue.Count >= 10)
                {
                    _ = Task.Run(FlushEventsAsync);
                }
            }
            catch
            {
                // تجاهل أخطاء التحليلات لعدم تأثيرها على التطبيق
            }
        }

        public void TrackFeatureUsage(string featureName, Dictionary<string, object> context = null)
        {
            var properties = context ?? new Dictionary<string, object>();
            properties["feature"] = featureName;
            TrackEvent("feature_used", properties);
        }

        public void TrackError(Exception exception, string context = null)
        {
            TrackEvent("error_occurred", new Dictionary<string, object>
            {
                ["error_type"] = exception.GetType().Name,
                ["error_message"] = exception.Message,
                ["stack_trace"] = exception.StackTrace,
                ["context"] = context ?? "unknown"
            });
        }

        public void TrackPerformance(string operation, TimeSpan duration, bool success = true)
        {
            TrackEvent("performance_metric", new Dictionary<string, object>
            {
                ["operation"] = operation,
                ["duration_ms"] = duration.TotalMilliseconds,
                ["success"] = success
            });
        }

        public void TrackUserAction(string action, string screen = null, Dictionary<string, object> data = null)
        {
            var properties = data ?? new Dictionary<string, object>();
            properties["action"] = action;
            if (!string.IsNullOrEmpty(screen))
                properties["screen"] = screen;

            TrackEvent("user_action", properties);
        }

        public void TrackBusinessMetric(string metric, double value, string unit = null)
        {
            TrackEvent("business_metric", new Dictionary<string, object>
            {
                ["metric"] = metric,
                ["value"] = value,
                ["unit"] = unit ?? "count"
            });
        }

        public async Task FlushEventsAsync()
        {
            try
            {
                List<AnalyticsEvent> eventsToSend;
                
                lock (_queueLock)
                {
                    if (_eventQueue.Count == 0) return;
                    
                    eventsToSend = new List<AnalyticsEvent>(_eventQueue);
                    _eventQueue.Clear();
                }

                var payload = new
                {
                    events = eventsToSend,
                    client_info = new
                    {
                        app_name = "AccountingSystem11",
                        app_version = GetAppVersion(),
                        platform = "Windows",
                        user_id = _userId,
                        session_id = _sessionId
                    }
                };

                var json = JsonSerializer.Serialize(payload);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // إرسال مع timeout قصير لعدم تأثيره على الأداء
                using var cts = new System.Threading.CancellationTokenSource(TimeSpan.FromSeconds(5));
                await _httpClient.PostAsync(_analyticsEndpoint, content, cts.Token);
            }
            catch
            {
                // تجاهل أخطاء الإرسال
            }
        }

        public void TrackLicenseEvent(string eventType, string licenseType = null)
        {
            TrackEvent("license_event", new Dictionary<string, object>
            {
                ["event_type"] = eventType, // activated, expired, trial_started, etc.
                ["license_type"] = licenseType ?? GetLicenseType()
            });
        }

        public void TrackSalesMetrics(decimal revenue, string currency = "EGP", string source = null)
        {
            TrackEvent("sales_metric", new Dictionary<string, object>
            {
                ["revenue"] = revenue,
                ["currency"] = currency,
                ["source"] = source ?? "direct"
            });
        }

        private string GetOrCreateUserId()
        {
            try
            {
                const string registryPath = @"SOFTWARE\AccountingSystem11";
                const string userIdKey = "UserId";

                using var key = Registry.CurrentUser.CreateSubKey(registryPath);
                var existingUserId = key.GetValue(userIdKey) as string;

                if (!string.IsNullOrEmpty(existingUserId))
                {
                    return existingUserId;
                }

                var newUserId = Guid.NewGuid().ToString();
                key.SetValue(userIdKey, newUserId);
                return newUserId;
            }
            catch
            {
                return Guid.NewGuid().ToString();
            }
        }

        private string GetAppVersion()
        {
            try
            {
                var assembly = System.Reflection.Assembly.GetExecutingAssembly();
                return assembly.GetName().Version?.ToString() ?? "1.0.0";
            }
            catch
            {
                return "1.0.0";
            }
        }

        private string GetLicenseType()
        {
            try
            {
                var licenseService = new LicenseService();
                var license = licenseService.GetLicenseInfo();
                return license?.LicenseType.ToString() ?? "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }

        private string GetUserType()
        {
            try
            {
                var licenseService = new LicenseService();
                var license = licenseService.GetLicenseInfo();
                
                if (license?.LicenseType == LicenseType.Trial)
                    return "trial";
                else if (license?.LicenseType != LicenseType.Trial)
                    return "paid";
                else
                    return "unknown";
            }
            catch
            {
                return "unknown";
            }
        }

        public void Dispose()
        {
            try
            {
                // إرسال حدث إنهاء الجلسة
                TrackEvent("app_end");
                
                // إرسال الأحداث المتبقية
                FlushEventsAsync().Wait(TimeSpan.FromSeconds(2));
            }
            catch
            {
                // تجاهل الأخطاء عند الإغلاق
            }
            finally
            {
                _httpClient?.Dispose();
            }
        }
    }

    public class AnalyticsEvent
    {
        public string EventName { get; set; }
        public string UserId { get; set; }
        public string SessionId { get; set; }
        public DateTime Timestamp { get; set; }
        public Dictionary<string, object> Properties { get; set; }
    }

    // إضافة للتطبيق الرئيسي
    public static class AnalyticsExtensions
    {
        private static AnalyticsService _analytics;

        public static void InitializeAnalytics()
        {
            _analytics = new AnalyticsService();
        }

        public static void TrackEvent(string eventName, Dictionary<string, object> properties = null)
        {
            _analytics?.TrackEvent(eventName, properties);
        }

        public static void TrackFeatureUsage(string featureName, Dictionary<string, object> context = null)
        {
            _analytics?.TrackFeatureUsage(featureName, context);
        }

        public static void TrackError(Exception exception, string context = null)
        {
            _analytics?.TrackError(exception, context);
        }

        public static void TrackUserAction(string action, string screen = null, Dictionary<string, object> data = null)
        {
            _analytics?.TrackUserAction(action, screen, data);
        }

        public static void ShutdownAnalytics()
        {
            _analytics?.Dispose();
            _analytics = null;
        }
    }
}
