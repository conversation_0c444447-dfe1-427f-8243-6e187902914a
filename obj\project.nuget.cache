{"version": 2, "dgSpecHash": "P4ChoYtDxTQ=", "success": false, "projectFilePath": "D:\\اتن\\AccountingSystem11.csproj", "expectedPackageFiles": [], "logs": [{"code": "Undefined", "level": "Error", "message": "Failed to download package 'Microsoft.EntityFrameworkCore.SqlServer.7.0.14' from 'https://api.nuget.org/v3-flatcontainer/microsoft.entityframeworkcore.sqlserver/7.0.14/microsoft.entityframeworkcore.sqlserver.7.0.14.nupkg'.\r\nNo such host is known. (api.nuget.org:443)\r\n  No such host is known.", "projectPath": "D:\\اتن\\AccountingSystem11.csproj", "filePath": "D:\\اتن\\AccountingSystem11.csproj", "targetGraphs": []}, {"code": "Undefined", "level": "Error", "message": "Failed to download package 'System.Data.SqlClient.4.8.5' from 'https://api.nuget.org/v3-flatcontainer/system.data.sqlclient/4.8.5/system.data.sqlclient.4.8.5.nupkg'.\r\nNo such host is known. (api.nuget.org:443)\r\n  No such host is known.", "projectPath": "D:\\اتن\\AccountingSystem11.csproj", "filePath": "D:\\اتن\\AccountingSystem11.csproj", "targetGraphs": []}, {"code": "Undefined", "level": "Error", "message": "Failed to download package 'MaterialDesignThemes.4.9.0' from 'https://api.nuget.org/v3-flatcontainer/materialdesignthemes/4.9.0/materialdesignthemes.4.9.0.nupkg'.\r\nNo such host is known. (api.nuget.org:443)\r\n  No such host is known.", "projectPath": "D:\\اتن\\AccountingSystem11.csproj", "filePath": "D:\\اتن\\AccountingSystem11.csproj", "targetGraphs": []}, {"code": "Undefined", "level": "Error", "message": "Failed to download package 'EPPlus.7.0.4' from 'https://api.nuget.org/v3-flatcontainer/epplus/7.0.4/epplus.7.0.4.nupkg'.\r\nNo such host is known. (api.nuget.org:443)\r\n  No such host is known.", "projectPath": "D:\\اتن\\AccountingSystem11.csproj", "filePath": "D:\\اتن\\AccountingSystem11.csproj", "targetGraphs": []}, {"code": "Undefined", "level": "Error", "message": "Failed to download package 'Microsoft.EntityFrameworkCore.Tools.7.0.14' from 'https://api.nuget.org/v3-flatcontainer/microsoft.entityframeworkcore.tools/7.0.14/microsoft.entityframeworkcore.tools.7.0.14.nupkg'.\r\nNo such host is known. (api.nuget.org:443)\r\n  No such host is known.", "projectPath": "D:\\اتن\\AccountingSystem11.csproj", "filePath": "D:\\اتن\\AccountingSystem11.csproj", "targetGraphs": []}, {"code": "Undefined", "level": "Error", "message": "The feed 'nuget.org [https://api.nuget.org/v3/index.json]' lists package 'Microsoft.EntityFrameworkCore.Tools.7.0.14' but multiple attempts to download the nupkg have failed. The feed is either invalid or required packages were removed while the current operation was in progress. Verify the package exists on the feed and try again.", "projectPath": "D:\\اتن\\AccountingSystem11.csproj", "filePath": "D:\\اتن\\AccountingSystem11.csproj", "targetGraphs": []}, {"code": "Undefined", "level": "Error", "message": "The feed 'nuget.org [https://api.nuget.org/v3/index.json]' lists package 'EPPlus.7.0.4' but multiple attempts to download the nupkg have failed. The feed is either invalid or required packages were removed while the current operation was in progress. Verify the package exists on the feed and try again.", "projectPath": "D:\\اتن\\AccountingSystem11.csproj", "filePath": "D:\\اتن\\AccountingSystem11.csproj", "targetGraphs": []}, {"code": "Undefined", "level": "Error", "message": "The feed 'nuget.org [https://api.nuget.org/v3/index.json]' lists package 'MaterialDesignThemes.4.9.0' but multiple attempts to download the nupkg have failed. The feed is either invalid or required packages were removed while the current operation was in progress. Verify the package exists on the feed and try again.", "projectPath": "D:\\اتن\\AccountingSystem11.csproj", "filePath": "D:\\اتن\\AccountingSystem11.csproj", "targetGraphs": []}, {"code": "Undefined", "level": "Error", "message": "The feed 'nuget.org [https://api.nuget.org/v3/index.json]' lists package 'System.Data.SqlClient.4.8.5' but multiple attempts to download the nupkg have failed. The feed is either invalid or required packages were removed while the current operation was in progress. Verify the package exists on the feed and try again.", "projectPath": "D:\\اتن\\AccountingSystem11.csproj", "filePath": "D:\\اتن\\AccountingSystem11.csproj", "targetGraphs": []}, {"code": "Undefined", "level": "Error", "message": "The feed 'nuget.org [https://api.nuget.org/v3/index.json]' lists package 'Microsoft.EntityFrameworkCore.SqlServer.7.0.14' but multiple attempts to download the nupkg have failed. The feed is either invalid or required packages were removed while the current operation was in progress. Verify the package exists on the feed and try again.", "projectPath": "D:\\اتن\\AccountingSystem11.csproj", "filePath": "D:\\اتن\\AccountingSystem11.csproj", "targetGraphs": []}]}