#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف إعداد cx_Freeze لتحويل نظام المحاسبة 11 إلى EXE
"""

import sys
from cx_Freeze import setup, Executable

# إعدادات البناء
build_exe_options = {
    "packages": ["tkinter", "sqlite3", "hashlib", "datetime", "os"],
    "excludes": ["unittest", "email", "html", "http", "urllib", "xml"],
    "include_files": [],
    "optimize": 2,
    "build_exe": "EXE_cx_Freeze"
}

# إعدادات الملف التنفيذي
base = None
if sys.platform == "win32":
    base = "Win32GUI"  # إخفاء نافذة الكونسول

# إعداد التطبيق
setup(
    name="نظام المحاسبة 11",
    version="1.0",
    description="نظام محاسبة شامل باللغة العربية",
    author="فريق التطوير",
    options={"build_exe": build_exe_options},
    executables=[
        Executable(
            "accounting_system_python.py",
            base=base,
            target_name="نظام_المحاسبة_11_cx_Freeze.exe",
            icon=None
        )
    ]
)
