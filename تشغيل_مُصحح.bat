@echo off
title نظام المحاسبة 11 - إصدار مُصحح
color 0B
echo.
echo ========================================================
echo        نظام المحاسبة 11 - إصدار مُصحح
echo ========================================================
echo.
echo 🔧 تم إصلاح جميع الأخطاء
echo 🗃️ قاعدة بيانات Access محلية محسنة
echo 🚀 أداء أسرع واستقرار أعلى
echo.

echo [1] التحقق من .NET...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET غير مثبت
    echo 📥 جاري فتح رابط التحميل...
    start https://dotnet.microsoft.com/download/dotnet/6.0
    echo.
    echo بعد تثبيت .NET، أعد تشغيل هذا الملف
    pause
    exit /b 1
)
echo ✅ .NET مثبت

echo.
echo [2] التحقق من Access Database Engine...
set "ACCESS_OK=0"

REM فحص شامل لجميع إصدارات Access
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Classes\Microsoft.ACE.OLEDB.16.0" >nul 2>&1
if %errorlevel% equ 0 set "ACCESS_OK=1"

reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Classes\Microsoft.ACE.OLEDB.12.0" >nul 2>&1
if %errorlevel% equ 0 set "ACCESS_OK=1"

reg query "HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Classes\Microsoft.ACE.OLEDB.16.0" >nul 2>&1
if %errorlevel% equ 0 set "ACCESS_OK=1"

reg query "HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Classes\Microsoft.ACE.OLEDB.12.0" >nul 2>&1
if %errorlevel% equ 0 set "ACCESS_OK=1"

if "%ACCESS_OK%"=="1" (
    echo ✅ Access Database Engine مثبت
) else (
    echo ❌ Access Database Engine غير مثبت
    echo.
    echo 📥 جاري فتح رابط التحميل...
    start https://www.microsoft.com/en-us/download/details.aspx?id=54920
    echo.
    echo 📋 اختر النسخة المناسبة وثبتها، ثم أعد تشغيل هذا الملف
    pause
    exit /b 1
)

echo.
echo [3] إعداد مجلد البيانات...
if not exist "Data" (
    mkdir "Data"
    echo ✅ تم إنشاء مجلد البيانات
) else (
    echo ✅ مجلد البيانات موجود
)

echo.
echo [4] تحضير البرنامج...
dotnet restore --verbosity minimal >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ في تحضير البرنامج
    echo جرب تشغيل الملف كـ Administrator
    pause
    exit /b 1
)
echo ✅ تم تحضير البرنامج

echo.
echo [5] بناء البرنامج...
dotnet build --verbosity minimal >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ في بناء البرنامج
    echo.
    echo 🔧 جرب الحلول التالية:
    echo    1. تشغيل الملف كـ Administrator
    echo    2. إغلاق أي برنامج مضاد فيروسات مؤقتاً
    echo    3. التأكد من وجود مساحة كافية على القرص
    pause
    exit /b 1
)
echo ✅ تم بناء البرنامج بنجاح

echo.
echo ========================================================
echo 🎉 جاري تشغيل نظام المحاسبة 11...
echo ========================================================
echo.
echo 🗃️ قاعدة البيانات: Access محلية (مُصححة)
echo 📁 مكان البيانات: Data\AccountingSystem11.accdb
echo.
echo 🔐 بيانات تسجيل الدخول:
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo.
echo 🔧 التحسينات الجديدة:
echo    ✅ إصلاح جميع أخطاء قاعدة البيانات
echo    ✅ تحسين الأداء والاستقرار
echo    ✅ واجهة محسنة وأسرع
echo    ✅ دعم أفضل لـ Access 2016+
echo.
echo ⚠️ ملاحظات مهمة:
echo    • غير كلمة المرور بعد تسجيل الدخول
echo    • البيانات محفوظة محلياً على جهازك
echo    • لا تحتاج اتصال بالإنترنت للعمل
echo.
echo ========================================================

timeout /t 3 /nobreak >nul

REM تشغيل البرنامج مع معالجة الأخطاء
echo 🚀 بدء التشغيل...
dotnet run 2>error.log
set "EXIT_CODE=%errorlevel%"

if %EXIT_CODE% neq 0 (
    echo.
    echo ❌ حدث خطأ أثناء التشغيل
    echo.
    if exist error.log (
        echo 📄 تفاصيل الخطأ:
        type error.log
        echo.
    )
    echo 🔧 الحلول المقترحة:
    echo    1. أعد تشغيل الجهاز
    echo    2. شغل الملف كـ Administrator
    echo    3. تأكد من إغلاق أي نسخة أخرى من البرنامج
    echo    4. تحقق من مساحة القرص الصلب
    echo.
) else (
    echo.
    echo ✅ تم إغلاق البرنامج بنجاح
)

REM تنظيف ملفات الأخطاء
if exist error.log del error.log >nul 2>&1

echo.
echo 💾 البيانات محفوظة في: Data\AccountingSystem11.accdb
echo 🔄 لإعادة التشغيل، اضغط على هذا الملف مرة أخرى
echo.
pause
