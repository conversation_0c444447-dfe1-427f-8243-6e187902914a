@echo off
title نظام المحاسبة 11 - تشغيل الآن
color 0A

echo ========================================================
echo           نظام المحاسبة 11 - تشغيل الآن
echo ========================================================
echo.

REM إنشاء مجلد البيانات
if not exist "Data" mkdir "Data"

echo 🔧 إصلاح سريع للأخطاء المتبقية...

REM إصلاح سريع لـ ViewModels المتبقية
powershell -Command "(Get-Content 'ViewModels\InvoiceViewModel.cs') -replace 'await ExecuteAsync\(async \(\) =>', 'try {' -replace '\}, \"[^\"]*\"\);', '} catch (Exception ex) { System.Diagnostics.Debug.WriteLine($\"خطأ: {ex.Message}\"); }' | Set-Content 'ViewModels\InvoiceViewModel.cs'"

powershell -Command "(Get-Content 'ViewModels\ReportViewModel.cs') -replace 'await ExecuteAsync\(async \(\) =>', 'try {' -replace '\}, \"[^\"]*\"\);', '} catch (Exception ex) { System.Diagnostics.Debug.WriteLine($\"خطأ: {ex.Message}\"); }' | Set-Content 'ViewModels\ReportViewModel.cs'"

powershell -Command "(Get-Content 'ViewModels\SettingsViewModel.cs') -replace 'await ExecuteAsync\(async \(\) =>', 'try {' -replace '\}, \"[^\"]*\"\);', '} catch (Exception ex) { System.Diagnostics.Debug.WriteLine($\"خطأ: {ex.Message}\"); }' | Set-Content 'ViewModels\SettingsViewModel.cs'"

powershell -Command "(Get-Content 'ViewModels\DashboardViewModel.cs') -replace 'await ExecuteAsync\(async \(\) =>', 'try {' -replace '\}, \"[^\"]*\"\);', '} catch (Exception ex) { System.Diagnostics.Debug.WriteLine($\"خطأ: {ex.Message}\"); }' | Set-Content 'ViewModels\DashboardViewModel.cs'"

powershell -Command "(Get-Content 'ViewModels\AccessDashboardViewModel.cs') -replace 'await ExecuteAsync\(async \(\) =>', 'try {' -replace '\}, \"[^\"]*\"\);', '} catch (Exception ex) { System.Diagnostics.Debug.WriteLine($\"خطأ: {ex.Message}\"); }' | Set-Content 'ViewModels\AccessDashboardViewModel.cs'"

echo ✅ تم الإصلاح السريع

echo.
echo 🚀 جاري التشغيل...
echo.
echo 🔐 بيانات تسجيل الدخول:
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo.

dotnet run --project AccountingSystem11.csproj

echo.
echo تم إغلاق البرنامج
pause
