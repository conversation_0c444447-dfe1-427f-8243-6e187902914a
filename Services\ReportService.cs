using AccountingSystem11.Data;
using AccountingSystem11.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AccountingSystem11.Services
{
    /// <summary>
    /// Report service implementation
    /// </summary>
    public class ReportService : IReportService
    {
        private readonly AccountingDbContext _context;

        public ReportService(AccountingDbContext context)
        {
            _context = context;
        }

        public async Task<DashboardData> GetDashboardDataAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var start = startDate ?? DateTime.Today.AddDays(-30);
            var end = endDate ?? DateTime.Today;

            var dashboardData = new DashboardData
            {
                StartDate = start,
                EndDate = end
            };

            // Get sales data
            var salesInvoices = await _context.Invoices
                .Where(i => i.InvoiceType == InvoiceType.Sales &&
                           i.InvoiceDate >= start &&
                           i.InvoiceDate <= end &&
                           i.Status == InvoiceStatus.Approved)
                .ToListAsync();

            dashboardData.TotalSales = salesInvoices.Sum(i => i.TotalAmount);
            dashboardData.TotalInvoices = salesInvoices.Count;

            // Calculate profit (simplified - would need cost data)
            dashboardData.TotalProfit = dashboardData.TotalSales * 0.25m; // Assume 25% profit margin

            // Get customer count
            dashboardData.TotalCustomers = await _context.Customers
                .Where(c => c.IsActive)
                .CountAsync();

            // Calculate growth (simplified)
            var previousPeriodStart = start.AddDays(-(end - start).Days);
            var previousPeriodEnd = start.AddDays(-1);

            var previousSales = await _context.Invoices
                .Where(i => i.InvoiceType == InvoiceType.Sales &&
                           i.InvoiceDate >= previousPeriodStart &&
                           i.InvoiceDate <= previousPeriodEnd &&
                           i.Status == InvoiceStatus.Approved)
                .SumAsync(i => i.TotalAmount);

            if (previousSales > 0)
            {
                dashboardData.SalesGrowth = ((dashboardData.TotalSales - previousSales) / previousSales) * 100;
                dashboardData.ProfitGrowth = dashboardData.SalesGrowth; // Simplified
            }

            // Get daily sales for chart
            dashboardData.SalesChart = await GetDailySalesReportAsync(start, end);

            // Get top products
            dashboardData.TopProducts = await GetTopSellingProductsAsync(start, end, 5);

            // Get low stock alerts
            dashboardData.LowStockAlerts = await GetLowStockAlertsAsync();

            // Get recent activities
            dashboardData.RecentActivities = await GetRecentActivitiesAsync(10);

            return dashboardData;
        }

        public async Task<List<DailySalesReport>> GetDailySalesReportAsync(DateTime startDate, DateTime endDate)
        {
            var dailySales = await _context.Invoices
                .Where(i => i.InvoiceType == InvoiceType.Sales &&
                           i.InvoiceDate >= startDate &&
                           i.InvoiceDate <= endDate &&
                           i.Status == InvoiceStatus.Approved)
                .GroupBy(i => i.InvoiceDate.Date)
                .Select(g => new DailySalesReport
                {
                    Date = g.Key,
                    Sales = g.Sum(i => i.TotalAmount - i.TotalTaxAmount),
                    Tax = g.Sum(i => i.TotalTaxAmount),
                    InvoiceCount = g.Count(),
                    CustomerCount = g.Select(i => i.CustomerId).Distinct().Count()
                })
                .OrderBy(d => d.Date)
                .ToListAsync();

            return dailySales;
        }

        public async Task<List<TopSellingProduct>> GetTopSellingProductsAsync(DateTime startDate, DateTime endDate, int count = 10)
        {
            var topProducts = await _context.InvoiceItems
                .Where(ii => ii.Invoice.InvoiceType == InvoiceType.Sales &&
                            ii.Invoice.InvoiceDate >= startDate &&
                            ii.Invoice.InvoiceDate <= endDate &&
                            ii.Invoice.Status == InvoiceStatus.Approved)
                .GroupBy(ii => new { ii.ProductId, ii.ProductCode, ii.ProductName })
                .Select(g => new TopSellingProduct
                {
                    ProductId = g.Key.ProductId ?? 0,
                    ProductCode = g.Key.ProductCode,
                    ProductName = g.Key.ProductName,
                    QuantitySold = g.Sum(ii => ii.Quantity),
                    Revenue = g.Sum(ii => ii.TotalAmount),
                    OrderCount = g.Count()
                })
                .OrderByDescending(p => p.Revenue)
                .Take(count)
                .ToListAsync();

            return topProducts;
        }

        public async Task<List<LowStockAlert>> GetLowStockAlertsAsync()
        {
            var lowStockProducts = await _context.Products
                .Where(p => p.IsActive && 
                           p.StockQuantity <= p.MinStockLevel)
                .Select(p => new LowStockAlert
                {
                    ProductId = p.Id,
                    ProductCode = p.Code,
                    ProductName = p.Name,
                    CurrentStock = p.StockQuantity,
                    MinStockLevel = p.MinStockLevel,
                    ReorderPoint = p.ReorderPoint,
                    Status = p.StockQuantity <= 0 ? "نفد المخزون" : 
                            p.StockQuantity <= p.ReorderPoint ? "يحتاج إعادة طلب" : "مخزون منخفض"
                })
                .OrderBy(p => p.CurrentStock)
                .ToListAsync();

            return lowStockProducts;
        }

        public async Task<List<RecentActivity>> GetRecentActivitiesAsync(int count = 10)
        {
            var recentInvoices = await _context.Invoices
                .OrderByDescending(i => i.CreatedAt)
                .Take(count)
                .Select(i => new RecentActivity
                {
                    Date = i.CreatedAt,
                    Type = "فاتورة",
                    Description = $"فاتورة رقم {i.InvoiceNumber} - {i.Customer.Name}",
                    User = i.CreatedBy,
                    Details = $"{i.TotalAmount:N0} ج.م"
                })
                .ToListAsync();

            return recentInvoices;
        }

        public async Task<List<Alert>> GetSystemAlertsAsync()
        {
            var alerts = new List<Alert>();

            // Low stock alerts
            var lowStockCount = await _context.Products
                .Where(p => p.IsActive && p.StockQuantity <= p.MinStockLevel)
                .CountAsync();

            if (lowStockCount > 0)
            {
                alerts.Add(new Alert
                {
                    Type = "مخزون",
                    Title = "تحذير مخزون منخفض",
                    Message = $"{lowStockCount} منتج يحتاج إعادة تموين",
                    Date = DateTime.Now,
                    Severity = "تحذير",
                    IsRead = false
                });
            }

            // Overdue invoices
            var overdueCount = await _context.Invoices
                .Where(i => i.DueDate < DateTime.Today && 
                           i.PaymentStatus != PaymentStatus.Paid)
                .CountAsync();

            if (overdueCount > 0)
            {
                alerts.Add(new Alert
                {
                    Type = "فواتير",
                    Title = "فواتير متأخرة السداد",
                    Message = $"{overdueCount} فاتورة متأخرة السداد",
                    Date = DateTime.Now,
                    Severity = "خطر",
                    IsRead = false
                });
            }

            return alerts;
        }

        // Placeholder implementations for other methods
        public async Task<SalesReportData> GenerateSalesReportAsync(DateTime startDate, DateTime endDate, int? customerId = null)
        {
            // Implementation would go here
            return new SalesReportData { StartDate = startDate, EndDate = endDate };
        }

        public async Task<List<CustomerSalesReport>> GetCustomerSalesReportAsync(DateTime startDate, DateTime endDate)
        {
            // Implementation would go here
            return new List<CustomerSalesReport>();
        }

        public async Task<List<MonthlySalesReport>> GetMonthlySalesReportAsync(int year)
        {
            // Implementation would go here
            return new List<MonthlySalesReport>();
        }

        public async Task<ProfitLossReport> GenerateProfitLossReportAsync(DateTime startDate, DateTime endDate)
        {
            // Implementation would go here
            return new ProfitLossReport { StartDate = startDate, EndDate = endDate };
        }

        public async Task<CashFlowReport> GenerateCashFlowReportAsync(DateTime startDate, DateTime endDate)
        {
            // Implementation would go here
            return new CashFlowReport { StartDate = startDate, EndDate = endDate };
        }

        public async Task<BalanceSheetReport> GenerateBalanceSheetReportAsync(DateTime asOfDate)
        {
            // Implementation would go here
            return new BalanceSheetReport { AsOfDate = asOfDate };
        }

        public async Task<AccountsReceivableReport> GenerateAccountsReceivableReportAsync(DateTime? asOfDate = null)
        {
            // Implementation would go here
            return new AccountsReceivableReport { AsOfDate = asOfDate ?? DateTime.Today };
        }

        public async Task<AccountsPayableReport> GenerateAccountsPayableReportAsync(DateTime? asOfDate = null)
        {
            // Implementation would go here
            return new AccountsPayableReport { AsOfDate = asOfDate ?? DateTime.Today };
        }

        public async Task<InventoryReportData> GenerateInventoryReportAsync(DateTime? asOfDate = null)
        {
            // Implementation would go here
            return new InventoryReportData { ReportDate = asOfDate ?? DateTime.Today };
        }

        public async Task<InventoryValuationReport> GenerateInventoryValuationReportAsync(DateTime? asOfDate = null)
        {
            // Implementation would go here
            return new InventoryValuationReport { ReportDate = asOfDate ?? DateTime.Today };
        }

        public async Task<StockMovementReport> GenerateStockMovementReportAsync(DateTime startDate, DateTime endDate, int? productId = null)
        {
            // Implementation would go here
            return new StockMovementReport { StartDate = startDate, EndDate = endDate };
        }

        public async Task<TaxReport> GenerateTaxReportAsync(DateTime startDate, DateTime endDate)
        {
            // Implementation would go here
            return new TaxReport { StartDate = startDate, EndDate = endDate };
        }

        public async Task<VatReturnData> GenerateVatReturnAsync(DateTime startDate, DateTime endDate)
        {
            // Implementation would go here
            return new VatReturnData { PeriodStart = startDate, PeriodEnd = endDate };
        }

        public async Task<bool> ExportReportToPdfAsync<T>(T reportData, string templateName, string filePath)
        {
            // Implementation would go here
            return await Task.FromResult(false);
        }

        public async Task<bool> ExportReportToExcelAsync<T>(T reportData, string filePath)
        {
            // Implementation would go here
            return await Task.FromResult(false);
        }

        public async Task<bool> EmailReportAsync<T>(T reportData, string templateName, string toEmail, string subject)
        {
            // Implementation would go here
            return await Task.FromResult(false);
        }
    }
}
