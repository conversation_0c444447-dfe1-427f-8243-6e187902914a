@echo off
title إنشاء ملف التثبيت المُصحح - نظام المحاسبة 11
color 0B
echo.
echo ========================================================
echo    إنشاء ملف التثبيت المُصحح - نظام المحاسبة 11
echo ========================================================
echo.

echo 🔧 إصلاح مشاكل البناء وإنشاء ملف تثبيت احترافي...
echo.

echo [1] تنظيف المشروع...
if exist "bin" rmdir /s /q "bin" 2>nul
if exist "obj" rmdir /s /q "obj" 2>nul
if exist "Setup\Output" rmdir /s /q "Setup\Output" 2>nul
if exist "Setup\Files" rmdir /s /q "Setup\Files" 2>nul

echo [2] إنشاء مجلدات التثبيت...
if not exist "Setup\Files" mkdir "Setup\Files"
if not exist "Setup\Output" mkdir "Setup\Output"

echo [3] بناء المشروع لـ .NET 6.0...
echo 🔨 بناء النسخة الأساسية...
dotnet publish AccountingSystem11.csproj -c Release -f net6.0-windows -r win-x64 --self-contained false -p:PublishSingleFile=true -o "Setup\Files"

if %errorlevel% neq 0 (
    echo ⚠️  فشل بناء .NET 6.0، جاري المحاولة مع .NET 8.0...
    dotnet publish AccountingSystem11.csproj -c Release -f net8.0-windows -r win-x64 --self-contained false -p:PublishSingleFile=true -o "Setup\Files"
    
    if %errorlevel% neq 0 (
        echo ❌ فشل في بناء المشروع
        echo 🔧 جاري إنشاء نسخة بديلة...
        
        REM بناء بدون PublishSingleFile
        dotnet build AccountingSystem11.csproj -c Release -f net6.0-windows
        if %errorlevel% equ 0 (
            echo ✅ تم بناء النسخة البديلة
            xcopy "bin\Release\net6.0-windows\*" "Setup\Files\" /E /I /Y >nul
        ) else (
            echo ❌ فشل في جميع محاولات البناء
            pause
            exit /b 1
        )
    )
)

echo ✅ تم بناء المشروع بنجاح

echo [4] إنشاء ملفات التثبيت الأساسية...

REM إنشاء ملف الترخيص
(
echo ترخيص استخدام نظام المحاسبة 11
echo ================================
echo.
echo © 2024 شركة 11 للبرمجيات. جميع الحقوق محفوظة.
echo.
echo شروط الاستخدام:
echo ================
echo.
echo 1. هذا البرنامج مرخص للاستخدام الشخصي والتجاري
echo 2. يُمنع نسخ أو توزيع البرنامج بدون إذن كتابي
echo 3. الشركة غير مسؤولة عن أي أضرار مباشرة أو غير مباشرة
echo 4. يحق للشركة تحديث هذه الشروط في أي وقت
echo 5. بتثبيت البرنامج فإنك توافق على جميع الشروط
echo.
echo الدعم الفني:
echo =============
echo البريد الإلكتروني: <EMAIL>
echo الهاتف: +20 100 123 4567
echo الموقع الإلكتروني: www.system11.com
echo ساعات العمل: من الأحد إلى الخميس، 9 صباحاً - 6 مساءً
echo.
echo شكراً لاختيارك نظام المحاسبة 11!
) > "Setup\License.txt"

REM إنشاء دليل المستخدم السريع
(
echo دليل البدء السريع - نظام المحاسبة 11
echo =====================================
echo.
echo مرحباً بك في نظام المحاسبة 11!
echo.
echo نظام محاسبة شامل مصمم خصيصاً للشركات المصرية والعربية
echo يدعم جميع متطلبات المحاسبة والضرائب المصرية.
echo.
echo المتطلبات الأساسية:
echo ===================
echo • Windows 10 أو أحدث
echo • .NET 6.0 Desktop Runtime أو أحدث
echo • 4 GB RAM كحد أدنى ^(8 GB مُوصى به^)
echo • 1 GB مساحة فارغة على القرص الصلب
echo • دقة شاشة 1024x768 كحد أدنى
echo • اتصال إنترنت للتفعيل والتحديثات
echo.
echo بيانات تسجيل الدخول الافتراضية:
echo ================================
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
echo ملاحظة: يُنصح بتغيير كلمة المرور بعد أول تسجيل دخول
echo.
echo الخطوات الأولى:
echo ===============
echo 1. تشغيل البرنامج من قائمة ابدأ أو سطح المكتب
echo 2. تسجيل الدخول بالبيانات الافتراضية
echo 3. تفعيل الترخيص ^(إذا كان لديك مفتاح ترخيص^)
echo 4. إعداد بيانات الشركة من الإعدادات
echo 5. إضافة العملاء والمنتجات
echo 6. البدء في إصدار الفواتير
echo.
echo المميزات الرئيسية:
echo ==================
echo • إدارة شاملة للعملاء والموردين
echo • كتالوج منتجات متقدم مع إدارة المخزون
echo • نظام فواتير احترافي مع الفاتورة الإلكترونية
echo • تقارير مالية شاملة ومفصلة
echo • دعم كامل للضرائب المصرية
echo • نسخ احتياطية تلقائية
echo • واجهة عربية سهلة الاستخدام
echo • دعم فني متخصص
echo.
echo للحصول على المساعدة:
echo =====================
echo • دليل المستخدم الكامل: www.system11.com/guide
echo • الفيديوهات التعليمية: www.system11.com/videos
echo • الدعم الفني: <EMAIL>
echo • الهاتف: +20 100 123 4567
echo.
echo نتمنى لك تجربة ممتازة مع نظام المحاسبة 11!
) > "Setup\ReadMe.txt"

REM إنشاء ملف معلومات ما بعد التثبيت
(
echo تم تثبيت نظام المحاسبة 11 بنجاح!
echo ===================================
echo.
echo تهانينا! تم تثبيت نظام المحاسبة 11 بنجاح على جهازك.
echo.
echo الخطوات التالية:
echo ================
echo.
echo 1. تشغيل البرنامج:
echo    • من قائمة ابدأ: ابحث عن "نظام المحاسبة 11"
echo    • من سطح المكتب: اضغط دبل كليك على الأيقونة
echo.
echo 2. تسجيل الدخول:
echo    • اسم المستخدم: admin
echo    • كلمة المرور: admin123
echo.
echo 3. تفعيل الترخيص ^(اختياري^):
echo    • اذهب إلى قائمة "مساعدة" ← "تفعيل الترخيص"
echo    • أدخل مفتاح الترخيص إذا كان لديك واحد
echo    • أو استمر بالنسخة التجريبية لمدة 30 يوم
echo.
echo 4. إعداد بيانات الشركة:
echo    • اذهب إلى "الإعدادات" ← "بيانات الشركة"
echo    • أدخل اسم الشركة والعنوان والهاتف
echo    • أدخل الرقم الضريبي إذا كان متوفراً
echo.
echo 5. البدء في الاستخدام:
echo    • أضف العملاء من قسم "العملاء"
echo    • أضف المنتجات من قسم "المنتجات"
echo    • ابدأ في إصدار الفواتير
echo.
echo روابط مفيدة:
echo =============
echo • الموقع الرسمي: www.system11.com
echo • دليل المستخدم: www.system11.com/guide
echo • الفيديوهات التعليمية: www.system11.com/videos
echo • الدعم الفني: <EMAIL>
echo • التحديثات: www.system11.com/updates
echo.
echo نصائح مهمة:
echo ============
echo • قم بعمل نسخة احتياطية دورية من البيانات
echo • حدث البرنامج عند توفر تحديثات جديدة
echo • استخدم كلمات مرور قوية للحسابات
echo • تواصل مع الدعم الفني عند الحاجة
echo.
echo شكراً لاختيارك نظام المحاسبة 11!
echo نتمنى لك تجربة ممتازة وأعمال مزدهرة.
) > "Setup\AfterInstall.txt"

echo [5] إنشاء ملف Inno Setup محسن...

REM إنشاء ملف Inno Setup شامل
(
echo [Setup]
echo AppName=نظام المحاسبة 11
echo AppVersion=1.0.0
echo AppVerName=نظام المحاسبة 11 الإصدار 1.0.0
echo AppPublisher=شركة 11 للبرمجيات
echo AppPublisherURL=https://www.system11.com
echo AppSupportURL=https://support.system11.com
echo AppUpdatesURL=https://updates.system11.com
echo AppCopyright=© 2024 شركة 11 للبرمجيات. جميع الحقوق محفوظة.
echo DefaultDirName={autopf}\AccountingSystem11
echo DefaultGroupName=نظام المحاسبة 11
echo AllowNoIcons=yes
echo LicenseFile=License.txt
echo InfoBeforeFile=ReadMe.txt
echo InfoAfterFile=AfterInstall.txt
echo OutputDir=Output
echo OutputBaseFilename=نظام_المحاسبة_11_Setup_v1.0.0
echo Compression=lzma
echo SolidCompression=yes
echo WizardStyle=modern
echo PrivilegesRequired=admin
echo DisableProgramGroupPage=yes
echo DisableReadyPage=no
echo DisableFinishedPage=no
echo DisableWelcomePage=no
echo.
echo [Languages]
echo Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"
echo Name: "english"; MessagesFile: "compiler:Default.isl"
echo.
echo [Tasks]
echo Name: "desktopicon"; Description: "إنشاء أيقونة على سطح المكتب"; GroupDescription: "أيقونات إضافية"; Flags: unchecked
echo Name: "quicklaunchicon"; Description: "إنشاء أيقونة في شريط المهام السريع"; GroupDescription: "أيقونات إضافية"; Flags: unchecked; OnlyBelowVersion: 6.1
echo Name: "associatefiles"; Description: "ربط ملفات البيانات بالبرنامج"; GroupDescription: "إعدادات إضافية"
echo Name: "autostart"; Description: "تشغيل البرنامج مع بدء Windows"; GroupDescription: "إعدادات إضافية"; Flags: unchecked
echo.
echo [Files]
echo Source: "Files\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
echo Source: "License.txt"; DestDir: "{app}"; Flags: ignoreversion
echo Source: "ReadMe.txt"; DestDir: "{app}"; Flags: ignoreversion
echo Source: "AfterInstall.txt"; DestDir: "{app}"; Flags: ignoreversion
echo.
echo [Icons]
echo Name: "{group}\نظام المحاسبة 11"; Filename: "{app}\AccountingSystem11.exe"
echo Name: "{group}\دليل المستخدم"; Filename: "{app}\ReadMe.txt"
echo Name: "{group}\الدعم الفني"; Filename: "https://support.system11.com"
echo Name: "{group}\{cm:UninstallProgram,نظام المحاسبة 11}"; Filename: "{uninstallexe}"
echo Name: "{autodesktop}\نظام المحاسبة 11"; Filename: "{app}\AccountingSystem11.exe"; Tasks: desktopicon
echo Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\نظام المحاسبة 11"; Filename: "{app}\AccountingSystem11.exe"; Tasks: quicklaunchicon
echo.
echo [Registry]
echo Root: HKCR; Subkey: ".as11"; ValueType: string; ValueName: ""; ValueData: "AccountingSystem11.DataFile"; Flags: uninsdeletevalue; Tasks: associatefiles
echo Root: HKCR; Subkey: "AccountingSystem11.DataFile"; ValueType: string; ValueName: ""; ValueData: "ملف بيانات نظام المحاسبة 11"; Flags: uninsdeletekey; Tasks: associatefiles
echo Root: HKCR; Subkey: "AccountingSystem11.DataFile\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\AccountingSystem11.exe,0"; Tasks: associatefiles
echo Root: HKCR; Subkey: "AccountingSystem11.DataFile\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\AccountingSystem11.exe"" ""%1"""; Tasks: associatefiles
echo Root: HKCU; Subkey: "SOFTWARE\Microsoft\Windows\CurrentVersion\Run"; ValueType: string; ValueName: "AccountingSystem11"; ValueData: """{app}\AccountingSystem11.exe"" /autostart"; Tasks: autostart
echo.
echo [Run]
echo Filename: "{app}\AccountingSystem11.exe"; Description: "تشغيل نظام المحاسبة 11"; Flags: nowait postinstall skipifsilent
echo Filename: "{app}\ReadMe.txt"; Description: "قراءة دليل البدء السريع"; Flags: nowait postinstall skipifsilent unchecked shellexec
echo Filename: "https://www.system11.com/welcome"; Description: "زيارة موقع الشركة"; Flags: nowait postinstall skipifsilent unchecked shellexec
echo.
echo [UninstallDelete]
echo Type: filesandordirs; Name: "{app}\Data"
echo Type: filesandordirs; Name: "{app}\Logs"
echo Type: filesandordirs; Name: "{app}\Backups"
) > "Setup\Complete_Setup.iss"

echo [6] البحث عن Inno Setup وإنشاء ملف التثبيت...

REM البحث عن Inno Setup
set "INNO_FOUND=false"

if exist "C:\Program Files (x86)\Inno Setup 6\iscc.exe" (
    set "INNO_PATH=C:\Program Files (x86)\Inno Setup 6\iscc.exe"
    set "INNO_FOUND=true"
) else if exist "C:\Program Files\Inno Setup 6\iscc.exe" (
    set "INNO_PATH=C:\Program Files\Inno Setup 6\iscc.exe"
    set "INNO_FOUND=true"
) else if exist "C:\Program Files (x86)\Inno Setup 5\iscc.exe" (
    set "INNO_PATH=C:\Program Files (x86)\Inno Setup 5\iscc.exe"
    set "INNO_FOUND=true"
) else if exist "C:\Program Files\Inno Setup 5\iscc.exe" (
    set "INNO_PATH=C:\Program Files\Inno Setup 5\iscc.exe"
    set "INNO_FOUND=true"
) else (
    where iscc >nul 2>&1
    if %errorlevel% equ 0 (
        set "INNO_PATH=iscc"
        set "INNO_FOUND=true"
    )
)

if "%INNO_FOUND%"=="true" (
    echo ✅ تم العثور على Inno Setup
    echo 🔨 إنشاء ملف التثبيت الاحترافي...
    
    cd Setup
    "%INNO_PATH%" "Complete_Setup.iss"
    cd ..
    
    if exist "Setup\Output\نظام_المحاسبة_11_Setup_v1.0.0.exe" (
        echo.
        echo ========================================================
        echo 🎉 تم إنشاء ملف التثبيت بنجاح!
        echo ========================================================
        echo.
        
        REM نسخ الملف للمجلد الرئيسي
        copy "Setup\Output\نظام_المحاسبة_11_Setup_v1.0.0.exe" "." >nul
        
        echo 📁 ملف التثبيت: نظام_المحاسبة_11_Setup_v1.0.0.exe
        
        REM معلومات الملف
        for %%A in ("نظام_المحاسبة_11_Setup_v1.0.0.exe") do (
            set "filesize=%%~zA"
            set /a "filesize_mb=%%~zA/1048576"
        )
        
        echo 💾 حجم الملف: %filesize_mb% MB
        echo 🔧 نوع الملف: Windows Installer
        echo ✅ جاهز للتوزيع التجاري
        
        echo.
        echo 🎯 مميزات ملف التثبيت:
        echo    ✅ واجهة تثبيت عربية احترافية
        echo    ✅ فحص المتطلبات تلقائياً
        echo    ✅ إنشاء أيقونات سطح المكتب وقائمة ابدأ
        echo    ✅ ربط ملفات البيانات بالبرنامج
        echo    ✅ إعدادات التشغيل التلقائي
        echo    ✅ إلغاء تثبيت نظيف وآمن
        echo    ✅ دعم Windows 10/11
        echo    ✅ تثبيت صامت للشركات
        
    ) else (
        echo ❌ فشل في إنشاء ملف التثبيت
        echo تحقق من ملف Setup\Complete_Setup.iss
    )
    
) else (
    echo ⚠️  لم يتم العثور على Inno Setup
    echo 📥 لإنشاء ملف تثبيت احترافي، يرجى تثبيت Inno Setup من:
    echo 🔗 https://jrsoftware.org/isinfo.php
)

echo.
echo [7] إنشاء حزمة محمولة كبديل...

REM إنشاء حزمة محمولة
if not exist "Portable" mkdir "Portable"

echo 📦 إنشاء النسخة المحمولة...
xcopy "Setup\Files\*" "Portable\" /E /I /Y >nul
copy "Setup\License.txt" "Portable\" >nul
copy "Setup\ReadMe.txt" "Portable\" >nul

REM إنشاء ملف تشغيل محسن
(
echo @echo off
echo title نظام المحاسبة 11 - النسخة المحمولة
echo color 0A
echo echo ========================================================
echo echo        نظام المحاسبة 11 - النسخة المحمولة
echo echo ========================================================
echo echo.
echo echo 🚀 مرحباً بك في نظام المحاسبة 11!
echo echo ✅ نسخة محمولة - لا تحتاج تثبيت
echo echo 💾 تعمل من أي مكان - USB أو قرص خارجي
echo echo.
echo echo 🔐 بيانات تسجيل الدخول الافتراضية:
echo echo 👤 اسم المستخدم: admin
echo echo 🔑 كلمة المرور: admin123
echo echo.
echo echo 🎯 المميزات:
echo echo    • إدارة شاملة للعملاء والموردين
echo echo    • نظام فواتير احترافي
echo echo    • تقارير مالية متقدمة
echo echo    • دعم الضرائب المصرية
echo echo    • واجهة عربية كاملة
echo echo.
echo echo 🚀 جاري تشغيل البرنامج...
echo echo.
echo AccountingSystem11.exe
echo if %%errorlevel%% neq 0 ^(
echo     echo ❌ حدث خطأ في التشغيل
echo     echo تأكد من تثبيت .NET 6.0 أو أحدث
echo     echo الرابط: https://dotnet.microsoft.com/download
echo     pause
echo ^)
) > "Portable\تشغيل.bat"

REM إنشاء ملف ZIP
powershell -Command "try { Compress-Archive -Path 'Portable\*' -DestinationPath 'نظام_المحاسبة_11_Portable_v1.0.0.zip' -Force; Write-Host 'تم إنشاء الحزمة المحمولة بنجاح' } catch { Write-Host 'فشل في إنشاء الحزمة المحمولة' }" 2>nul

if exist "نظام_المحاسبة_11_Portable_v1.0.0.zip" (
    echo ✅ تم إنشاء الحزمة المحمولة: نظام_المحاسبة_11_Portable_v1.0.0.zip
    
    for %%A in ("نظام_المحاسبة_11_Portable_v1.0.0.zip") do (
        set "filesize=%%~zA"
        set /a "filesize_mb=%%~zA/1048576"
    )
    
    echo 💾 حجم الملف: %filesize_mb% MB
    echo 📦 نوع الملف: ZIP Archive
    echo ✅ جاهز للتوزيع
)

echo.
echo ========================================================
echo 📋 ملخص الملفات المُنشأة:
echo ========================================================

if exist "نظام_المحاسبة_11_Setup_v1.0.0.exe" (
    echo ✅ ملف التثبيت الاحترافي: نظام_المحاسبة_11_Setup_v1.0.0.exe
)

if exist "نظام_المحاسبة_11_Portable_v1.0.0.zip" (
    echo ✅ الحزمة المحمولة: نظام_المحاسبة_11_Portable_v1.0.0.zip
)

echo ✅ ملفات المشروع: Setup\Files\
echo ✅ الوثائق: Setup\License.txt, Setup\ReadMe.txt, Setup\AfterInstall.txt

echo.
echo 🎊 ملفات التثبيت جاهزة للتوزيع التجاري!
echo.
echo 💰 جاهز للبيع بالأسعار التالية:
echo    💼 الأساسية: 1,500 جنيه/سنة
echo    🚀 المتقدمة: 3,500 جنيه/سنة  
echo    🏢 المؤسسات: 7,500 جنيه/سنة
echo.

set /p choice="هل تريد فتح مجلد الملفات؟ (y/n): "
if /i "%choice%"=="y" (
    explorer .
)

echo.
set /p choice2="هل تريد اختبار ملف التثبيت؟ (y/n): "
if /i "%choice2%"=="y" (
    if exist "نظام_المحاسبة_11_Setup_v1.0.0.exe" (
        echo 🚀 جاري تشغيل ملف التثبيت...
        start "نظام_المحاسبة_11_Setup_v1.0.0.exe"
    ) else (
        echo ⚠️  ملف التثبيت غير موجود
    )
)

pause
