#include "resource.h"
#include <windows.h>

// Main Menu
IDR_MAINMENU MENU
BEGIN
    POPUP "&ملف"
    BEGIN
        MENUITEM "&جديد\tCtrl+N",                ID_FILE_NEW
        MENUITEM "&فتح\tCtrl+O",                ID_FILE_O<PERSON>EN
        MENUITEM SEPARATOR
        MENUITEM "&حفظ\tCtrl+S",                ID_FILE_SAVE
        MENUITEM "حفظ &باسم...",                ID_FILE_SAVEAS
        MENUITEM SEPARATOR
        MENUITEM "&خروج",                       ID_FILE_EXIT
    END
    POPUP "&عرض"
    BEGIN
        MENUITEM "&لوحة التحكم",                ID_VIEW_DASHBOARD
        MENUITEM SEPARATOR
        MENUITEM "&العملاء",                    ID_VIEW_CUSTOMERS
        MENUITEM "&المنتجات",                   ID_VIEW_PRODUCTS
        MENUITEM "&الفواتير",                   ID_VIEW_INVOICES
        MENUITEM SEPARATOR
        MENUITEM "&التقارير",                   ID_VIEW_REPORTS
        MENUITEM "&الإعدادات",                  ID_VIEW_SETTINGS
    END
    POPUP "&مساعدة"
    BEGIN
        MENUITEM "&حول البرنامج...",             ID_HELP_ABOUT
    END
END

// About Dialog
IDD_ABOUT DIALOGEX 0, 0, 300, 200
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "حول نظام المحاسبة 11"
FONT 12, "Segoe UI"
BEGIN
    DEFPUSHBUTTON   "موافق",IDOK,120,170,50,14
    CTEXT           "نظام المحاسبة 11",IDC_STATIC_TITLE,50,20,200,20,SS_CENTER
    CTEXT           "إصدار C++ - Native",IDC_STATIC,50,45,200,15,SS_CENTER
    CTEXT           "نظام محاسبة شامل باللغة العربية",IDC_STATIC,50,65,200,15,SS_CENTER
    CTEXT           "يدعم جميع إصدارات Windows",IDC_STATIC,50,85,200,15,SS_CENTER
    CTEXT           "بدون الحاجة لـ .NET Framework",IDC_STATIC,50,105,200,15,SS_CENTER
    CTEXT           "© 2024 شركة 11 للبرمجيات",IDC_STATIC,50,140,200,15,SS_CENTER
END

// Customer Dialog
IDD_CUSTOMER DIALOGEX 0, 0, 400, 350
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "إضافة/تعديل عميل"
FONT 12, "Segoe UI"
BEGIN
    LTEXT           "الاسم:",IDC_STATIC,20,20,50,15
    EDITTEXT        IDC_EDIT_NAME,80,18,250,15,ES_AUTOHSCROLL
    
    LTEXT           "الشركة:",IDC_STATIC,20,45,50,15
    EDITTEXT        IDC_EDIT_COMPANY,80,43,250,15,ES_AUTOHSCROLL
    
    LTEXT           "الهاتف:",IDC_STATIC,20,70,50,15
    EDITTEXT        IDC_EDIT_PHONE,80,68,120,15,ES_AUTOHSCROLL
    
    LTEXT           "الموبايل:",IDC_STATIC,220,70,50,15
    EDITTEXT        IDC_EDIT_MOBILE,280,68,120,15,ES_AUTOHSCROLL
    
    LTEXT           "البريد الإلكتروني:",IDC_STATIC,20,95,80,15
    EDITTEXT        IDC_EDIT_EMAIL,110,93,220,15,ES_AUTOHSCROLL
    
    LTEXT           "العنوان:",IDC_STATIC,20,120,50,15
    EDITTEXT        IDC_EDIT_ADDRESS,80,118,250,40,ES_MULTILINE | ES_AUTOVSCROLL | WS_VSCROLL
    
    LTEXT           "المدينة:",IDC_STATIC,20,170,50,15
    EDITTEXT        IDC_EDIT_CITY,80,168,120,15,ES_AUTOHSCROLL
    
    LTEXT           "الرقم الضريبي:",IDC_STATIC,220,170,70,15
    EDITTEXT        IDC_EDIT_TAX_NUMBER,300,168,100,15,ES_AUTOHSCROLL
    
    LTEXT           "حد الائتمان:",IDC_STATIC,20,200,60,15
    EDITTEXT        IDC_EDIT_CREDIT_LIMIT,90,198,100,15,ES_AUTOHSCROLL | ES_NUMBER
    
    DEFPUSHBUTTON   "حفظ",IDC_BUTTON_SAVE,150,300,50,20
    PUSHBUTTON      "إلغاء",IDC_BUTTON_CANCEL,220,300,50,20
END

// Product Dialog
IDD_PRODUCT DIALOGEX 0, 0, 400, 400
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "إضافة/تعديل منتج"
FONT 12, "Segoe UI"
BEGIN
    LTEXT           "كود المنتج:",IDC_STATIC,20,20,60,15
    EDITTEXT        IDC_EDIT_NAME,90,18,100,15,ES_AUTOHSCROLL
    
    LTEXT           "اسم المنتج:",IDC_STATIC,210,20,60,15
    EDITTEXT        IDC_EDIT_COMPANY,280,18,100,15,ES_AUTOHSCROLL
    
    DEFPUSHBUTTON   "حفظ",IDC_BUTTON_SAVE,150,350,50,20
    PUSHBUTTON      "إلغاء",IDC_BUTTON_CANCEL,220,350,50,20
END

// Invoice Dialog
IDD_INVOICE DIALOGEX 0, 0, 600, 500
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "إنشاء/تعديل فاتورة"
FONT 12, "Segoe UI"
BEGIN
    DEFPUSHBUTTON   "حفظ",IDC_BUTTON_SAVE,250,450,50,20
    PUSHBUTTON      "إلغاء",IDC_BUTTON_CANCEL,320,450,50,20
END

// Version Information
VS_VERSION_INFO VERSIONINFO
FILEVERSION     1,0,0,0
PRODUCTVERSION  1,0,0,0
FILEFLAGSMASK   VS_FFI_FILEFLAGSMASK
FILEFLAGS       0x0L
FILEOS          VOS_NT_WINDOWS32
FILETYPE        VFT_APP
FILESUBTYPE     VFT2_UNKNOWN
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", "شركة 11 للبرمجيات"
            VALUE "FileDescription", "نظام المحاسبة 11 - إصدار C++"
            VALUE "FileVersion", "*******"
            VALUE "InternalName", "AccountingSystem11"
            VALUE "LegalCopyright", "© 2024 شركة 11 للبرمجيات. جميع الحقوق محفوظة."
            VALUE "OriginalFilename", "AccountingSystem11.exe"
            VALUE "ProductName", "نظام المحاسبة 11"
            VALUE "ProductVersion", "*******"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END
