using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AccountingSystem11.Models
{
    /// <summary>
    /// Product entity representing items and services
    /// </summary>
    public class Product : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string Code { get; set; }

        [Required]
        [MaxLength(200)]
        public string Name { get; set; }

        [MaxLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// Product category
        /// </summary>
        public int? CategoryId { get; set; }
        public virtual ProductCategory Category { get; set; }

        /// <summary>
        /// Unit of measurement (piece, kg, meter, etc.)
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string Unit { get; set; } = "قطعة";

        /// <summary>
        /// Purchase price
        /// </summary>
        public decimal PurchasePrice { get; set; } = 0;

        /// <summary>
        /// Selling price
        /// </summary>
        public decimal SellingPrice { get; set; } = 0;

        /// <summary>
        /// Minimum selling price
        /// </summary>
        public decimal MinSellingPrice { get; set; } = 0;

        /// <summary>
        /// Wholesale price
        /// </summary>
        public decimal WholesalePrice { get; set; } = 0;

        /// <summary>
        /// Current stock quantity
        /// </summary>
        public decimal StockQuantity { get; set; } = 0;

        /// <summary>
        /// Minimum stock level for alerts
        /// </summary>
        public decimal MinStockLevel { get; set; } = 0;

        /// <summary>
        /// Maximum stock level
        /// </summary>
        public decimal MaxStockLevel { get; set; } = 0;

        /// <summary>
        /// Reorder point
        /// </summary>
        public decimal ReorderPoint { get; set; } = 0;

        /// <summary>
        /// Barcode
        /// </summary>
        [MaxLength(50)]
        public string Barcode { get; set; }

        /// <summary>
        /// Product image path
        /// </summary>
        [MaxLength(200)]
        public string ImagePath { get; set; }

        /// <summary>
        /// Tax rate for this product (if different from default)
        /// </summary>
        public decimal? TaxRate { get; set; }

        /// <summary>
        /// Whether this product is taxable
        /// </summary>
        public bool IsTaxable { get; set; } = true;

        /// <summary>
        /// Whether this product is a service
        /// </summary>
        public bool IsService { get; set; } = false;

        /// <summary>
        /// Whether this product is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Product location in warehouse
        /// </summary>
        [MaxLength(50)]
        public string Location { get; set; }

        /// <summary>
        /// Supplier information
        /// </summary>
        public int? SupplierId { get; set; }
        public virtual Supplier Supplier { get; set; }

        /// <summary>
        /// Notes about the product
        /// </summary>
        [MaxLength(500)]
        public string Notes { get; set; }

        // Navigation properties
        public virtual ICollection<InvoiceItem> InvoiceItems { get; set; } = new List<InvoiceItem>();
        public virtual ICollection<InventoryTransaction> InventoryTransactions { get; set; } = new List<InventoryTransaction>();
    }
}
