<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFrameworks>net6.0-windows;net8.0-windows</TargetFrameworks>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>icon.ico</ApplicationIcon>
    <AssemblyTitle>نظام المحاسبة 11</AssemblyTitle>
    <AssemblyDescription>نظام محاسبة متكامل يدعم الضرائب المصرية والفاتورة الإلكترونية - يدعم جميع إصدارات .NET</AssemblyDescription>
    <AssemblyCompany>شركة 11 للبرمجيات</AssemblyCompany>
    <AssemblyProduct>نظام المحاسبة 11</AssemblyProduct>
    <AssemblyCopyright>© 2024 شركة 11 للبرمجيات</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <StartupObject>AccountingSystem11.App</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <!-- Entity Framework - إصدار متوافق مع .NET 6+ -->
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="7.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.14" />

    <!-- Excel & JSON Support -->
    <PackageReference Include="EPPlus" Version="7.0.4" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />

    <!-- Database Support -->
    <PackageReference Include="System.Data.SqlClient" Version="4.8.5" />
    <PackageReference Include="System.Data.OleDb" Version="7.0.0" />

    <!-- UI Components -->
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    <PackageReference Include="LiveCharts.Wpf" Version="0.9.7" />

    <!-- Utilities -->
    <PackageReference Include="QRCoder" Version="1.4.3" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Models\" />
    <Folder Include="Views\" />
    <Folder Include="ViewModels\" />
    <Folder Include="Services\" />
    <Folder Include="Data\" />
    <Folder Include="Helpers\" />
    <Folder Include="Resources\" />
    <Folder Include="Reports\" />
  </ItemGroup>

</Project>
