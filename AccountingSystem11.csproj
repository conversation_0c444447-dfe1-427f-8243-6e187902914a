<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFrameworks>net48;net6.0-windows;net7.0-windows;net8.0-windows</TargetFrameworks>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>icon.ico</ApplicationIcon>
    <AssemblyTitle>نظام المحاسبة 11</AssemblyTitle>
    <AssemblyDescription>نظام محاسبة متكامل يدعم الضرائب المصرية والفاتورة الإلكترونية - يدعم جميع إصدارات .NET</AssemblyDescription>
    <AssemblyCompany>شركة 11 للبرمجيات</AssemblyCompany>
    <AssemblyProduct>نظام المحاسبة 11</AssemblyProduct>
    <AssemblyCopyright>© 2024 شركة 11 للبرمجيات</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <StartupObject>AccountingSystem11.App</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <!-- Entity Framework - متوافق مع جميع إصدارات .NET -->
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0" />

    <!-- Excel & JSON Support -->
    <PackageReference Include="EPPlus" Version="7.0.4" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />

    <!-- Database Support -->
    <PackageReference Include="System.Data.SqlClient" Version="4.8.5" />
    <PackageReference Include="System.Data.OleDb" Version="8.0.0" />

    <!-- UI Components -->
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    <PackageReference Include="LiveCharts.Wpf" Version="0.9.7" />

    <!-- Utilities -->
    <PackageReference Include="QRCoder" Version="1.4.3" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
  </ItemGroup>

  <!-- إعدادات خاصة بـ .NET Framework 4.8 -->
  <ItemGroup Condition="'$(TargetFramework)' == 'net48'">
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Windows.Forms" />
    <!-- استخدام إصدارات أقدم متوافقة مع .NET Framework -->
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.25" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.25" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.25" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.25" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Models\" />
    <Folder Include="Views\" />
    <Folder Include="ViewModels\" />
    <Folder Include="Services\" />
    <Folder Include="Data\" />
    <Folder Include="Helpers\" />
    <Folder Include="Resources\" />
    <Folder Include="Reports\" />
  </ItemGroup>

</Project>
