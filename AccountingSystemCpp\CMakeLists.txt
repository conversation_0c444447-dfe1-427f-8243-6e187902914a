cmake_minimum_required(VERSION 3.16)
project(AccountingSystem11 VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set Windows-specific settings
if(WIN32)
    set(CMAKE_WIN32_EXECUTABLE TRUE)
    add_definitions(-DUNICODE -D_UNICODE)
endif()

# Find SQLite3
find_package(PkgConfig REQUIRED)
pkg_check_modules(SQLITE3 REQUIRED sqlite3)

# Include directories
include_directories(${SQLITE3_INCLUDE_DIRS})

# Source files
set(SOURCES
    main.cpp
    database.cpp
    AccountingSystem.rc
)

# Header files
set(HEADERS
    database.h
    resource.h
)

# Create executable
add_executable(${PROJECT_NAME} WIN32 ${SOURCES} ${HEADERS})

# Link libraries
target_link_libraries(${PROJECT_NAME} 
    ${SQLITE3_LIBRARIES}
    comctl32
    shell32
    user32
    gdi32
    kernel32
    ole32
    oleaut32
)

# Compiler-specific options
if(MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE /W4)
    # Enable Unicode
    target_compile_definitions(${PROJECT_NAME} PRIVATE UNICODE _UNICODE)
    # Set subsystem to Windows
    set_target_properties(${PROJECT_NAME} PROPERTIES
        LINK_FLAGS "/SUBSYSTEM:WINDOWS"
    )
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "GNU" OR CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    target_compile_options(${PROJECT_NAME} PRIVATE -Wall -Wextra -pedantic)
    # Link with static libraries for standalone executable
    set_target_properties(${PROJECT_NAME} PROPERTIES
        LINK_FLAGS "-static-libgcc -static-libstdc++"
    )
endif()

# Set output directory
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Copy SQLite3 DLL if needed (for Windows)
if(WIN32)
    # Try to find sqlite3.dll
    find_file(SQLITE3_DLL sqlite3.dll PATHS ${SQLITE3_LIBRARY_DIRS})
    if(SQLITE3_DLL)
        add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_if_different
            ${SQLITE3_DLL}
            $<TARGET_FILE_DIR:${PROJECT_NAME}>
        )
    endif()
endif()

# Installation
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)

# CPack configuration for creating installer
set(CPACK_PACKAGE_NAME "AccountingSystem11")
set(CPACK_PACKAGE_VENDOR "شركة 11 للبرمجيات")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "نظام محاسبة شامل باللغة العربية")
set(CPACK_PACKAGE_VERSION_MAJOR ${PROJECT_VERSION_MAJOR})
set(CPACK_PACKAGE_VERSION_MINOR ${PROJECT_VERSION_MINOR})
set(CPACK_PACKAGE_VERSION_PATCH ${PROJECT_VERSION_PATCH})
set(CPACK_PACKAGE_INSTALL_DIRECTORY "AccountingSystem11")

if(WIN32)
    set(CPACK_GENERATOR "NSIS")
    set(CPACK_NSIS_DISPLAY_NAME "نظام المحاسبة 11")
    set(CPACK_NSIS_PACKAGE_NAME "نظام المحاسبة 11")
    set(CPACK_NSIS_HELP_LINK "https://system11.com")
    set(CPACK_NSIS_URL_INFO_ABOUT "https://system11.com")
    set(CPACK_NSIS_CONTACT "<EMAIL>")
    set(CPACK_NSIS_MODIFY_PATH ON)
endif()

include(CPack)
